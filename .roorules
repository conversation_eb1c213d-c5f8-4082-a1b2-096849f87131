 
1. I prefer typescript over javascript
2. code_reuse: prefer to reuse existing code instead of writing it from scratch.
3. security: prioritize secure coding practices.
4. simplicity: aim for simple, clear solutions, avoid over-engineering.
5. code_quality: prioritize clean, readable, and maintainable code.
6. algorithm_efficiency: use the most efficient algorithms and data structures.
7. error_handling: implement robust error handling and logging.
8. comments: add clear, concise comments explaining code blocks and logic when necessary.
9. changes: make small, incremental changes; avoid large refactors.
10. avoid: do not change working code unless explicitly asked.
11. changes: when changing code, do it step by step, verify the changes first.
12. clarification: if unsure, ask for clarification before generating code.
13. avoid: do not overwrite manual code changes, unless explicitly asked.
14. documentation: check project documentation if asked, and use it in your response.
15. reasoning: reason step by step before generating code or sending a response.
16. cost_optimization: be cost conscious, only send requests if necessary, and avoid ai-powered debugging, refactoring or test generation unless necessary, batch changes when possible.
17. debugging: make small incremental changes to try to fix bugs, check terminal output for information.
18. prompt_efficiency: use precise and specific prompts; avoid ambiguity, do not repeat previous instructions; reuse context.
19. local_processing: perform simple tasks manually; avoid using AI unnecessarily.
20. user_guidance: always follow the instructions that are given and prioritize user instructions over global rules.
21. simplicity: avoid over-engineering and aim for the simplest solution.
22. When fixing errors please briefly explain why and how so I can learn.
23. No React/Ts file should be more than 300-400 lines of code, logically break into components.
24. npx tsc --noEmit is an acceptable test
25. If something isn't totally clear feel free to ask me or do your own research if possible, don't assume.