import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

/**
 * MIDDLEWARE ARCHITECTURE OVERVIEW:
 * 
 * This is the main Next.js middleware that runs on EVERY request before routes are matched.
 * It handles global concerns like:
 * 1. Authentication via Clerk
 * 2. Bot detection
 * 3. Public route exemptions
 * 4. Admin route protection
 * 
 * The specialized middleware in the /middleware folder is NOT automatically run.
 * Those middleware functions are imported and used within specific API routes or
 * page handlers as needed, providing more targeted functionality.
 */

const isOnLockDownList = createRouteMatcher([
  "/cr(.*)",
  "/submit(.*)",
]);
const isAdminRoute = createRouteMatcher(["/admin(.*)", "/api/admin/(.*)"]);
const isRootPath = (req: Request) => new URL(req.url).pathname === "/";

// Enhanced function to detect bots and social media crawlers
const isBot = (req: Request) => {
  const userAgent = req.headers.get("user-agent") || "";
  const lowerUserAgent = userAgent.toLowerCase();

  // Comprehensive list of social media crawlers and bots
  const botPatterns = [
    // Search engines
    "googlebot",
    "bingbot",
    "slurp",
    "duckduckbot",
    "yandex",
    "baiduspider",
    // Social media
    "facebookexternalhit",
    "twitterbot",
    "linkedinbot",
    "whatsapp",
    "slackbot",
    "discordbot",
    "telegrambot",
    "viber",
    "pinterest",
    // Generic bot identifiers
    "bot",
    "crawler",
    "spider"
  ];

  return botPatterns.some((pattern) => lowerUserAgent.includes(pattern));
};

/**
 * Determines if a route should be publicly accessible without authentication
 * This runs as part of the main middleware flow
 */
function isPublicApiRoute(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // Check for all debug endpoints
  if (path.startsWith('/api/debug-') || path.startsWith('/api/test-')) {
    return true;
  }

  // Check for all OG image related routes
  if (path.startsWith('/api/og') || path.includes('/api/og/')) {
    return true;
  }

  // Allow access to any metadata-related endpoints
  if (path.includes('/api/metadata') || path.includes('/metadata')) {
    return true;
  }

  // Allow access to top reviewers endpoint
  if (path === '/api/top-reviewers') {
    return true;
  }

  // Allow access to public product and review endpoints for browsing
  if (path === '/api/get/all/products' || path === '/api/get/products/all') {
    return true;
  }

  if (path === '/api/get/reviews' || path === '/api/get/all/reviews') {
    return true;
  }

  if (path === '/api/get/reviews/approved' || path === '/api/get/reviews/latest') {
    return true;
  }

  // Allow access to get reviews endpoint (needed for individual review pages)
  if (path === '/api/get/reviews') {
    return true;
  }

  // Allow access to individual product and review data (for sharing)
  if (path === '/api/get/product' || path === '/api/get/review') {
    return true;
  }

  // Allow access to comments for reviews (needed for public review pages)
  if (path.startsWith('/api/comments/review/')) {
    return true;
  }

  // Allow access to search functionality
  if (path === '/api/productsearch') {
    return true;
  }

  // Check for the reviews page with ID parameter which needs to be shareable
  if (path.includes('/reviews') && req.nextUrl.searchParams.has('id')) {
    return true;
  }

  // Also allow access to the base /reviews route for public browsing
  if (path === '/reviews') {
    return true;
  }

  if (path.includes('/browse')) {
    return true;
  }

  // Allow access to reviews explore page for public browsing
  if (path.includes('/reviews-explore')) {
    return true;
  }
  // Check for the full review page (fr) with ID and productid parameters
  if (path.includes('/fr') &&
    req.nextUrl.searchParams.has('id') &&
    req.nextUrl.searchParams.has('productid')) {
    return true;
  }

  // Allow access to userprofile route - it handles its own authentication
  if (path === '/userprofile' || path.startsWith('/userprofile/')) {
    return true;
  }

  // Add widget exemptions - these need to be publicly accessible
  if (path.startsWith('/api/public/widgets/')) {
    return true;
  }
  
  if (path.startsWith('/widgets/iframe/')) {
    return true;
  }

  // Allow access to widget embed script
  if (path.startsWith('/api/widgets/embed.js')) {
    return true;
  }

  return false;
}

/**
 * Simplified admin route handler for middleware
 * Database checks are now handled in individual admin API routes using adminAuth middleware
 */
async function handleAdminRoute(req: NextRequest, auth: any) {
  try {
    // Get user ID from auth context
    const { userId } = await auth();
    
    if (!userId) {
      // Redirect to sign-in page if no user is authenticated
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://reviewit.gy";
      return NextResponse.redirect(`${baseUrl}/sign-in`);
    }

    // Allow access - detailed admin checks happen in individual API routes
    return null;
  } catch (error) {
    console.error("Error in admin route middleware:", error);
    // Return a simple redirect to avoid header issues
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://reviewit.gy";
    return NextResponse.redirect(`${baseUrl}/sign-in`);
  }
}

/**
 * User status checking is now handled in individual page components and API routes
 * This avoids Edge Runtime compatibility issues with Prisma Client
 * 
 * NOTE: User status enforcement should be implemented in:
 * 1. Page layouts (app/layout.tsx or route-specific layouts)
 * 2. API route middleware (adminAuth.ts)
 * 3. Individual page components as needed
 */

/**
 * Main middleware function that runs on every request
 * This is the entry point for Next.js middleware processing
 */
export default clerkMiddleware(async (auth, req) => {
  // Check for /products route and redirect to /browse
  if (req.nextUrl.pathname === '/products') {
    return NextResponse.redirect(new URL('/browse', req.url));
  }

  // Bot check must be first - allow all bots to access the site
  if (isBot(req)) {
    return; // Let the request continue without auth
  }

  // Skip auth for public API routes like OG image generation
  if (isPublicApiRoute(req)) {
    return;
  }

  // Explicitly exempt the root path from Clerk processing
  if (isRootPath(req)) {
    return; // Serve the page directly without Clerk redirects
  }

  // Skip middleware for the root path if it's a redirect from admin check
  const url = new URL(req.url);
  const redirectSource = url.searchParams.get("redirectSource");
  if (isRootPath(req) && redirectSource === "adminCheck") {
    return;
  }

  // Handle admin routes separately
  if (isAdminRoute(req)) {
    return handleAdminRoute(req, auth);
  }
  
  // For protected routes, check authentication
  if (!isPublicApiRoute(req) && !isRootPath(req)) {
    try {
      // Get current auth state
      const authResult = await auth();



      // Protect all non-public routes
      if (!authResult.userId) {
        // User is not authenticated - let Clerk handle the redirect automatically
        // Don't manually redirect as this causes CORS issues with Account Portal
        // console.log('❌ No userId found for:', req.nextUrl.pathname, '- letting Clerk handle redirect');
        // Return null to let Clerk's built-in redirect handling take over
        return null;
      }

      // User status checking is now handled in individual routes/components
    } catch (error) {
      console.error("Error in middleware auth protection:", error);
    }
  }
});

export const config = {
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};
