generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Security log for tracking security-related events
model SecurityLog {
  id          String   @id @default(uuid())
  eventType   String   // Type of security event (e.g., SUSPENDED_ACCESS_ATTEMPT)
  userId      String   // User ID associated with the event
  ipAddress   String   // IP address of the request
  userAgent   String   // User agent of the request
  requestPath String   // Path that was requested
  details     String?  // JSON string with additional details
  severity    String   // LOW, MEDIUM, HIGH, CRITICAL
  timestamp   DateTime @default(now())
  
  // Index for faster queries on common filters
  @@index([eventType])
  @@index([userId])
  @@index([timestamp])
}

model Product {
  id               String                   @id @default(uuid())
  address          String?                  // Keep for backward compatibility
  streetAddress    String?                  // NEW: Street address only ("123 Main Street")
  city             String?                  // NEW: City/Town/Area ("Linden", "Georgetown", "Bartica")
  createdDate      DateTime                 @default(now())
  description      String
  display_image    String
  images           String[]
  videos           String[]
  links            String[]
  name             String                   @unique
  tags             String[]
  openingHrs       String?
  closingHrs       String?
  businessHours    Json?                    // Flexible business hours with per-day schedules
  telephone        String?
  website          String[]
  rating           Int                      @default(0)
  hasOwner         Boolean?
  ownerId          String?
  createdById      String
  isDeleted        Boolean                  @default(false)
  isPublic         Boolean                  @default(true)    // Controls product visibility
  email            String?
  businessId       String?
  featuredPosition Int?
  rating1Star      Int                      @default(0)
  rating2Stars     Int                      @default(0)
  rating3Stars     Int                      @default(0)
  rating4Stars     Int                      @default(0)
  rating5Stars     Int                      @default(0)
  updatedAt        DateTime?
  descriptionLastUpdatedAt DateTime? // Tracks description updates
  imagesLastUpdatedAt      DateTime? // Tracks image updates
  viewCount        Int                      @default(0)
  openingDays      String[]                 @default([])
  latitude         Float?
  longitude        Float?
  business         Business?                @relation(fields: [businessId], references: [id])
  createdBy        User                     @relation(fields: [createdById], references: [id])
  analytics        ProductAnalytics?
  claims           ProductClaim?
  viewEvents       ProductViewEvent[]
  reviews          Review[]
  userInteractions UserProductInteraction[]
  promotions       Promotion[]
  reports          ProductReport[]
  widgets          Widget[]
  sector           String?
  sub_sector       String?
  industry         String?

  @@index([isDeleted, rating])
  @@index([viewCount])
  @@index([featuredPosition])
  @@index([businessId])
}

model VoteCount {
  id             String @id @default(uuid())
  reviewId       String @unique
  helpfulVotes   Int    @default(0)
  unhelpfulVotes Int    @default(0)
  review         Review @relation(fields: [reviewId], references: [id])
}

model Review {
  id                String            @id @default(uuid())
  body              String
  createdDate       DateTime          @default(now())
  rating            Int
  title             String
  productId         String
  userId            String
  isVerified        Boolean?
  verifiedBy        String?
  isPublic          Boolean           @default(true)
  images            String[]
  videos            String[]
  links             String[]
  createdBy         String?
  isDeleted         Boolean?          @default(false)
  verifiedAt        DateTime?
  ownerRespondedAt  DateTime?
  comments          Comment[]
  moderationHistory ModerationEvent[]
  product           Product           @relation(fields: [productId], references: [id])
  user              User              @relation(fields: [userId], references: [id])
  voteCount         VoteCount?
  likedBy           User[]            @relation("ReviewLike")
  reports           ReviewReport[]

  @@unique([title, userId, productId], name: "unique_review")
  @@index([isDeleted, isVerified])
  @@index([rating])
  @@index([createdDate])
  @@index([productId])
  @@index([userId])
}

model Comment {
  id          String        @id @default(uuid())
  body        String
  createdDate DateTime      @default(now())
  reviewId    String
  userId      String
  isDeleted   Boolean?      @default(false)
  parentId    String?
  downvotes   Int           @default(0)
  upvotes     Int           @default(0)
  parent      Comment?      @relation("CommentReplies", fields: [parentId], references: [id])
  replies     Comment[]     @relation("CommentReplies")
  review      Review        @relation(fields: [reviewId], references: [id])
  user        User          @relation(fields: [userId], references: [id])
  votes       CommentVote[]
}

model CommentVote {
  id          String   @id @default(uuid())
  commentId   String
  userId      String
  voteType    String   @default("UP")
  createdAt   DateTime @default(now())
  clerkUserId String
  comment     Comment  @relation(fields: [commentId], references: [id])
  user        User     @relation(fields: [userId], references: [id])

  @@unique([commentId, clerkUserId])
  @@index([commentId])
  @@index([userId])
  @@index([clerkUserId])
}

model User {
  id                  String                   @id
  userName            String                   @unique
  avatar              String?
  createdDate         DateTime                 @default(now())
  email               String                   @unique
  firstName           String
  lastName            String
  clerkUserId         String                   @unique
  isDeleted           Boolean?                 @default(false)
  bio                 String?
  lastLoginAt         DateTime?
  loginCount          Int                      @default(0)
  usernameNeedsChange Boolean?                @default(false)
  role                String                   @default("USER")
  status              String                   @default("ACTIVE")
  suspendedReason     String?
  suspendedUntil      DateTime?
  usernameChangedAt   DateTime?
  adminActions        AdminAction[]
  bugReports          BugReport[]
  resolvedBugs        BugReport[]              @relation("BugReportResolver")
  businesses          Business[]
  comments            Comment[]
  commentVotes        CommentVote[]
  moderationEvents    ModerationEvent[]
  product             Product[]
  reviewedClaims      ProductClaim[]           @relation("ClaimReviewer")
  productClaims       ProductClaim[]
  productViews        ProductViewEvent[]
  reviews             Review[]
  productInteractions UserProductInteraction[]
  likedReviews        Review[]                 @relation("ReviewLike")
  submittedReports    ReviewReport[]           @relation("ReportSubmitter")
  resolvedReports     ReviewReport[]           @relation("ReportResolver")
  createdPromotions   Promotion[]
  submittedProductReports ProductReport[]      @relation("ProductReportSubmitter")
  resolvedProductReports  ProductReport[]      @relation("ProductReportResolver")

  @@index([role, status])
  @@index([isDeleted, status])
  @@index([lastLoginAt])
}

model Business {
  id                 String    @id @unique @default(cuid())
  ownerId            String
  subscriptionStatus String
  subscriptionExpiry DateTime?
  createdDate        DateTime? @default(now())
  isVerified         Boolean?  @default(false)
  ownerName          String?
  // Tiered subscription fields
  tier              Tier     @default(starter)
  tierExpiresAt     DateTime?
  owner              User      @relation(fields: [ownerId], references: [id])
  products           Product[]
  promotions         Promotion[]
  widgets            Widget[]
  analytics  BusinessAnalytics? // Add relation to the new model
}

model BusinessAnalytics {
    id                            String    @id @default(uuid())
    businessId                    String    @unique
    averageReviewResponseTime     Float?    // In hours
    negativeReviewResponseRate    Float?    // Percentage of negative reviews responded to
    productContentFreshnessScore  Float?    // A score from 0 to 100
    lastCalculated                DateTime  @default(now())
    business                      Business  @relation(fields: [businessId], references: [id])

    @@index([businessId])
  }

model AdminAction {
  id          String     @id @default(uuid())
  adminId     String
  actionType  String
  targetId    String
  targetType  String
  description String
  createdAt   DateTime   @default(now())
  bugReportId String?
  admin       User       @relation(fields: [adminId], references: [id])
  BugReport   BugReport? @relation(fields: [bugReportId], references: [id])

  @@index([adminId])
  @@index([targetId])
  @@index([targetType])
  @@index([createdAt])
}

model ModerationEvent {
  id        String   @id @default(uuid())
  reviewId  String
  adminId   String
  action    String
  reason    String?
  createdAt DateTime @default(now())
  admin     User     @relation(fields: [adminId], references: [id])
  review    Review   @relation(fields: [reviewId], references: [id])

  @@index([reviewId])
  @@index([adminId])
  @@index([createdAt])
  @@index([action])
}

model ProductViewEvent {
  id          String   @id @default(uuid())
  productId   String
  userId      String?
  sessionId   String
  timestamp   DateTime @default(now())
  duration    Int?
  scrollDepth Int?
  source      String?
  deviceType  String?
  isNewUser   Boolean  @default(false)
  isThrottled Boolean  @default(false)
  product     Product  @relation(fields: [productId], references: [id])
  user        User?    @relation(fields: [userId], references: [id])

  @@index([productId])
  @@index([userId])
  @@index([sessionId])
  @@index([timestamp])
}

model ProductAnalytics {
  id                  String   @id @default(uuid())
  productId           String   @unique
  totalViews          Int      @default(0)
  uniqueVisitors      Int      @default(0)
  averageViewDuration Float    @default(0)
  peakHours           Json
  weekdayStats        Json
  lastUpdated         DateTime @default(now())
  product             Product  @relation(fields: [productId], references: [id])

  @@index([productId])
  @@index([totalViews])
}

model UserProductInteraction {
  id               String   @id @default(uuid())
  userId           String
  productId        String
  lastViewed       DateTime @default(now())
  viewCount        Int      @default(0)
  hasReviewed      Boolean  @default(false)
  hasLiked         Boolean  @default(false)
  averageTimeSpent Float    @default(0)
  product          Product  @relation(fields: [productId], references: [id])
  user             User     @relation(fields: [userId], references: [id])

  @@unique([userId, productId])
  @@index([userId])
  @@index([productId])
  @@index([lastViewed])
}

model ProductClaim {
  id              String    @id @default(uuid())
  productId       String    @unique
  userId          String
  status          String    @default("PENDING")
  contactInfo     String
  additionalInfo  String
  images          String[]
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  reviewedAt      DateTime?
  reviewedBy      String?
  rejectionReason String?
  product         Product   @relation(fields: [productId], references: [id])
  reviewer        User?     @relation("ClaimReviewer", fields: [reviewedBy], references: [id])
  user            User      @relation(fields: [userId], references: [id])

  @@index([status])
  @@index([userId])
  @@index([createdAt])
}

model BugReport {
  id               String          @id @default(uuid())
  title            String
  description      String
  browser          String?
  device           String?
  mobile_os        String?
  status           BugReportStatus @default(OPEN)
  resolved_at      DateTime?
  resolved_by      String?
  resolution_notes String?
  created_at       DateTime        @default(now())
  updated_at       DateTime        @updatedAt
  reporterId       String?
  adminActions     AdminAction[]
  reporter         User?           @relation(fields: [reporterId], references: [id])
  resolver         User?           @relation("BugReportResolver", fields: [resolved_by], references: [id])

  @@index([status])
  @@index([reporterId])
  @@index([resolved_by])
  @@index([created_at])
}

enum BugReportStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
  WONT_FIX
}

model ReviewReport {
  id         String    @id @default(uuid())
  reviewId   String
  userId     String
  reason     String
  status     String    @default("PENDING")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  resolvedAt DateTime?
  resolvedBy String?
  notes      String?

  review   Review @relation(fields: [reviewId], references: [id])
  user     User   @relation("ReportSubmitter", fields: [userId], references: [id])
  resolver User?  @relation("ReportResolver", fields: [resolvedBy], references: [id])

  @@index([reviewId])
  @@index([userId])
  @@index([status])
}

model ProductReport {
  id         String    @id @default(uuid())
  productId  String
  userId     String
  reason     String
  status     String    @default("PENDING")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  resolvedAt DateTime?
  resolvedBy String?
  notes      String?

  product  Product @relation(fields: [productId], references: [id])
  user     User    @relation("ProductReportSubmitter", fields: [userId], references: [id])
  resolver User?   @relation("ProductReportResolver", fields: [resolvedBy], references: [id])

  @@index([productId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model Promotion {
  id                  String   @id @default(uuid())
  title               String
  description         String
  startDate           DateTime
  endDate             DateTime
  discountPercentage  Float?
  discountAmount      Float?
  promotionCode       String?
  isActive            Boolean  @default(true)
  image               String?  // Cloudinary URL
  imagePublicId       String?  // For Cloudinary management

  // Relationships
  productId           String
  businessId          String
  createdById         String

  // Analytics
  viewCount           Int      @default(0)
  clickCount          Int      @default(0)
  conversionCount     Int      @default(0)

  // Timestamps
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  product             Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  business            Business @relation(fields: [businessId], references: [id], onDelete: Cascade)
  createdBy           User     @relation(fields: [createdById], references: [id])

  // Analytics tracking
  analytics           PromotionAnalytics?

  @@index([productId])
  @@index([businessId])
  @@index([isActive])
  @@index([startDate, endDate])
  @@index([createdAt])
  @@index([createdById])
}

model PromotionAnalytics {
  id                  String   @id @default(uuid())
  promotionId         String   @unique

  // Daily metrics
  dailyViews          Json     @default("{}")  // { "2024-01-01": 150, "2024-01-02": 200 }
  dailyClicks         Json     @default("{}")  // { "2024-01-01": 15, "2024-01-02": 25 }
  dailyConversions    Json     @default("{}")  // { "2024-01-01": 2, "2024-01-02": 3 }

  // Performance metrics
  ctr                 Float    @default(0) // Click-through rate
  conversionRate      Float    @default(0)
  averageOrderValue   Float?

  // Audience insights
  topReferrers        Json     @default("{}")  // { "google": 100, "facebook": 50 }
  deviceBreakdown     Json     @default("{}")  // { "mobile": 60, "desktop": 40 }

  lastUpdated         DateTime @default(now())

  promotion           Promotion @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@index([promotionId])
}

model Widget {
  id              String   @id @default(uuid())
  businessId      String
  productId       String?  // Optional: widget can be for specific product or entire business
  name            String   // User-defined name for the widget
  type            WidgetType
  
  // Configuration
  config          Json     @default("{}")  // Flexible configuration object
  isActive        Boolean  @default(true)
  
  // Security Configuration
  securityLevel   WidgetSecurityLevel @default(SIMPLE)  // SIMPLE or SECURE
  apiKey          String?  // API key for secure widgets
  tokenExpiry     Int      @default(3600)  // Token expiry in seconds (default 1 hour)
  maxRequestsPerHour Int   @default(1000)  // Rate limit per domain
  
  // Styling options
  theme           String   @default("light") // light, dark, custom
  primaryColor    String?  // Custom brand color
  borderRadius    String   @default("8px")
  showLogo        Boolean  @default(true)
  showPoweredBy   Boolean  @default(true)
  
  // Content options
  maxReviews      Int      @default(5)
  showRating      Boolean  @default(true)
  showReviewText  Boolean  @default(true)
  showReviewDate  Boolean  @default(true)
  showReviewerName Boolean @default(true)
  allowedDomains   String[]   @default([])   // List of allowed domains for embedding (empty means allow all)
  
  // Analytics
  viewCount       Int      @default(0)
  clickCount      Int      @default(0)
  conversionCount Int      @default(0)   // Count of conversions (leads generated)
  blockedCount    Int      @default(0)   // Count of blocked embedding attempts
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  lastUsed        DateTime?
  
  // Relations
  business        Business @relation(fields: [businessId], references: [id], onDelete: Cascade)
  product         Product? @relation(fields: [productId], references: [id], onDelete: Cascade)
  analytics       WidgetAnalytics?
  tokens          WidgetToken[]
  domainVerifications DomainVerification[]
  
  @@index([businessId])
  @@index([productId])
  @@index([type])
  @@index([isActive])
  @@index([securityLevel])
  @@index([createdAt])
}

enum WidgetType {
  REVIEW_CAROUSEL    // Scrolling reviews
  REVIEW_GRID        // Grid of reviews
  RATING_SUMMARY     // Just rating and count
  MINI_REVIEW        // Compact single review
  BUSINESS_CARD      // Business info with rating
  TRUST_BADGE        // Simple trust indicator
  REVIEW_POPUP       // Modal-style widget
}

enum WidgetSecurityLevel {
  SIMPLE    // Public widget - can be embedded anywhere
  SECURE    // Domain-restricted widget with verification
}

model WidgetAnalytics {
  id              String   @id @default(uuid())
  widgetId        String   @unique
  
  // Daily metrics
  dailyViews      Json     @default("{}")  // { "2024-01-01": 150 }
  dailyClicks     Json     @default("{}")  // { "2024-01-01": 15 }
  dailyConversions Json    @default("{}")  // { "2024-01-01": 3 } - conversion events
  blockedEvents   Json     @default("{}")  // { "2024-01-01": 5 } - blocked embedding attempts
  
  // Referrer data
  topReferrers    Json     @default("{}")  // { "example.com": 100 }
  
  // Performance metrics
  averageLoadTime Float    @default(0)
  errorCount      Int      @default(0)
  
  lastUpdated     DateTime @default(now())
  
  widget          Widget   @relation(fields: [widgetId], references: [id], onDelete: Cascade)
  
  @@index([widgetId])
  @@index([lastUpdated])
}

model CorsErrorLog {
  id             String   @id @default(uuid())
  timestamp      DateTime @default(now())
  origin         String?
  requestUrl     String
  method         String
  errorType      String   // CorsErrorType enum values
  userAgent      String?
  referrer       String?
  widgetId       String?
  errorMessage   String
  requestHeaders Json     @default("{}")
  ipAddress      String
  
  @@index([timestamp])
  @@index([errorType])
  @@index([origin])
  @@index([ipAddress])
  @@index([widgetId])
}

model CorsAlert {
  id         String   @id @default(uuid())
  type       String   // alert type (high_error_rate, suspicious_activity, etc.)
  message    String
  metadata   Json     @default("{}")
  timestamp  DateTime @default(now())
  isResolved Boolean  @default(false)
  resolvedAt DateTime?
  resolvedBy String?
  
  @@index([timestamp])
  @@index([type])
  @@index([isResolved])
}

// Widget Security Models
model WidgetToken {
  id           String   @id @default(uuid())
  widgetId     String
  domain       String
  token        String   @unique
  expiresAt    DateTime
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  lastUsed     DateTime?
  requestCount Int      @default(0)
  
  // Relations
  widget       Widget   @relation(fields: [widgetId], references: [id], onDelete: Cascade)
  
  @@index([widgetId])
  @@index([token])
  @@index([domain])
  @@index([expiresAt])
  @@unique([widgetId, domain])
}

model DomainVerification {
  id               String   @id @default(uuid())
  widgetId         String
  domain           String
  verificationCode String
  method           VerificationMethod
  isVerified       Boolean  @default(false)
  verifiedAt       DateTime?
  createdAt        DateTime @default(now())
  expiresAt        DateTime
  
  // Relations
  widget           Widget   @relation(fields: [widgetId], references: [id], onDelete: Cascade)
  
  @@index([widgetId])
  @@index([domain])
  @@index([verificationCode])
  @@index([isVerified])
  @@unique([widgetId, domain])
}

enum Tier {
  starter
  pro
  enterprise
}

enum VerificationMethod {
  HTML_FILE    // Upload HTML file to domain root
  DNS_TXT      // Add TXT record to DNS
  META_TAG     // Add meta tag to website
}
