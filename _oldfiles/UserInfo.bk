import React, { useState, useRef, useEffect, useMemo } from "react";
import Image from "next/image";
import Link from "next/link";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { avatarTriggerAtom } from "@/app/store/store";
import { useAtom } from "jotai";
import {
  AlertCircle,
  Camera,
  Edit,
  FileText,
  FileX,
  MessageCircle,
  Save,
  Search,
  Settings,
  ThumbsUp,
  Building,
} from "lucide-react";
import { iUser, iReview, iProduct } from "../util/Interfaces";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { uploadProfilePicToCloudinary } from "../util/uploadImageToCloudinary";
import { useImageResizer } from "../util/useImageResizer";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import UserSettings from "./UserSettings";

interface UserInfoProps {
  user: iUser;
  onUpdateUser: (updatedUser: Partial<iUser>) => void;
}

// Define gradient combinations
const profileGradients = [
  "from-blue-500 to-purple-600",
  "from-pink-500 to-orange-400",
  "from-green-400 to-blue-500",
  "from-purple-500 to-pink-500",
  "from-yellow-400 to-orange-500",
  "from-indigo-500 to-purple-600",
  "from-teal-400 to-blue-500",
  "from-red-500 to-pink-500",
  "from-emerald-500 to-teal-500",
  "from-violet-500 to-purple-600",
  "from-cyan-500 to-blue-500",
  "from-rose-500 to-pink-500",
  "from-amber-500 to-orange-500",
  "from-sky-500 to-indigo-500",
  "from-fuchsia-500 to-purple-500",
];

export default function UserInfo({ user, onUpdateUser }: UserInfoProps) {
  const { userId: clerkUserId } = useAuth();
  let {
    firstName,
    lastName,
    userName,
    avatar,
    reviews,
    comments,
    likedReviews,
    bio,
    commentVotes,
    id,
    businesses,
  } = user;
  const [activeTab, setActiveTab] = useState("reviews");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<"date" | "rating">("date");
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [editedFirstName, setEditedFirstName] = useState(firstName);
  const [editedLastName, setEditedLastName] = useState(lastName);
  const [editedUserName, setEditedUserName] = useState(userName);
  const [editedBio, setEditedBio] = useState(bio || "");
  const [isAvatarUploading, setIsAvatarUploading] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [currentAvatar, setCurrentAvatar] = useState(avatar || "");
  const [, setAvatarTrigger] = useAtom(avatarTriggerAtom);
  const [isLoadingReviews, setIsLoadingReviews] = useState(false);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [isLoadingLikes, setIsLoadingLikes] = useState(false);
  const [selectedGradient, setSelectedGradient] = useState("");

  dayjs.extend(relativeTime);

  // Calculate total likes
  const reviewLikes = likedReviews?.length || 0;
  const commentUpvotes =
    commentVotes?.filter((vote) => vote.voteType === "UP").length || 0;
  const totalLikes = reviewLikes + commentUpvotes;

  // Get unique tags from all reviews
  const allTags = Array.from(
    new Set(reviews?.flatMap((review) => review.product?.tags || []) || []),
  );

  // Enhanced filtering function with type safety
  const filteredReviews = useMemo(() => {
    if (!reviews) return [];

    return reviews
      .filter((review) => {
        const matchesSearch =
          searchTerm === "" ||
          (review.title?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false) ||
          (review.body?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false) ||
          (review.product?.name
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ??
            false);

        const matchesTag =
          !selectedTag ||
          (review.product?.tags?.includes(selectedTag) ?? false);

        return matchesSearch && matchesTag;
      })
      .sort((a, b) => {
        if (sortBy === "date") {
          return (
            new Date(b.createdDate || 0).getTime() -
            new Date(a.createdDate || 0).getTime()
          );
        } else {
          return (b.rating || 0) - (a.rating || 0);
        }
      });
  }, [reviews, searchTerm, selectedTag, sortBy]);

  // Handle loading state separately
  useEffect(() => {
    setIsLoadingReviews(true);
    const timer = setTimeout(() => {
      setIsLoadingReviews(false);
    }, 100); // Small delay to prevent flickering
    return () => clearTimeout(timer);
  }, [searchTerm, selectedTag, sortBy]);

  const filteredComments = comments?.filter((comment) =>
    comment.body.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const filteredLikes = likedReviews?.filter((liked) => {
    const likedReviews =
      liked.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
      liked.userId !== user.id;
    return likedReviews;
  });

  // Input sanitization to prevent XSS
  const sanitizeInput = (input: string): string => {
    return input.replace(/<[^>]*>/g, "");
  };

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!editedFirstName.trim()) {
      errors.firstName = "First name is required";
    }

    if (!editedLastName.trim()) {
      errors.lastName = "Last name is required";
    }

    if (!editedUserName.trim()) {
      errors.userName = "Username is required";
    } else if (editedUserName.length < 3) {
      errors.userName = "Username must be at least 3 characters";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAvatarClick = () => {
    if (isEditable) {
      fileInputRef.current?.click();
    }
  };

  const { processImage, isResizing } = useImageResizer();

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (!isEditable) return;
    const file = event.target.files?.[0];
    if (file) {
      setIsAvatarUploading(true);
      const tempUrl = URL.createObjectURL(file);
      setCurrentAvatar(tempUrl);

      try {
        // Process the file directly with our enhanced hook
        const result = await processImage(file, {
          maxDimension: 400, // Appropriate size for profile pictures
          quality: 0.8,
        });

        // Extract the dataUrl from the result
        let dataUrl: string;
        if ("dataUrl" in result) {
          dataUrl = result.dataUrl ?? "";
        } else {
          // This shouldn't happen now that we always return dataUrl
          console.warn("Unexpected result type when processing profile image");
          throw new Error("Failed to process image");
        }

        try {
          const res = await uploadProfilePicToCloudinary(dataUrl);
          const imageUrl = res.secure_url;
          setCurrentAvatar(imageUrl);
          onUpdateUser({ avatar: imageUrl });
          setAvatarTrigger(imageUrl);
          toast.success("Profile picture updated successfully");
        } catch (error) {
          console.error("Error uploading image:", error);
          toast.error("Failed to upload image. Please try again.");
        } finally {
          setIsAvatarUploading(false);
          URL.revokeObjectURL(tempUrl);
        }
      } catch (error) {
        console.error("Error processing image:", error);
        toast.error("Failed to process image. Please try again.");
        setIsAvatarUploading(false);
        URL.revokeObjectURL(tempUrl);
      }
    }
  };

  const handleSaveProfile = () => {
    if (!isEditable) return;

    if (validateForm()) {
      onUpdateUser({
        firstName: sanitizeInput(editedFirstName),
        lastName: sanitizeInput(editedLastName),
        userName: sanitizeInput(editedUserName),
        bio: sanitizeInput(editedBio),
      });
      setIsEditingProfile(false);
    } else {
      toast.error("Please fix the errors in the form");
    }
  };

  const isEditable = user.clerkUserId === clerkUserId;

  // Select a random gradient on component mount
  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * profileGradients.length);
    setSelectedGradient(profileGradients[randomIndex]);
  }, []);

  return (
    <Card className="w-full border-0 shadow-none overflow-hidden">
      <CardHeader
        className={`relative h-36 sm:h-52 md:h-64 overflow-hidden bg-gradient-to-br ${selectedGradient} transition-all duration-500`}
      >
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-20 -right-20 w-64 h-64 sm:w-80 sm:h-80 bg-gradient-to-br from-white/10 to-white/30 rounded-full blur-3xl animate-pulse opacity-50"></div>
          <div className="absolute -bottom-40 -left-20 w-64 h-64 sm:w-80 sm:h-80 bg-gradient-to-tl from-white/10 to-white/30 rounded-full blur-3xl animate-pulse opacity-50 animation-delay-1000"></div>
        </div>
        
        {/* Subtle background branding */}
        <div className="absolute inset-0 pointer-events-none select-none">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-5 text-white text-4xl sm:text-6xl md:text-8xl font-light tracking-wider">
            ReviewIt
          </div>
        </div>
        
        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/5 to-black/10"></div>
        
        {/* Avatar container */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative transition-transform duration-300 hover:scale-105">
            <div
              className={`relative h-28 w-28 sm:h-36 sm:w-36 md:h-44 md:w-44 ${isEditable ? "cursor-pointer" : ""} transition-all duration-300`}
              onClick={handleAvatarClick}
              role={isEditable ? "button" : undefined}
              aria-label={isEditable ? "Change profile picture" : undefined}
              tabIndex={isEditable ? 0 : undefined}
              onKeyDown={(e) => {
                if (isEditable && (e.key === "Enter" || e.key === " ")) {
                  e.preventDefault();
                  handleAvatarClick();
                }
              }}
            >
              <Avatar className="h-full w-full border-4 border-white/90 shadow-xl backdrop-blur-sm transition-all duration-300 hover:shadow-2xl">
                <AvatarImage
                  src={currentAvatar || ""}
                  alt={`${editedFirstName} ${editedLastName}`}
                  className="object-cover"
                />
                <AvatarFallback className="bg-white/80 text-gray-700 text-2xl sm:text-3xl md:text-4xl font-medium">
                  {`${editedFirstName?.charAt(0) || ""}${editedLastName?.charAt(0) || ""}`}
                </AvatarFallback>
              </Avatar>
              
              {/* Loading state */}
              {(isAvatarUploading || isResizing) && (
                <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 rounded-full backdrop-blur-sm">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-t-2 border-white"></div>
                  {isResizing && (
                    <span className="absolute text-white text-sm font-medium mt-16 px-2 py-1 bg-black/40 rounded-full">
                      Processing...
                    </span>
                  )}
                </div>
              )}
            </div>
            
            {/* Edit button */}
            {isEditable && (
              <div
                className="absolute -bottom-1 -right-1 bg-primary rounded-full p-2.5 cursor-pointer shadow-lg hover:shadow-xl hover:bg-primary/90 transition-all duration-300 border-2 border-white/90"
                onClick={handleAvatarClick}
                role="button"
                aria-label="Change profile picture"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    handleAvatarClick();
                  }
                }}
              >
                <Camera className="h-5 w-5 text-white" aria-hidden="true" />
              </div>
            )}
          </div>
          
          {isEditable && (
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
              aria-label="Upload profile picture"
            />
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-3 sm:pt-5 md:pt-7 px-4 sm:px-6 md:px-8">
        <div className="px-4 sm:px-6 mb-6">
          {isEditingProfile && isEditable ? (
            <div className="max-w-2xl mx-auto bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-xl font-semibold text-center mb-6 text-gray-800">
                Edit Profile
              </h3>
              <div className="space-y-4">
                <div className="space-y-1">
                  <label
                    htmlFor="firstName"
                    className="text-sm font-medium text-gray-700"
                  >
                    First Name
                  </label>
                  <Input
                    id="firstName"
                    value={editedFirstName}
                    onChange={(e) => setEditedFirstName(e.target.value)}
                    placeholder="First Name"
                    className={`bg-white ${formErrors.firstName ? "border-red-500" : ""}`}
                    aria-invalid={!!formErrors.firstName}
                    aria-describedby={
                      formErrors.firstName ? "firstName-error" : undefined
                    }
                  />
                  {formErrors.firstName && (
                    <p
                      id="firstName-error"
                      className="text-xs text-red-500 flex items-center"
                    >
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {formErrors.firstName}
                    </p>
                  )}
                </div>

                <div className="space-y-1">
                  <label
                    htmlFor="lastName"
                    className="text-sm font-medium text-gray-700"
                  >
                    Last Name
                  </label>
                  <Input
                    id="lastName"
                    value={editedLastName}
                    onChange={(e) => setEditedLastName(e.target.value)}
                    placeholder="Last Name"
                    className={`bg-white ${formErrors.lastName ? "border-red-500" : ""}`}
                    aria-invalid={!!formErrors.lastName}
                    aria-describedby={
                      formErrors.lastName ? "lastName-error" : undefined
                    }
                  />
                  {formErrors.lastName && (
                    <p
                      id="lastName-error"
                      className="text-xs text-red-500 flex items-center"
                    >
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {formErrors.lastName}
                    </p>
                  )}
                </div>

                <div className="space-y-1">
                  <label
                    htmlFor="userName"
                    className="text-sm font-medium text-gray-700"
                  >
                    Username
                  </label>
                  <Input
                    id="userName"
                    value={editedUserName}
                    onChange={(e) => setEditedUserName(e.target.value)}
                    placeholder="Username"
                    className={`bg-white ${formErrors.userName ? "border-red-500" : ""}`}
                    aria-invalid={!!formErrors.userName}
                    aria-describedby={
                      formErrors.userName ? "userName-error" : undefined
                    }
                  />
                  {formErrors.userName && (
                    <p
                      id="userName-error"
                      className="text-xs text-red-500 flex items-center"
                    >
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {formErrors.userName}
                    </p>
                  )}
                </div>

                <div className="space-y-1">
                  <label
                    htmlFor="bio"
                    className="text-sm font-medium text-gray-700"
                  >
                    Bio
                  </label>
                  <Textarea
                    id="bio"
                    value={editedBio}
                    onChange={(e) => setEditedBio(e.target.value)}
                    placeholder="Write your bio here..."
                    className="bg-white min-h-[100px] resize-none"
                    aria-label="Bio"
                  />
                </div>
              </div>

              <div className="flex gap-3 justify-end mt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsEditingProfile(false)}
                  className="min-w-[100px] border-gray-300 hover:bg-gray-100"
                  aria-label="Cancel profile editing"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveProfile}
                  className="min-w-[100px] bg-blue-600 hover:bg-blue-700 text-white"
                  aria-label="Save profile changes"
                >
                  <Save className="h-4 w-4 mr-2" aria-hidden="true" />
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center animate-fadeIn">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3 tracking-tight">
                {`${editedFirstName} ${editedLastName}`}
              </h2>
              <p className="text-lg text-gray-600 mb-4 font-medium">@{editedUserName}</p>
              
              {editedBio ? (
                <div className="relative max-w-2xl mx-auto mb-6 px-4 sm:px-6">
                  <div className="absolute -left-2 top-0 text-gray-300 opacity-30">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-8 h-8 sm:w-10 sm:h-10">
                      <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                      <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                    </svg>
                  </div>
                  <p className="text-gray-700 leading-relaxed text-base sm:text-lg px-6 py-4 bg-gray-50/80 rounded-xl shadow-sm border border-gray-100">
                    {editedBio}
                  </p>
                </div>
              ) : null}

              {isEditable && (
                <Button
                  variant="outline"
                  onClick={() => setIsEditingProfile(true)}
                  className="mt-2 hover:bg-primary/10 border-gray-300 hover:border-primary/30 transition-all duration-300 shadow-sm hover:shadow px-5 py-2 h-auto"
                  aria-label="Edit profile"
                >
                  <Edit className="h-4 w-4 mr-2" aria-hidden="true" />
                  Edit Profile
                </Button>
              )}
            </div>
          )}
        </div>

<Tabs
  defaultValue="reviews"
  className="w-full"
  onValueChange={setActiveTab}
  aria-label="User content tabs"
  activationMode="manual"
>          {/* Sticky tabs navigation that stays visible when scrolling */}
          <div className="sticky top-0 z-10 px-4 sm:px-0 -mx-4 sm:mx-0 pt-2 pb-4 bg-white/95 backdrop-blur-sm shadow-sm border-b">
            <div className="overflow-x-auto overflow-y-hidden">
              <div className="max-w-3xl mx-auto">
                <TabsList
                  className="flex min-w-max gap-x-2 bg-gray-50/80 border rounded-xl p-1.5 h-16 sm:h-14 shadow-inner"
                >
                <TabsTrigger
                  value="reviews"
                  className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col sm:flex-row items-center justify-center sm:gap-2 py-1 sm:py-3 transition-all duration-300 hover:bg-white/50"
                >
                  <FileText className="h-4 w-4 mb-0.5 sm:mb-0" />
                  <span className="text-xs sm:text-sm">Reviews</span>
                  <span className="bg-gray-200 data-[state=active]:bg-blue-100 px-2 py-0.5 rounded-full text-xs font-semibold transition-colors duration-300 absolute -top-1 -right-1">
                    {reviews?.length || 0}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="comments"
                  className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col sm:flex-row items-center justify-center sm:gap-2 py-1 sm:py-3 transition-all duration-300 hover:bg-white/50"
                >
                  <MessageCircle className="h-4 w-4 mb-0.5 sm:mb-0" />
                  <span className="text-xs sm:text-sm">Comments</span>
                  <span className="bg-gray-200 data-[state=active]:bg-blue-100 px-2 py-0.5 rounded-full text-xs font-semibold transition-colors duration-300 absolute -top-1 -right-1">
                    {comments?.length || 0}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="likes"
                  className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col sm:flex-row items-center justify-center sm:gap-2 py-1 sm:py-3 transition-all duration-300 hover:bg-white/50"
                >
                  <ThumbsUp className="h-4 w-4 mb-0.5 sm:mb-0" />
                  <span className="text-xs sm:text-sm">Likes</span>
                  <span className="bg-gray-200 data-[state=active]:bg-blue-100 px-2 py-0.5 rounded-full text-xs font-semibold transition-colors duration-300 absolute -top-1 -right-1">
                    {reviewLikes}
                  </span>
                </TabsTrigger>
                {isEditable && (
                <Link
                  href="/mybusinesses"
                  className="relative rounded-lg text-sm font-medium flex flex-col sm:flex-row items-center justify-center sm:gap-2 py-1 sm:py-3 transition-all duration-300 hover:bg-white/50 px-4"
                >
                  <Building className="h-4 w-4 mb-0.5 sm:mb-0" />
                  <span className="text-xs sm:text-sm">My Businesses</span>
                </Link>
              )}
                {isEditable && (
                  <TabsTrigger
                    value="settings"
                    className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col sm:flex-row items-center justify-center sm:gap-2 py-1 sm:py-3 transition-all duration-300 hover:bg-white/50"
                  >
                    <Settings className="h-4 w-4 mb-0.5 sm:mb-0" />
                    <span className="text-xs sm:text-sm">Settings</span>
                  </TabsTrigger>
                )}
                </TabsList>
              </div>
            </div>
          </div>

          <TabsContent value="reviews" className="animate-fadeIn">
            <div className="space-y-6 pt-4">
              <div className="bg-gray-50/80 p-5 rounded-xl space-y-4 shadow-sm border border-gray-100">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search reviews..."
                    className="pl-10 bg-white border-gray-200 rounded-lg h-12 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm transition-all duration-300 hover:shadow-md"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Select
                    value={selectedTag || "all"}
                    onValueChange={(value) =>
                      setSelectedTag(value === "all" ? null : value)
                    }
                  >
                    <SelectTrigger className="bg-white border-gray-200 rounded-lg h-12 text-sm shadow-sm transition-all duration-300 hover:shadow-md">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[300px] overflow-y-auto">
                      <SelectItem value="all">All Categories</SelectItem>
                      {allTags.map((tag) => (
                        <SelectItem key={tag} value={tag}>
                          {tag}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={sortBy}
                    onValueChange={(value) =>
                      setSortBy(value as "date" | "rating")
                    }
                  >
                    <SelectTrigger className="bg-white border-gray-200 rounded-lg h-12 text-sm shadow-sm transition-all duration-300 hover:shadow-md">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date">Latest First</SelectItem>
                      <SelectItem value="rating">Highest Rated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              {/* Reviews content with improved loading state */}
              <div className="min-h-[500px]">
                <div className="w-full">
                  {isLoadingReviews ? (
                    <div className="flex flex-col justify-center items-center h-64 space-y-4">
                      <div className="relative">
                        <div className="animate-spin rounded-full h-12 w-12 border-2 border-t-blue-600 border-r-blue-600 border-b-transparent border-l-transparent"></div>
                        <div className="animate-ping absolute inset-0 rounded-full h-12 w-12 border border-blue-400 opacity-20"></div>
                      </div>
                      <p className="text-gray-500 font-medium">Loading reviews...</p>
                    </div>
                  ) : filteredReviews?.length === 0 ? (
                    <div className="flex flex-col justify-center items-center h-64 space-y-4 text-center">
                      <div className="bg-gray-100 rounded-full p-4">
                        <FileX className="h-8 w-8 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-gray-700 font-medium text-lg">No reviews found</p>
                        <p className="text-gray-500">Try changing your search or filter criteria</p>
                      </div>
                    </div>
                  ) : (
                    <div className="grid gap-5 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                      {filteredReviews?.map((review, index) => (
                        <Link
                          key={review.id}
                          href={`/fr?id=${review.id}&productid=${review.productId}`}
                          className="block group transform transition-all duration-300 hover:translate-y-[-4px]"
                          style={{ animationDelay: `${index * 100}ms` }}
                        >
                          <Card className="h-full hover:shadow-xl transition-all duration-300 border-gray-200 group-hover:border-blue-200 overflow-hidden">
                            <CardContent className="p-5">
                              <div className="flex items-start gap-3 mb-3">
                                <div className="relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                                  <Image
                                    src={
                                      review.product?.display_image ||
                                      "/images/default-product.png"
                                    }
                                    alt={review.title || "Product image"}
                                    fill
                                    className="object-cover"
                                    onError={(e) => {
                                      const target =
                                        e.target as HTMLImageElement;
                                      target.src =
                                        "/images/default-product.png";
                                    }}
                                  />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-semibold line-clamp-2 text-gray-900 leading-tight mb-1">
                                    {review.title}
                                  </h4>
                                  <p className="text-sm text-gray-600 line-clamp-1">
                                    {review.product?.name}
                                  </p>
                                </div>
                              </div>
                              
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-1">
                                    <div className="flex text-yellow-400">
                                      {[...Array(5)].map((_, i) => (
                                        <span key={i} className="text-sm">
                                          {i < review.rating ? "★" : "☆"}
                                        </span>
                                      ))}
                                    </div>
                                    <span className="text-sm font-medium text-gray-700 ml-1">
                                      {review.rating}/5
                                    </span>
                                  </div>
                                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                    {review.comments?.length || 0} comments
                                  </span>
                                </div>
                                
                                {review.product?.tags && review.product.tags.length > 0 && (
                                  <div className="flex flex-wrap gap-1">
                                    {review.product.tags.slice(0, 2).map((tag) => (
                                      <span
                                        key={tag}
                                        className="px-2 py-1 bg-blue-50 text-blue-700 rounded-md text-xs font-medium"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                  </div>
                                )}
                                
                                <p className="text-xs text-gray-500">
                                  {dayjs(review.createdDate).fromNow()}
                                </p>
                              </div>
                            </CardContent>
                          </Card>
                        </Link>
                      ))}
                      {(!filteredReviews || filteredReviews.length === 0) && (
                        <div className="col-span-full text-center py-12">
                          <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No reviews found</h3>
                          <p className="text-gray-500">
                            {searchTerm || selectedTag 
                              ? "Try adjusting your search or filters" 
                              : "No reviews have been written yet"}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="comments" className="animate-fadeIn">
            <div className="space-y-6 pt-4">
              <div className="min-h-[500px]">
                <div className="w-full">
                  {isLoadingComments ? (
                    <div className="flex flex-col justify-center items-center h-64 space-y-4">
                      <div className="relative">
                        <div className="animate-spin rounded-full h-12 w-12 border-2 border-t-blue-600 border-r-blue-600 border-b-transparent border-l-transparent"></div>
                        <div className="animate-ping absolute inset-0 rounded-full h-12 w-12 border border-blue-400 opacity-20"></div>
                      </div>
                      <p className="text-gray-500 font-medium">Loading comments...</p>
                    </div>
                  ) : filteredComments?.length === 0 ? (
                    <div className="flex flex-col justify-center items-center h-64 space-y-4 text-center">
                      <div className="bg-gray-100 rounded-full p-4">
                        <MessageCircle className="h-8 w-8 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-gray-700 font-medium text-lg">No comments yet</p>
                        <p className="text-gray-500">Comments will appear here when available</p>
                      </div>
                    </div>
                  ) : (
                    <div className="grid gap-5 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                      {filteredComments?.map((comment, index) => (
                        <Link
                          key={comment.id}
                          href={`/fr?id=${comment.review?.id}&productid=${comment.review?.productId}`}
                          className="block group transform transition-all duration-300 hover:translate-y-[-4px]"
                          style={{ animationDelay: `${index * 100}ms` }}
                          aria-label={`View comment on ${comment.review?.title || 'review'}`}
                        >
                          <Card className="h-full hover:shadow-xl transition-all duration-300 border-gray-200 group-hover:border-blue-200 overflow-hidden">
                            <CardContent className="p-5">
                              <p className="line-clamp-4 text-gray-700 leading-relaxed mb-4 text-sm sm:text-base">
                                {comment.body}
                              </p>
                              <div className="flex justify-between items-center text-xs">
                                <span className="text-gray-500 font-medium">{dayjs(comment.createdDate).fromNow()}</span>
                                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full font-medium hover:bg-gray-200 transition-colors">
                                  {comment.replies?.length || 0} replies
                                </span>
                              </div>
                            </CardContent>
                          </Card>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="likes" className="animate-fadeIn">
            <div className="space-y-6 pt-4">
              <div className="min-h-[500px]">
                <div className="w-full">
                  {isLoadingLikes ? (
                    <div className="flex flex-col justify-center items-center h-64 space-y-4">
                      <div className="relative">
                        <div className="animate-spin rounded-full h-12 w-12 border-2 border-t-blue-600 border-r-blue-600 border-b-transparent border-l-transparent"></div>
                        <div className="animate-ping absolute inset-0 rounded-full h-12 w-12 border border-blue-400 opacity-20"></div>
                      </div>
                      <p className="text-gray-500 font-medium">Loading liked reviews...</p>
                    </div>
                  ) : filteredLikes?.length === 0 ? (
                    <div className="flex flex-col justify-center items-center h-64 space-y-4 text-center">
                      <div className="bg-gray-100 rounded-full p-4">
                        <ThumbsUp className="h-8 w-8 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-gray-700 font-medium text-lg">No liked reviews</p>
                        <p className="text-gray-500">Liked reviews will appear here</p>
                      </div>
                    </div>
                  ) : (
                    <div className="grid gap-5 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                      {filteredLikes?.map((liked, index) => (
                        <Link
                          key={liked.id}
                          href={`/fr?id=${liked.id}&productid=${liked.productId}`}
                          className="block group transform transition-all duration-300 hover:translate-y-[-4px]"
                          style={{ animationDelay: `${index * 100}ms` }}
                          aria-label={`View liked review: ${liked.title || 'Untitled review'}`}
                        >
                          <Card className="h-full hover:shadow-xl transition-all duration-300 border-gray-200 group-hover:border-blue-200 overflow-hidden">
                            <CardContent className="p-5">
                              <div className="flex items-start gap-3 mb-3">
                                <div className="relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100 shadow-sm">
                                  <Image
                                    src={liked.product?.display_image || "/images/default-product.png"}
                                    alt={liked.title || "Product image"}
                                    fill
                                    className="object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.src = "/images/default-product.png";
                                    }}
                                  />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-semibold line-clamp-2 text-gray-900 leading-tight mb-1">
                                    {liked.title}
                                  </h4>
                                  <p className="text-sm text-gray-600 line-clamp-1">
                                    {liked.product?.name}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center justify-between mt-4">
                                <div className="flex items-center gap-1">
                                  <div className="flex text-yellow-400">
                                    {[...Array(5)].map((_, i) => (
                                      <span key={i} className="text-sm">
                                        {i < liked.rating ? "★" : "☆"}
                                      </span>
                                    ))}
                                  </div>
                                  <span className="text-sm font-medium text-gray-700 ml-1">
                                    {liked.rating}/5
                                  </span>
                                </div>
                                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full font-medium">
                                  {dayjs(liked.createdDate).fromNow()}
                                </span>
                              </div>
                            </CardContent>
                          </Card>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
          {isEditable && (
            <TabsContent value="settings" className="animate-fadeIn">
              <div className="space-y-6 pt-4">
                <UserSettings user={user} onUpdateUser={onUpdateUser} />
              </div>
            </TabsContent>
          )}
          {businesses && businesses.length > 0 && (
            <TabsContent value="businesses" className="animate-fadeIn">
              <div className="space-y-6 pt-4">
                <Link href="/mybusinesses">
                  <p>My Businesses</p>
                </Link>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
