# ReviewIt Homepage Design Guide  
_Posh-yet-Corporate refresh – v1.0_  

This document captures the layout patterns, Tailwind utility recipes, and interaction guidelines used in the redesigned homepage so that the same aesthetic can be applied consistently across future pages.

---

## 1. Foundations  

### 1.1 Typography  
| Token | Font | Tailwind class | Usage |
|-------|------|---------------|-------|
| `font-sans` | Poppins | `font-sans` (default) | Body text |
| `h1`        | Poppins - SemiBold 700 | `text-5xl md:text-6xl` | Page heroes |
| `h2`        | Poppins - Bold 600    | `text-4xl`             | Section titles |
| `h3`        | Poppins - Medium 500  | `text-2xl`             | Card / feature headings |
| `body`      | Poppins - Regular 400 | `text-base`            | Paragraphs |

### 1.2 Colour Palette  
| Semantic name | HSL / Hex | Tailwind var |
|---------------|-----------|--------------|
| **Primary**   | `#1E88E5` | `--myTheme-primary` (already exists) |
| **Primary-Light** | `#42A5F5` | `--myTheme-secondary` |
| **Navy-Dark** | `#002D62` | `--myTheme-navy` (_new_) |
| **Gradient-BG** | radial #F8FAFF → #FFFFFF | `bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-[#F8FAFF] to-white` |
| **Hairline Border** | `#E5EAF5` | `border-hairline` (_custom class_) |

Add to `globals.css`:  
```css
:root {
  --myTheme-navy: 0 45 98;
  --hairline: 229 234 245;
}
.border-hairline {
  @apply border border-[color:rgb(var(--hairline))];
}
```

### 1.3 Spacing Scale (8-pt)  
| Step | rem | Example utility |
|------|-----|-----------------|
| 1    | 0.5 | `p-2` |
| 2    | 1   | `p-4` |
| 3    | 1.5 | `p-6` |
| …    | …   | … |

Set `max-w-7xl mx-auto px-4 sm:px-6 lg:px-8` on outer containers.

---

## 2. Section Patterns  

### 2.1 Hero Section  

```tsx
<DiagonalSection className="bg-gradient-to-br from-[#F8FAFF] to-white">
  <div className="grid grid-cols-1 md:grid-cols-2 gap-12 pt-24 pb-32">
    {/* Copy */}
    <div className="space-y-8">
      <h1 className="text-5xl md:text-6xl font-semibold tracking-tight">
        Your Voice, <br className="hidden md:block" />Your Choice.
      </h1>
      <p className="text-lg text-slate-600 max-w-md">
        Share real experiences and help Guyana shop smarter.
      </p>
      <div className="flex gap-4">
        <PrimaryButton href="/write-review">Write a Review</PrimaryButton>
        <GhostButton href="/browse">Browse Reviews</GhostButton>
      </div>
    </div>

    {/* Visual */}
    <motion.div
      initial={{ y: 40, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      transition={{ type: "spring", stiffness: 60 }}
      className="relative w-full h-80"
    >
      <Image src="/mockups/devices.png" fill priority />
    </motion.div>
  </div>
</DiagonalSection>
```

Key ingredients  
1. `<DiagonalSection>` adds an angled SVG bottom separator (`clip-path: polygon(...)`).  
2. CTA buttons use gradient background and `hover:scale-105` transition.  
3. Mockup floats with Framer-motion parallax on scroll (`useScroll`).

### 2.2 Social-Proof Stripe  

```tsx
<section className="py-6 bg-white border-hairline">
  <div className="flex items-center justify-center gap-8 opacity-80 grayscale">
    {/* map partner logos */}
  </div>
</section>
```

### 2.3 Value Prop Split  

Utility: `.feature-row`  
```css
.feature-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-12 items-center py-16;
}
```

Alternate background colour every other row with `even:bg-slate-50`.

### 2.4 Metrics Band  

```tsx
<section className="bg-[color:rgb(var(--myTheme-navy))] text-white py-20">
  <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 text-center">
    <Metric value={8742} label="Reviews" />
    <Metric value={1250} label="Businesses" />
    <Metric value={63000} label="Monthly Visitors" />
  </div>
</section>
```

`Metric` uses `react-countup`.

### 2.5 Card Carousel  

Wrapper uses `keen-slider`.  
```tsx
<CarouselWrapper>
  {items.map(item => <ReviewCard key={item.id} {...item} />)}
</CarouselWrapper>
```

`ReviewCard` styling  
```css
.review-card {
  @apply bg-white rounded-xl border-hairline p-6 shadow-lg transition
         hover:-translate-y-1 hover:shadow-xl;
}
```

---

## 3. Components & Utilities  

| Component | Purpose |
|-----------|---------|
| `DiagonalSection` | Handles angled separators, accepts extra className |
| `PrimaryButton` / `GhostButton` | Abstract CTA styles |
| `CarouselWrapper` | Generic `keen-slider` wrapper |
| `SocialProof` | Logo bar, reusable anywhere |
| `MetricsBand` | Counters on dark background |

---

## 4. Micro-Interactions  

| Element | Motion | Implementation |
|---------|--------|---------------|
| Section on scroll | `fadeUp` (opacity 0→1, y 40→0) | `<motion.div whileInView={{...}}>` |
| Card hover | `translate-y-1`, `shadow-xl` | Tailwind transition |
| CTA hover | Gradient angle shift via `bg-[length]` & `transition-[background]` |
| Bubble background | `<div className="absolute inset-0 pointer-events-none animate-bubbles" />` with keyframes |

Respect `prefers-reduced-motion` by disabling `motion` props.

---

## 5. Accessibility & Performance Checklist  

- All interactive elements have `:focus-visible` outline.  
- Colour contrast AA verified (≥ 4.5:1 normal text).  
- `next/image` with `placeholder="blur"` for hero mockups.  
- Reserve height for images to prevent CLS.  
- LCP target < 2.5 s, use `priority` on top hero image.  

---

## 6. Dependencies  

```bash
npm i framer-motion keen-slider react-countup
```

Peer dependency: `"keen-slider": "^6"`.

---

## 7. File-level Change Map  

| New file | Location |
|----------|----------|
| `components/DiagonalSection.tsx` | Layout helper |
| `components/SocialProof.tsx` | Logos bar |
| `components/MetricsBand.tsx` | Counter band |
| `components/CarouselWrapper.tsx` | Slider |
| `styles/animations.css` | Keyframes (bubbles) |

Existing components to update: `HeroSection`, `TopReviews`, `QuickTabs`, `TopReviewers`, `ReviewCategories`, `ValueProposition`, footer.

---

## 8. Reuse across pages  

1. **Import `DiagonalSection`** anywhere a diagonal separator is needed.  
2. Apply `.feature-row` for any image-copy split.  
3. Wrap collections in `CarouselWrapper` for touch-friendly sliders.  
4. Use `MetricsBand` in dashboards or landing pages to display KPI counters.  
5. Maintain the 8-pt spacing scale for all new sections.

---

_End of guide_