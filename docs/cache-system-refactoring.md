# Cache System Refactoring Documentation

## Overview

The ReviewIt caching system has been refactored to improve architecture, maintainability, and separation of concerns. This document outlines the changes made and provides guidance for developers working with the cache system.

## Key Changes

### 1. New Cache Module Structure

The cache system has been moved from `src/app/util/analytics/cache.ts` to a dedicated module in `src/app/lib/cache/`:

```
src/app/lib/cache/
├── index.ts          # Main exports
├── keys.ts           # Cache key definitions
├── stats.ts          # Performance tracking & circuit breaker
├── operations.ts     # Safe cache operations
└── invalidation.ts   # Cache invalidation functions
```

### 2. Improved Separation of Concerns

Previously, the cache system was embedded within the analytics module, despite being used throughout the application. The refactoring:

- Moves cache functionality to a proper location in `/lib/cache/`
- Separates different cache concerns into dedicated files
- Maintains backward compatibility through redirects

### 3. Enhanced Product Claim Cache Invalidation

The product claim approval process now properly invalidates all relevant caches:

```typescript
// In src/app/api/admin/review-claim/route.ts
if (status === 'APPROVED') {
  // Invalidate product-specific caches
  await invalidateProductCache(claim.productId);
  
  // Invalidate all products cache since ownership status affects listings
  await invalidateAllProductsCache();
  
  // Invalidate search cache since ownership affects search results
  await invalidateSearchCache();
  
  // Invalidate caches related to product ownership change
  await invalidateCachesOnOwnershipChange(claim.productId, result.product?.businessId || undefined);
  
  // If a business was created or updated, invalidate business caches
  if (result.product?.businessId) {
    await invalidateBusinessCaches(result.product.businessId);
  }
}
```

## Cache Key Structure

All cache keys follow the pattern: `reviewit:v2:{category}:{...params}`

### Main Product Cache Keys

| Cache Key | Description | Invalidation Function |
|-----------|-------------|------------------------|
| `reviewit:v2:all_products` | All products listing | `invalidateAllProductsCache()` |
| `reviewit:v2:all_products:_weighted` | Weighted products listing | `invalidateAllProductsCache()` |
| `reviewit:v2:product_details:{productId}` | Individual product details | `invalidateProductCache(productId)` |
| `reviewit:v2:product_search:{hashedQuery}` | Product search results | `invalidateSearchCache()` |
| `reviewit:v2:product_reviews:{productId}:{isPublic}` | Product reviews | `invalidateProductCache(productId)` |

### Admin Cache Keys

| Cache Key | Description | Invalidation Function |
|-----------|-------------|------------------------|
| `reviewit:v2:admin:recent_reviews` | Recent reviews for admin | `invalidateAdminCache()` |
| `reviewit:v2:admin:review_stats` | Review statistics | `invalidateAdminCache()` |
| `reviewit:v2:admin:dashboard_metrics` | Admin dashboard metrics | `invalidateAdminCache()` |
| `reviewit:v2:admin:reports` | Admin reports | `invalidateAdminCache()` |
| `reviewit:v2:admin:report_stats` | Report statistics | `invalidateAdminCache()` |

### Review Cache Keys

| Cache Key | Description | Invalidation Function |
|-----------|-------------|------------------------|
| `reviewit:v2:reviews:latest` | Latest reviews | `invalidateReviewCaches()` |
| `reviewit:v2:reviews:popular` | Popular reviews | `invalidateReviewCaches()` |
| `reviewit:v2:reviews:trending` | Trending reviews | `invalidateReviewCaches()` |
| `reviewit:v2:comments:review:{reviewId}` | Review comments | `invalidateCommentCache(reviewId)` |

## Using the Cache System

### Importing Cache Functions

```typescript
// Import from the new location
import { 
  invalidateProductCache, 
  invalidateAllProductsCache,
  invalidateSearchCache,
  // other functions...
} from '@/app/lib/cache';
```

### Cache Operations

```typescript
// Safe cache operations with circuit breaker
import { safeGetFromCache, safeSetToCache } from '@/app/lib/cache';

// Get from cache
const data = await safeGetFromCache<MyType>(cacheKey);

// Set in cache (with 1 hour TTL)
await safeSetToCache(cacheKey, value, 3600);
```

### Cache Invalidation

```typescript
// Product-specific cache invalidation
await invalidateProductCache(productId);

// All products cache invalidation
await invalidateAllProductsCache();

// Search cache invalidation
await invalidateSearchCache();

// Batch invalidation with retry logic
await batchInvalidateCache(keysArray);
```

### Cache Health Monitoring

```typescript
// Check cache health
import { checkCacheHealth } from '@/app/lib/cache';

const healthStatus = await checkCacheHealth();
console.log(healthStatus.isHealthy, healthStatus.stats);
```

## When to Invalidate Cache

### Product Changes

- **Product Creation**: Invalidate `allProducts` and `search` caches
- **Product Update**: Invalidate `productDetails`, `allProducts`, and `search` caches
- **Product Deletion**: Invalidate `productDetails`, `allProducts`, and `search` caches

### Review Changes

- **Review Creation**: Invalidate `productDetails`, `productReviews`, and aggregated caches
- **Review Update**: Invalidate `productReviews` and aggregated caches
- **Review Approval**: Invalidate `productReviews` and aggregated caches

### Ownership Changes

- **Product Claim Approval**: Invalidate `productDetails`, `allProducts`, `search`, and business caches
- **Business Creation/Update**: Invalidate business-specific caches

## Backward Compatibility

For backward compatibility, the old import paths still work:

```typescript
// These still work but are redirected to the new module
import { invalidateProductCache } from '@/app/util/analytics/cache';
import { invalidateAllProductsCache } from '@/app/util/databaseAnalytics';
```

However, it's recommended to update to the new import paths for clarity and maintainability.

## Cache Circuit Breaker

The cache system includes a circuit breaker pattern to prevent cascading failures:

- After 5 consecutive cache failures, the circuit opens
- While open, cache operations return null immediately without hitting Redis
- After 30 seconds, the circuit attempts to close again
- If successful, normal cache operations resume

This ensures that Redis failures don't impact application performance.

## Best Practices

1. **Use Specific Invalidation**: Invalidate only what's necessary, not everything
2. **Batch Operations**: Use `batchInvalidateCache()` for multiple keys
3. **Error Handling**: Cache operations should never throw exceptions
4. **Monitoring**: Check cache health regularly
5. **TTL Management**: Set appropriate TTLs based on data volatility

## Future Improvements

1. **Cache Warming**: Implement proactive cache population
2. **Metrics Dashboard**: Create admin UI for cache performance visualization
3. **Distributed Cache Invalidation**: Support for multi-instance invalidation
4. **Cache Versioning**: Enhanced schema change support