# ReviewIt Caching System Documentation for Administrators

## Overview

ReviewIt uses a sophisticated caching system to improve performance and reduce database load. This document provides information for administrators about the caching system, including how to manage and troubleshoot cache-related issues.

## Cache Architecture

The ReviewIt caching system is built on Redis and follows these principles:

1. **Centralized Management**: All cache operations are managed through the `@/app/lib/cache` module
2. **Versioned Keys**: Cache keys include version information to handle schema changes
3. **Circuit Breaker Pattern**: Automatic fallback when Redis is unavailable
4. **Event-Based Invalidation**: Cache is invalidated based on specific business events

## Cache Key Structure

All cache keys follow the pattern: `reviewit:v2:{category}:{...params}`

### Main Cache Categories

- **Product Cache**: Product listings, details, and search results
- **Review Cache**: Review listings, comments, and aggregated statistics
- **Admin Cache**: Dashboard metrics, reports, and administrative data
- **Business Cache**: Business-specific analytics and data

## Admin Cache Management

### Cache Invalidation API Endpoints

Administrators can use the following API endpoints to manage the cache:

```
POST /api/admin/cache/invalidate
```

**Request Body:**
```json
{
  "action": "invalidate-all-products" | "invalidate-search" | 
           "invalidate-admin" | "invalidate-reviews" | "invalidate-all"
}
```

**Actions:**
- `invalidate-all-products`: Clears product listing caches
- `invalidate-search`: Clears product search caches
- `invalidate-admin`: Clears admin dashboard caches
- `invalidate-reviews`: Clears review listing caches
- `invalidate-all`: Clears all application caches (preserves analytics)

### Cache Health Monitoring

Administrators can check cache health using:

```
GET /api/admin/cache/health
```

**Response:**
```json
{
  "isHealthy": true,
  "stats": {
    "hits": 1250,
    "misses": 320,
    "hitRate": 79.6,
    "totalRequests": 1570,
    "errors": 2,
    "lastError": "2023-06-15T14:32:10.123Z"
  },
  "circuitBreakerStatus": {
    "isOpen": false,
    "failureCount": 0,
    "lastFailureTime": null,
    "threshold": 5,
    "timeout": 30000
  }
}
```

### Cache Testing

Administrators can test cache functionality using:

```
POST /api/admin/cache/test
```

**Request Body:**
```json
{
  "action": "test-cache-operations" | "test-invalidation" | 
           "test-batch-invalidation" | "health-check"
}
```

## Common Cache Scenarios

### Product Claim Approval

When a product claim is approved:

1. Product-specific caches are invalidated
2. All products cache is invalidated (ownership affects listings)
3. Search cache is invalidated (ownership affects search results)
4. Business caches are invalidated if applicable

### Review Moderation

When reviews are approved or rejected:

1. Product review caches are invalidated
2. Admin dashboard metrics are updated
3. Latest/popular/trending review caches are refreshed

### Product Updates

When products are updated:

1. Product details cache is invalidated
2. All products cache is invalidated
3. Search cache is invalidated

## Troubleshooting

### Common Issues

#### Stale Data

If users report seeing outdated information:

1. Identify the affected data type (product, review, etc.)
2. Use the appropriate cache invalidation endpoint
3. Verify the data is refreshed

#### Cache Health Issues

If the cache health endpoint reports problems:

1. Check Redis server status
2. Verify network connectivity to Redis
3. Check for memory pressure on Redis server
4. Reset the circuit breaker if necessary

#### High Miss Rate

If the cache hit rate is consistently low:

1. Check TTL settings for frequently accessed data
2. Consider implementing cache warming strategies
3. Review cache invalidation patterns for over-invalidation

### Emergency Cache Reset

In case of severe cache issues, administrators can clear all caches:

```
POST /api/admin/cache/invalidate
{
  "action": "invalidate-all"
}
```

**Note:** This preserves username restrictions analytics data but clears all other application caches.

## Best Practices

1. **Selective Invalidation**: Use specific invalidation actions rather than clearing all caches
2. **Regular Monitoring**: Check cache health metrics regularly
3. **Scheduled Maintenance**: Consider scheduled cache refreshes during low-traffic periods
4. **Gradual Changes**: Make incremental changes to cache TTLs and strategies

## Recent Changes

The caching system was recently refactored to improve architecture and maintainability:

1. **Moved from Analytics**: Cache system moved from `analytics/cache.ts` to `lib/cache/`
2. **Modular Structure**: Split into keys, stats, operations, and invalidation modules
3. **Enhanced Product Claims**: Improved cache invalidation for product ownership changes
4. **Better Organization**: Centralized cache key definitions and invalidation logic

For more details, see `docs/cache-system-refactoring.md`.