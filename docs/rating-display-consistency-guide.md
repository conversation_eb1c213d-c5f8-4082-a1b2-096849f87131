# Rating Display Consistency Guide

## Overview

This guide ensures consistent rating display across the entire application by enforcing the minimum review threshold rule: **ratings should only be displayed when a product has 5 or more reviews**.

## The Rule

- **Minimum Reviews Required**: 5 reviews (configurable via `MINIMUM_REVIEWS` constant)
- **Before Minimum**: Show empty stars with message indicating more reviews needed
- **After Minimum**: Show actual rating with star display

## Implementation

### ✅ Correct Implementation

Use the `RatingDisplayWithThreshold` component for all rating displays:

```tsx
import RatingDisplayWithThreshold from '@/app/components/RatingDisplayWithThreshold';
import { calculateWeightedRating } from '@/app/util/calculateWeightedRating';

// In your component:
<RatingDisplayWithThreshold
  ratingData={calculateWeightedRating(product.reviews || [], {
    globalAverageRating: product.rating
  })}
  size="lg"
  showReviewCount={true}
  showConfidence={false}
  minimumReviewsMessage="Not enough reviews for rating yet"
  className=""
/>
```

### ❌ Incorrect Implementation

Direct rating display without minimum review validation:

```tsx
// DON'T DO THIS:
{rating > 0 && (
  <div className="flex items-center">
    {[...Array(5)].map((_, i) => (
      <Star className={i < Math.round(rating) ? 'filled' : 'empty'} />
    ))}
    <span>{rating.toFixed(1)} ({totalReviews} reviews)</span>
  </div>
)}
```

## Component Status

### ✅ Components Following the Rule

1. **ProductCard** (`/src/app/components/ProductCard.tsx`)
   - Uses proper minimum review validation
   - Shows "X more needed for rating" message

2. **MiniProductCard** (`/src/app/components/MiniProductCard.tsx`)
   - Uses `RatingDisplayWithThreshold`
   - Properly handles weighted ratings

3. **ProductCardSlim** (`/src/app/components/ProductCardSlim.tsx`)
   - Uses `RatingDisplayWithThreshold`
   - Consistent with other card components

4. **BusinessProductCard** (`/src/components/BusinessProductCard.tsx`)
   - Uses `RatingDisplayWithThreshold`
   - Proper business dashboard integration

5. **RatingDistribution** (`/src/components/product/RatingDistribution.tsx`)
   - Imports and uses `MINIMUM_REVIEWS` constant
   - Validates before showing distribution

6. **RatingSummaryWidget** (`/src/app/components/widgets/RatingSummaryWidget.tsx`)
   - Uses `RatingDisplayWithThreshold`
   - Widget for displaying product rating summary

7. **MiniReviewWidget** (`/src/app/components/widgets/MiniReviewWidget.tsx`)
   - Uses `RatingDisplayWithThreshold`
   - Widget for displaying mini product reviews

8. **BusinessCardWidget** (`/src/app/components/widgets/BusinessCardWidget.tsx`)
   - Uses `RatingDisplayWithThreshold`
   - Widget for business card display

9. **TrustBadgeWidget** (`/src/app/components/widgets/TrustBadgeWidget.tsx`)
   - Uses `RatingDisplayWithThreshold`
   - Widget for trust badge display

10. **ReviewPopupWidget** (`/src/app/components/widgets/ReviewPopupWidget.tsx`)
    - Uses `RatingDisplayWithThreshold` for product ratings
    - Widget for review popup display

11. **DeletedProductsList** (`/src/app/owner-admin/components/DeletedProductsList.tsx`)
    - Uses `RatingDisplayWithThreshold`
    - Admin component for deleted products

### ✅ Recently Fixed

6. **GrandProductCard** (`/src/app/components/GrandProductCard.tsx`)
   - **FIXED**: Now uses `RatingDisplayWithThreshold`
   - Used on `/fr` route (Full Review Page)

## Configuration

The minimum review threshold is configurable:

```typescript
// /src/app/config/rating.ts
export const MINIMUM_REVIEWS = 5; // Can be overridden via PUBLIC_MINIMUM_REVIEWS env var
```

## Routes and Usage

- **Home Page** (`/`): Uses ProductCard components ✅
- **Browse Page** (`/browse`): Uses ProductCard components ✅
- **Full Review Page** (`/fr`): Uses GrandProductCard ✅ (Fixed)
- **Product Pages** (`/product/[id]`): Uses various card components ✅
- **Business Dashboard** (`/owner-admin`): Uses BusinessProductCard ✅

## Best Practices

1. **Always use `RatingDisplayWithThreshold`** for any rating display
2. **Import `MINIMUM_REVIEWS`** from config when needed
3. **Use `calculateWeightedRating`** for consistent rating calculations
4. **Provide meaningful messages** when minimum not met
5. **Test with products having < 5 reviews** to verify behavior

## Testing Checklist

When adding new rating displays:

- [ ] Component uses `RatingDisplayWithThreshold`
- [ ] Shows empty stars when < 5 reviews
- [ ] Shows helpful message about needing more reviews
- [ ] Displays actual rating when ≥ 5 reviews
- [ ] Handles edge cases (0 reviews, null data)

## Migration Guide

To fix components not following the rule:

1. Import required dependencies:
   ```tsx
   import RatingDisplayWithThreshold from '@/app/components/RatingDisplayWithThreshold';
   import { calculateWeightedRating } from '@/app/util/calculateWeightedRating';
   ```

2. Replace direct rating display with:
   ```tsx
   <RatingDisplayWithThreshold
     ratingData={calculateWeightedRating(reviews || [], { globalAverageRating: rating })}
     size="lg"
     showReviewCount={true}
     showConfidence={false}
     minimumReviewsMessage="Not enough reviews for rating yet"
   />
   ```

3. Remove manual star rendering and rating validation logic

## Recent Updates (Migration Complete)

The following components have been successfully migrated to use the centralized rating system:

- ✅ `RatingSummaryWidget.tsx` - Updated to use `RatingDisplayWithThreshold`
- ✅ `MiniReviewWidget.tsx` - Updated to use `RatingDisplayWithThreshold`
- ✅ `BusinessCardWidget.tsx` - Updated to use `RatingDisplayWithThreshold`
- ✅ `TrustBadgeWidget.tsx` - Updated to use `RatingDisplayWithThreshold`
- ✅ `ReviewPopupWidget.tsx` - Updated to use `RatingDisplayWithThreshold` for product ratings
- ✅ `DeletedProductsList.tsx` - Updated to use `RatingDisplayWithThreshold`

All main product rating displays now consistently follow the 5-review minimum threshold rule.

### Components with Acceptable Exceptions

These components display ratings but have valid reasons for not using the threshold:

- `StarRating.tsx` - Generic star rating component for individual reviews
- `AllReviews.tsx` - Displays individual review ratings
- `ReviewGridWidget.tsx` - Displays individual review ratings in grid format
- `ReviewCarouselWidget.tsx` - Displays individual review ratings in carousel format
- `UserContentTabs.tsx` - Displays individual review ratings in user content
- API routes (`/api/og/route.tsx`, `/api/og/product/route.tsx`) - For Open Graph meta tags

## Conclusion

By following this guide, the entire application maintains consistent rating display behavior, ensuring users only see ratings when there's sufficient data to make them meaningful. The `/fr` route inconsistency has been resolved, and all components now follow the same minimum review threshold rule.