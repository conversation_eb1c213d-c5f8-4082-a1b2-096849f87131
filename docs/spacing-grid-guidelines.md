# Spacing & Grid Guidelines  
_ReviewIt Homepage Refresh – v1.0_  

These guidelines document the 8-pt spacing scale and responsive grid conventions introduced during the homepage redesign. Follow them when building new pages or refactoring legacy layouts to ensure visual consistency.  

---  

## 1. Spacing Scale (8-pt)  

Spacing values increase in **4 px** increments on small steps and **8 px** increments on core steps. All Tailwind utilities map directly to the scale.  

| Step | Pixels | rem | Tailwind | Typical Usage |
|-----:|-------:|----:|----------|---------------|
| 0 | 0 | 0 | `p-0` `m-0` | Zero offset |
| 1 | 4 | 0.25 | `p-1` `m-1` | Icon padding, chip gap |
| 2 | 8 | 0.5 | `p-2` `m-2` | Tight button padding |
| 3 | 12 | 0.75 | `p-3` `m-3` | Avatar offset |
| 4 | 16 | 1 | `p-4` `m-4` | Card inner padding |
| 5 | 20 | 1.25 | `p-5` `m-5` | Cluster gaps (e.g. button groups) |
| 6 | 24 | 1.5 | `p-6` `m-6` | Section inner padding (mobile) |
| 7 | 32 | 2 | `p-8` `m-8` | Section top / bottom (tablet) |
| 8 | 40 | 2.5 | `p-10` `m-10` | Section top / bottom (desktop) |
| 9 | 48 | 3 | `p-12` `m-12` | Hero & footer vertical rhythm |

**Tips**

1. Prefer utility classes (`gap-6`, `space-y-8`) over ad-hoc CSS.  
2. Never hard-code pixel margins in components; rely on scale steps.  
3. Use `lg:py-20` only for large, visually isolated bands (Hero, Metrics).  

---  

## 2. Container & Gutters  

The site uses a centred, **max-width of 1280 px** (`max-w-7xl`) with fluid side-padding.  

```html  
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> … </div>  
```  

| Breakpoint | Side Padding | Rationale |
|-----------|--------------|-----------|
| Base | `px-4` (16 px) | Minimum safe gutters on mobile |
| `sm` ≥640 px | `px-6` (24 px) | Comfortable reading width |
| `lg` ≥1024 px | `px-8` (32 px) | Aligns with desktop nav grid |

The same container pattern should wrap every top-level section except full-bleed bands (e.g. MetricsBand, diagonal separators).  

---  

## 3. Grid System  

Tailwind’s `grid` utilities provide responsive column counts without custom CSS. Adopt the following presets:  

| Pattern | Mobile | Tablet (`md`) | Desktop (`lg`) | Example Component |
|---------|--------|---------------|----------------|-------------------|
| Basic feature row | 1 col | 2 cols | 2 cols | ValueProposition |
| Card gallery | 1 col | 2 cols | 3–4 cols | TopReviews |
| Logo strip | Auto-flow | 5 cols | 8 cols | SocialProof |

Use **even gaps** drawn from the spacing scale: `gap-8` on desktops, `gap-6` on tablets, and `gap-4` on phones.  

```css  
/* helpers.css */
.feature-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-12 items-center py-16;
}
```  

---  

## 4. Section Rhythm  

1. Start sections with `pt-24` and finish with `pb-32` by default.  
2. For visually light dividers (hairline border), halve the padding (`py-12`).  
3. Adjacent full-bleed bands should share padding to avoid double whitespace.  

---  

## 5. Implementation Checklist  

- [ ] Wrap every new top-level section in the container pattern.  
- [ ] Select grid column counts from the presets above.  
- [ ] Use spacing scale utilities exclusively; no arbitrary values.  
- [ ] Confirm vertical rhythm with Figma overlay before merging.  

---  

_Last updated: 16 July 2025_