"use client";
import { RocketIcon } from "lucide-react";
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { getUser } from "@/app/util/serverFunctions";
import LoadingSpinner from "@/app/components/LoadingSpinner";
import { iUser } from "@/app/util/Interfaces";
import Link from "next/link";
import { FaPlus } from "react-icons/fa";
import { ownerNotificationsAtom } from "@/app/store/store";
import { useAtomValue } from "jotai";
import BusinessProductCard from "@/components/BusinessProductCard";
import {
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon,
  InfoIcon,
  StarIcon,
  EyeIcon,
  MessageSquareIcon,
  BarChart3Icon,
  AlertCircleIcon,
  ThumbsUpIcon,
  TargetIcon,
  UsersIcon,
  ArrowUpRight,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { isOwnerComment } from "@/app/util/commentHelpers";
import { calculateAverageReviewRatingSync } from "@/app/util/calculateAverageReviewRating";

export function BusinessDashboardComponent() {
  const auth = useAuth();
  const notifications = useAtomValue(ownerNotificationsAtom);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["user", auth.userId],
    queryFn: async () => await getUser(),
    refetchOnWindowFocus: false,
  }) as any;

  // Fetch cached business analytics data (includes optimized rating calculations)
  const { data: analyticsData, isLoading: analyticsLoading } = useQuery({
    queryKey: ["businessAnalytics", data?.data?.businesses?.[0]?.id],
    queryFn: async () => {
      if (!data?.data?.businesses?.[0]?.id) return null;
      const res = await fetch(
        `/api/business/${data.data.businesses[0].id}/analytics`,
      );
      return res.json();
    },
    enabled: !!data?.data?.businesses?.[0]?.id,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 15 * 60 * 1000, // Keep in cache for 15 minutes
  });

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <p>{error?.toString()}</p>;
  if (!data?.data) return <p>No user data available</p>;

  const user: iUser = data.data as iUser;
  if (!user) return <p>Unable to load user profile</p>;

  // This might come in handy later, this keeps tha business umbrella
  //   const businessProducts = user.businesses?.map(business => ({
  //   businessId: business.id,
  //   products: business.products
  // })) || [];

  if ((user.businesses?.length || 0) < 1) {
    return (
      <div className="container mx-auto p-6 bg-gray-100 min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-2xl ">
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center">
              Contribute to Review It
            </CardTitle>
          </CardHeader>
          <CardContent className="text-start">
            <RocketIcon className="w-24 h-24 mx-auto text-blue-500 mb-6" />
            <p className="text-base md:text-xl mb-4">
              Help local businesses thrive by adding them to our platform.
              Anyone can contribute!
            </p>
            <ul className="text-base md:text-xl text-start mb-4 list-disc pl-6 space-y-2">
              <li>Support small businesses in your community</li>
              <li>Help customers discover great local services</li>
              <li>Contribute to a transparent review ecosystem</li>
              <li>Empower businesses with valuable customer feedback</li>
              <li>Foster local economic growth through increased visibility</li>
            </ul>
            <Link
              href="/submit"
              className="bg-blue-600 text-white hover:bg-blue-700 py-3 px-6 rounded-full text-lg font-semibold transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Add a Business
            </Link>
          </CardContent>
          <CardFooter className="text-center text-gray-600">
            Join our community effort to boost local businesses and help them
            reach their full potential!
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Get only products that belong to businesses
  const allProducts = user.businesses?.flatMap((business) => business.products || []) || [];

  // Use cached analytics data when available, fallback to manual calculation
  const totalViews = analyticsData?.totalViews || allProducts.reduce((sum, product) => sum + (product.viewCount || 0), 0);
  const totalReviews = analyticsData?.totalReviews || allProducts.reduce((sum, product) => sum + (product.reviews?.length || 0), 0);
  
  // Use cached average rating from business analytics (already calculated with proper aggregation)
  const averageRating = analyticsData?.averageRating || (() => {
    // Fallback calculation only if analytics data is not available
    const productRatings = allProducts.map(product => {
      if (!product.reviews || product.reviews.length === 0) {
        return { rating: 0, hasReviews: false };
      }
      const ratingData = calculateAverageReviewRatingSync(product.reviews, true);
      return { rating: parseFloat(ratingData.roundedRatingOneDecimalPlace), hasReviews: true };
    });
    
    const productsWithReviews = productRatings.filter(p => p.hasReviews);
    return productsWithReviews.length > 0 
      ? productsWithReviews.reduce((sum, p) => sum + p.rating, 0) / productsWithReviews.length
      : 0;
  })();

  // Additional derived metrics for dashboard (lightweight client-side calculations)
  const now = new Date();
  const recentReviews = allProducts.reduce((sum, product) => sum + (product.reviews?.filter(review => (now.getTime() - new Date(review.createdDate!).getTime()) / (1000 * 60 * 60 * 24) <= 30).length || 0), 0);

  const lastReviewDate = allProducts
    .flatMap((p) => p.reviews || [])
    .sort((a, b) => new Date(b.createdDate!).getTime() - new Date(a.createdDate!).getTime())[0]?.createdDate;

  const daysSinceLastReview = lastReviewDate
    ? Math.floor((now.getTime() - new Date(lastReviewDate).getTime()) / (1000 * 60 * 60 * 24))
    : null;

  const engagementRate = totalReviews > 0 ? totalViews / totalReviews : null;

  

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="text-center mb-10">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
            Your Business Dashboard
          </h1>
          <p className="mt-3 text-lg text-slate-600 max-w-2xl mx-auto">
            Monitor your business performance, track customer feedback, and grow your online presence
          </p>
          <p className="mt-1 text-sm text-slate-500 max-w-2xl mx-auto">
            Analytics data refreshes daily at 2:00&nbsp;AM&nbsp;UTC.
          </p>
        </div>

        {/* Business Performance Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                <EyeIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">Total Views</p>
                <p className="text-2xl font-bold text-slate-900">{totalViews.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl shadow-lg">
                <MessageSquareIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">Total Reviews</p>
                <p className="text-2xl font-bold text-slate-900">{totalReviews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-yellow-500 to-yellow-600 p-3 rounded-xl shadow-lg">
                <StarIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">Average Rating</p>
                <p className="text-2xl font-bold text-slate-900">{averageRating > 0 ? averageRating.toFixed(1) : "N/A"}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-red-500 to-red-600 p-3 rounded-xl shadow-lg">
                <AlertCircleIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">Needs Attention</p>
                <p className="text-2xl font-bold text-slate-900">{allProducts.filter(product => {
                  if (!product.reviews || product.reviews.length === 0) return false;
                  const recentReviews = product.reviews.slice(-3);
                  if (recentReviews.length === 0) return false;
                  const recentAverage = recentReviews.reduce((sum, review) => sum + (review.rating || 0), 0) / recentReviews.length;
                  return recentAverage < 3;
                }).length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Metrics Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-indigo-500 to-indigo-600 p-3 rounded-xl shadow-lg">
                <MessageSquareIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">New Reviews (30d)</p>
                <p className="text-2xl font-bold text-slate-900">{recentReviews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                <ClockIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">Days Since Last Review</p>
                <p className="text-2xl font-bold text-slate-900">{daysSinceLastReview !== null ? daysSinceLastReview : "N/A"}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-teal-500 to-teal-600 p-3 rounded-xl shadow-lg">
                <BarChart3Icon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">Views per Review</p>
                <p className="text-2xl font-bold text-slate-900">{engagementRate !== null ? engagementRate.toFixed(1) : "N/A"}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 p-3 rounded-xl shadow-lg">
                <TrendingUpIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-600">Growth Trend (30d)</p>
                <p className="text-2xl font-bold text-slate-900">
                  {(() => {
                    // Calculate growth trend based on reviews over the last 30 days
                    const now = new Date();
                    const thirtyDaysAgo = new Date();
                    thirtyDaysAgo.setDate(now.getDate() - 30);
                    const sixtyDaysAgo = new Date();
                    sixtyDaysAgo.setDate(now.getDate() - 60);
                    
                    // Get all reviews
                    const allReviews = allProducts.flatMap(product => product.reviews || []);
                    
                    // Count reviews in the last 30 days
                    const recentReviews = allReviews.filter(review => {
                      const reviewDate = new Date(review.createdDate!);
                      return reviewDate >= thirtyDaysAgo && reviewDate <= now;
                    }).length;
                    
                    // Count reviews in the previous 30 days
                    const previousPeriodReviews = allReviews.filter(review => {
                      const reviewDate = new Date(review.createdDate!);
                      return reviewDate >= sixtyDaysAgo && reviewDate < thirtyDaysAgo;
                    }).length;
                    
                    // Calculate growth percentage
                    let growthPercentage = 0;
                    if (previousPeriodReviews > 0) {
                      growthPercentage = Math.round(((recentReviews - previousPeriodReviews) / previousPeriodReviews) * 100);
                    } else if (recentReviews > 0) {
                      growthPercentage = 100; // If no previous reviews but we have recent ones, that's 100% growth
                    }
                    
                    const prefix = growthPercentage >= 0 ? '+' : '';
                    return `${prefix}${growthPercentage}%`;
                  })()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Business Performance Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent flex items-center gap-2">
              <TargetIcon className="w-6 h-6 text-blue-600" />
              Your Business Performance
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl p-6 border border-blue-200/50 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm font-semibold text-blue-900">Customer Satisfaction</p>
                <div className="bg-blue-500 p-2 rounded-lg">
                  <StarIcon className="w-4 h-4 text-white" />
                </div>
              </div>
              <p className="text-3xl font-bold text-blue-900 mb-2">{averageRating > 0 ? averageRating.toFixed(1) : "N/A"}</p>
              <p className="text-xs text-blue-700 leading-relaxed">
                {averageRating === 0
                  ? "No reviews yet"
                  : averageRating >= 4.0
                  ? "Excellent customer satisfaction!"
                  : averageRating >= 3.5
                    ? "Good customer satisfaction"
                    : "Consider reviewing customer feedback to improve satisfaction"}
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl p-6 border border-green-200/50 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm font-semibold text-green-900">Review Response Rate</p>
                <div className="bg-green-500 p-2 rounded-lg">
                  <MessageSquareIcon className="w-4 h-4 text-white" />
                </div>
              </div>
              {analyticsLoading ? <LoadingSpinner /> : <p className="text-3xl font-bold text-green-900 mb-2">{analyticsData?.negativeReviewResponseRate?.toFixed(1) || 0}%</p>}
              <p className="text-xs text-green-700 leading-relaxed">
                {analyticsData?.negativeReviewResponseRate >= 80
                  ? "Outstanding response rate!"
                  : analyticsData?.negativeReviewResponseRate >= 60
                    ? "Good response rate"
                    : "Try to respond to more reviews to improve engagement"}
              </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl p-6 border border-purple-200/50 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm font-semibold text-purple-900">Response Time</p>
                <div className="bg-purple-500 p-2 rounded-lg">
                  <ClockIcon className="w-4 h-4 text-white" />
                </div>
              </div>
              {analyticsLoading ? <LoadingSpinner /> : <p className="text-3xl font-bold text-purple-900 mb-2">{analyticsData?.averageReviewResponseTime?.toFixed(1) || 0}h</p>}
              <p className="text-xs text-purple-700 leading-relaxed">
                {analyticsData?.averageReviewResponseTime <= 12
                  ? "Excellent response time!"
                  : analyticsData?.averageReviewResponseTime <= 24
                    ? "Good response time"
                    : "Aim to respond within 24 hours to improve customer satisfaction"}
              </p>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-orange-100/50 rounded-xl p-6 border border-orange-200/50 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm font-semibold text-orange-900">Content Freshness</p>
                <div className="bg-orange-500 p-2 rounded-lg">
                  <TrendingUpIcon className="w-4 h-4 text-white" />
                </div>
              </div>
              {analyticsLoading ? <LoadingSpinner /> : <p className="text-3xl font-bold text-orange-900 mb-2">{analyticsData?.productContentFreshnessScore?.toFixed(1) || 0}%</p>}
              <p className="text-xs text-orange-700 leading-relaxed">
                {analyticsData?.productContentFreshnessScore >= 80
                  ? "Excellent content freshness!"
                  : analyticsData?.productContentFreshnessScore >= 60
                    ? "Good content freshness"
                    : "Update your product info regularly to improve your score"}
              </p>
            </div>
          </div>

          <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50">
            <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center gap-2">
              <InfoIcon className="w-5 h-5 text-blue-600" />
              Understanding Your Metrics
            </h3>
            <ul className="text-sm text-blue-800 space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>These metrics help you understand how your business is performing and where you can improve.</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Each metric has specific targets that indicate good performance in your industry.</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Focus on improving metrics that are below target to enhance customer satisfaction.</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Management Links */}
        <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
          <Link
            href="/submit"
            className="flex items-center bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-all duration-300 transform hover:scale-105 shadow-md"
          >
            <FaPlus className="w-4 h-4 mr-2" />
            Add New Business
          </Link>

          <Link
            href="/owner-admin"
            className="flex items-center bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-all duration-300 transform hover:scale-105 shadow-md"
          >
            <RocketIcon className="w-4 h-4 mr-2" />
            Advanced Admin Dashboard
          </Link>
          <Link
            href="/claims"
            className="flex items-center bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-all duration-300 transform hover:scale-105 shadow-md"
          >
            <RocketIcon className="w-4 h-4 mr-2" />
            Claims
          </Link>
        </div>

        {/* Responsive Products Grid - Adjusts based on screen size */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {allProducts.map((product) => (
            <BusinessProductCard
              key={product.id}
              product={product}
              notifications={notifications || []}
            />
          ))}
        </div>

        {/* Show message if no products */}
        {allProducts.length === 0 && (
          <div className="text-center py-8 bg-white rounded-lg shadow-md">
            <p className="text-gray-500 text-lg">No products yet. Add a business to get started.</p>
          </div>
        )}

        {/* Dashboard Legend and Help Section */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <InfoIcon className="w-5 h-5 text-blue-600" />
            Understanding Your Dashboard
          </h2>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Performance Metrics */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Performance Metrics</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 p-2 rounded-lg mt-1">
                    <EyeIcon className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Views</p>
                    <p className="text-sm text-gray-600">Track customer interest and optimize your product visibility. Higher views often correlate with increased sales opportunities.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-green-100 p-2 rounded-lg mt-1">
                    <MessageSquareIcon className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Reviews</p>
                    <p className="text-sm text-gray-600">Customer feedback is crucial for growth. More reviews build trust and provide valuable insights for improvement.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-yellow-100 p-2 rounded-lg mt-1">
                    <StarIcon className="w-4 h-4 text-yellow-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Rating</p>
                    <p className="text-sm text-gray-600">Your overall customer satisfaction score. Aim for 4+ stars to maintain a strong market position.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Trend Indicators */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Trend Indicators</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="bg-green-50 p-2 rounded-lg mt-1 border border-green-200">
                    <TrendingUpIcon className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Trending Up</p>
                    <p className="text-sm text-gray-600">Your product is gaining momentum with positive feedback. Consider expanding inventory or features.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-red-50 p-2 rounded-lg mt-1 border border-red-200">
                    <TrendingDownIcon className="w-4 h-4 text-red-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Needs Attention</p>
                    <p className="text-sm text-gray-600">Address customer concerns promptly. Review recent feedback and implement improvements to maintain quality.</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-gray-50 p-2 rounded-lg mt-1 border border-gray-200">
                    <ClockIcon className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Stable</p>
                    <p className="text-sm text-gray-600">Maintain your current quality while looking for opportunities to enhance customer experience.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Enhanced Tips Section */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900">Business Growth Strategies</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Quick Wins</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start gap-2">
                    <span className="text-green-600">•</span>
                    <span>Respond to reviews within 24 hours to improve your response rate and customer satisfaction.</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-600">•</span>
                    <span>Update product images and descriptions weekly to maintain freshness and SEO ranking.</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-600">•</span>
                    <span>Address negative reviews within 48 hours to prevent reputation damage.</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Long-term Growth</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600">•</span>
                    <span>Analyze competitor reviews to identify market gaps and opportunities.</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600">•</span>
                    <span>Implement a review collection strategy to increase review volume.</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600">•</span>
                    <span>Use customer feedback to guide product development and improvements.</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
