import React from "react";
import { Button } from "../ui/button";
import { SignInButton } from "@clerk/nextjs";

interface SignInToParticipateProps {
  action?: string;
  compact?: boolean;
}

export const SignInToParticipate: React.FC<SignInToParticipateProps> = ({
  action = "participate",
  compact = false,
}) => {
  return (
    <div className={`flex flex-col items-center gap-2 ${compact ? "p-2" : "p-4"}`}>
      <p className={`text-gray-600 ${compact ? "text-sm" : "text-base"}`}>
        Please sign in to {action}
      </p>
      <SignInButton mode="modal">
        <Button variant="outline" size={compact ? "sm" : "default"}>
          Sign In
        </Button>
      </SignInButton>
    </div>
  );
};
