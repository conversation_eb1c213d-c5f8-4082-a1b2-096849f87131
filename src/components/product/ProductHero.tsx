import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Pencil } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { iProduct } from "@/app/util/Interfaces";
import { ShareButtonWrapper } from "@/app/components/ShareButtonWrapper";
import { generateShareMetadata } from "@/app/lib/shareUtils";
import RatingDisplayWithThreshold from "@/app/components/RatingDisplayWithThreshold";
import { calculateWeightedRating } from "@/app/util/calculateWeightedRating";
import ProductEditButton from "@/components/product/ProductEditButton";
import FlexibleBusinessHours from "@/app/components/FlexibleBusinessHours";
import BusinessStatusBadge from "@/app/components/BusinessStatusBadge";
import { MINIMUM_REVIEWS } from "@/app/config/rating";

interface ProductHeroProps {
  product: iProduct;
}

const ProductHero: React.FC<ProductHeroProps> = ({ product }) => {
  // Removed debug console.log statements

  // Calculate weighted rating for proper eligibility checking
  // Use the actual total review count, not the limited array length
  const totalReviewCount = product._count?.reviews || 0;
  const ratingData = calculateWeightedRating(product.reviews || [], {
    globalAverageRating: product.rating,
    actualReviewCount: totalReviewCount, // Pass the real count
  });

  // Removed debug console.log statements

  // Generate share metadata for the product
  const shareMetadata = generateShareMetadata({
    title: product.name,
    description: product.description,
    url: typeof window !== "undefined" ? window.location.href : "",
    imageUrl: product.display_image,
    rating: product.rating,
    reviewCount: product._count?.reviews || 0,
  });

  return (
     <section className="relative bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] overflow-hidden">
      {/* Faint background image overlay */}
      {product.display_image && (
        <div className="absolute inset-0 opacity-[0.10] pointer-events-none">
          <Image
            src={product.display_image}
            alt=""
            fill
            className="object-cover blur-sm scale-110"
          />
        </div>
      )}
      <div className="relative grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
        {/* Main image */}
        <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
          {product.display_image ? (
            <Image
              src={product.display_image}
              alt={product.name}
              fill
              className="object-cover"
              priority
              sizes="(max-width: 768px) 100vw, 50vw"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <span className="text-gray-400">No image available</span>
            </div>
          )}
          {product.hasOwner && (
            <div className="absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
              Verified
            </div>
          )}
        </div>

        {/* Product info */}
        <div className="flex flex-col justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <RatingDisplayWithThreshold
                  ratingData={ratingData}
                  size="md"
                  showReviewCount={false}
                  showConfidence={false}
                  className=""
                />
                <span className="text-gray-500">
                  ({product._count?.reviews || 0} reviews)
                </span>
              </div>

              {/* Prominent threshold notice */}
              {!ratingData.hasMinimumReviews && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mt-2">
                  <div className="flex items-start gap-2">
                    <div className="flex-shrink-0">
                      <svg
                        className="w-5 h-5 text-amber-600 mt-0.5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-amber-800 mb-1">
                        Rating Not Yet Available
                      </h4>
                      <p className="text-sm text-amber-700">
                        This business needs{" "}
                        <strong>
                          {MINIMUM_REVIEWS - (product._count?.reviews || 0)} more review
                          {MINIMUM_REVIEWS - (product._count?.reviews || 0) !== 1 ? "s" : ""}
                        </strong>{" "}
                        before we can display a reliable average rating.
                        {(product._count?.reviews || 0) > 0 && (
                          <span className="block mt-1">
                            Current reviews: {product._count?.reviews || 0} of {MINIMUM_REVIEWS}
                            required
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <p className="text-gray-700 mb-4">{product.description}</p>
            <div className="flex flex-wrap gap-2 mb-4">
              {product.tags &&
                product.tags.map((tag, i) => (
                  <span
                    key={i}
                    className="bg-gray-100 px-3 py-1 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                ))}
            </div>
            {product.viewCount !== undefined && (
              <div className="text-sm text-gray-500 mb-4">
                {product.viewCount.toLocaleString()} views
              </div>
            )}
            
            {/* Business Status Badge - Always show if any business hours data exists */}
            <BusinessStatusBadge product={product} className="mb-3" showMessage={true} />
            
            {/* Business Hours Display - Show if businessHours OR legacy data exists */}
            {(product.businessHours || (product.openingDays && product.openingHrs && product.closingHrs)) && (
              <div className="mb-4">
                <FlexibleBusinessHours product={product} compact={false} />
              </div>
            )}
          </div>

          <div className="flex gap-3 mt-4 flex-wrap">
            <ShareButtonWrapper metadata={shareMetadata} />
            <Link href={`/cr/?id=${product.id}&rating=3`}>
              <Button
                aria-label="Write a review"
                className={
                  !ratingData.hasMinimumReviews
                    ? "bg-amber-600 hover:bg-amber-700 text-white"
                    : ""
                }
              >
                <Pencil className="h-4 w-4 mr-2" />
                {!ratingData.hasMinimumReviews
                  ? "Help Build Their Rating"
                  : "Write a Review"}
              </Button>
            </Link>
            <ProductEditButton
              product={product}
              variant="outline"
              size="default"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductHero;
