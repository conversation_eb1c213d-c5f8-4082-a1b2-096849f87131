import React from 'react';
import { Star, StarHalf } from 'lucide-react';

interface StarRatingProps {
    rating: number;
    size?: 'sm' | 'md' | 'lg';
    showRating?: boolean;
    className?: string;
}

const StarRating: React.FC<StarRatingProps> = ({
    rating,
    size = 'md',
    showRating = false,
    className = '',
}) => {
    // Ensure rating is between 0 and 5
    const normalizedRating = Math.min(Math.max(rating, 0), 5);

    // Calculate full stars, half stars, and empty stars
    const fullStars = Math.floor(normalizedRating);
    const hasHalfStar = normalizedRating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    // Size classes
    const sizeClasses = {
        sm: 'w-3 h-3',
        md: 'w-5 h-5 sm:w-4 sm:h-4',
        lg: 'w-6 h-6 sm:w-5 sm:h-5',
    } as const;

    // Color classes mapped by rating (1-5)
    const ratingColorClasses = [
        'text-red-500 fill-red-500',       // 1 star – red
        'text-orange-500 fill-orange-500', // 2 stars – orange
        'text-yellow-500 fill-yellow-500', // 3 stars – yellow
        'text-lime-500 fill-lime-500',     // 4 stars – lime
        'text-green-500 fill-green-500',   // 5 stars – green
    ] as const;

    // Determine color based on overall rating (rounded to nearest star up)
    const colorClass = ratingColorClasses[Math.max(0, Math.min(4, Math.ceil(normalizedRating) - 1))];

    return (
        <div className={`flex items-center ${className}`}>
            <div className="flex">
                {[...Array(fullStars)].map((_, i) => (
                    <Star
                        key={`full-${i}`}
                        className={`${colorClass} ${sizeClasses[size]}`}
                        aria-hidden="true"
                    />
                ))}

                {hasHalfStar && (
                    <StarHalf
                        className={`${colorClass} ${sizeClasses[size]}`}
                        aria-hidden="true"
                    />
                )}

                {[...Array(emptyStars)].map((_, i) => (
                    <Star
                        key={`empty-${i}`}
                        className={`text-gray-300 ${sizeClasses[size]}`}
                        aria-hidden="true"
                    />
                ))}
            </div>

            {showRating && (
                <span className="ml-1 text-sm font-medium">
                    {normalizedRating.toFixed(1)}
                </span>
            )}
        </div>
    );
};

export default StarRating; 