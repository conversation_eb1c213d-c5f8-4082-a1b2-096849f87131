"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Trash2, Plus, RefreshCw, Database, AlertCircle, CheckCircle2, Save } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Types from usernameRestrictions.ts
interface RestrictedUsernamesConfig {
  version: string;
  lastUpdated: string;
  categories: {
    [categoryName: string]: {
      description: string;
      usernames: string[];
    };
  };
}

interface UsernameEntry {
  username: string;
  category: string;
}

// Function to fetch restricted usernames
const fetchRestrictedUsernames = async () => {
  try {
    const response = await fetch("/api/admin/restricted-usernames");
    if (!response.ok) {
      throw new Error("Failed to fetch restricted usernames");
    }
    const data = await response.json();
    return data as RestrictedUsernamesConfig;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("An unknown error occurred");
  }
};

// Function to add a new username
const addUsername = async ({ username, category }: UsernameEntry) => {
  const response = await fetch("/api/admin/restricted-usernames", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ username, category }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to add username");
  }
  
  return response.json();
};

// Function to delete a username
const deleteUsername = async ({ username, category }: UsernameEntry) => {
  const response = await fetch("/api/admin/restricted-usernames", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ username, category }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to delete username");
  }
  
  return response.json();
};

// Function to add a new category
const addCategory = async ({ name, description }: { name: string; description: string }) => {
  const response = await fetch("/api/admin/restricted-usernames/category", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ name, description }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to add category");
  }
  
  return response.json();
};

export function RestrictedUsernamesManagement() {
  const [newUsername, setNewUsername] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [newCategory, setNewCategory] = useState("");
  const [newCategoryDescription, setNewCategoryDescription] = useState("");
  const [activeTab, setActiveTab] = useState("view");
  const [filterCategory, setFilterCategory] = useState("all");
  const queryClient = useQueryClient();

  // Query for fetching restricted usernames
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["restrictedUsernames"],
    queryFn: fetchRestrictedUsernames,
    refetchInterval: 60000, // Refetch every minute
  });

  // Set default category when data loads
  useEffect(() => {
    if (data && Object.keys(data.categories).length > 0 && !selectedCategory) {
      setSelectedCategory(Object.keys(data.categories)[0]);
    }
  }, [data, selectedCategory]);

  // Mutation for adding a username
  const addUsernameMutation = useMutation({
    mutationFn: addUsername,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["restrictedUsernames"] });
      setNewUsername("");
      toast.success("Username added successfully");
    },
    onError: (error) => {
      toast.error(`Failed to add username: ${error.message}`);
    },
  });

  // Mutation for deleting a username
  const deleteUsernameMutation = useMutation({
    mutationFn: deleteUsername,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["restrictedUsernames"] });
      toast.success("Username deleted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to delete username: ${error.message}`);
    },
  });

  // Mutation for adding a category
  const addCategoryMutation = useMutation({
    mutationFn: addCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["restrictedUsernames"] });
      setNewCategory("");
      setNewCategoryDescription("");
      toast.success("Category added successfully");
    },
    onError: (error) => {
      toast.error(`Failed to add category: ${error.message}`);
    },
  });

  const handleAddUsername = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newUsername.trim() || !selectedCategory) return;
    addUsernameMutation.mutate({
      username: newUsername.trim(),
      category: selectedCategory,
    });
  };

  const handleDeleteUsername = (username: string, category: string) => {
    deleteUsernameMutation.mutate({ username, category });
  };

  const handleAddCategory = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCategory.trim() || !newCategoryDescription.trim()) return;
    addCategoryMutation.mutate({
      name: newCategory.trim(),
      description: newCategoryDescription.trim(),
    });
  };

  // Count total usernames across all categories
  const getTotalUsernamesCount = () => {
    if (!data) return 0;
    
    return Object.values(data.categories).reduce(
      (total, category) => total + category.usernames.length, 
      0
    );
  };

  // Get all usernames as flattened array with category info
  const getAllUsernames = () => {
    if (!data) return [];
    
    const allUsernames: { username: string; category: string }[] = [];
    
    Object.entries(data.categories).forEach(([categoryName, categoryData]) => {
      categoryData.usernames.forEach(username => {
        allUsernames.push({ username, category: categoryName });
      });
    });
    
    return allUsernames.sort((a, b) => a.username.localeCompare(b.username));
  };

  // Filter usernames by category
  const getFilteredUsernames = () => {
    const allUsernames = getAllUsernames();
    if (filterCategory === "all") return allUsernames;
    return allUsernames.filter(entry => entry.category === filterCategory);
  };

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-500">Error loading restricted usernames</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error?.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="view">View Usernames</TabsTrigger>
          <TabsTrigger value="add">Add Username</TabsTrigger>
          <TabsTrigger value="categories">Manage Categories</TabsTrigger>
        </TabsList>
        
        {/* View Usernames Tab */}
        <TabsContent value="view" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-medium">Restricted Usernames</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => queryClient.invalidateQueries({ queryKey: ["restrictedUsernames"] })}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </Button>
              </div>
              <CardDescription>
                Total of {getTotalUsernamesCount()} restricted usernames across {data ? Object.keys(data.categories).length : 0} categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <label className="text-sm font-medium mb-1 block">Filter by Category</label>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="w-full md:w-[200px]">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {data && Object.keys(data.categories).map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {isLoading ? (
                <div className="py-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading usernames...</p>
                </div>
              ) : (
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Username</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getFilteredUsernames().length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-4 text-muted-foreground">
                            No restricted usernames found
                          </TableCell>
                        </TableRow>
                      ) : (
                        getFilteredUsernames().map((entry) => (
                          <TableRow key={`${entry.category}-${entry.username}`}>
                            <TableCell className="font-medium">{entry.username}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{entry.category}</Badge>
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteUsername(entry.username, entry.category)}
                                className="h-8 w-8 text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
            <CardFooter className="text-xs text-muted-foreground">
              Last updated: {data?.lastUpdated || "Unknown"}
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Add Username Tab */}
        <TabsContent value="add" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Add New Restricted Username</CardTitle>
              <CardDescription>
                Add a new username to the restricted list
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAddUsername} className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Username</label>
                  <Input
                    type="text"
                    value={newUsername}
                    onChange={(e) => setNewUsername(e.target.value)}
                    placeholder="Enter username to restrict"
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {data && Object.keys(data.categories).map(category => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <Button 
                  type="submit" 
                  disabled={!newUsername.trim() || !selectedCategory || addUsernameMutation.isPending}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Username
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Manage Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Add New Category</CardTitle>
              <CardDescription>
                Create a new category for organizing restricted usernames
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAddCategory} className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Category Name</label>
                  <Input
                    type="text"
                    value={newCategory}
                    onChange={(e) => setNewCategory(e.target.value)}
                    placeholder="Enter category name"
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Input
                    type="text"
                    value={newCategoryDescription}
                    onChange={(e) => setNewCategoryDescription(e.target.value)}
                    placeholder="Enter category description"
                    className="w-full"
                  />
                </div>
                
                <Button 
                  type="submit" 
                  disabled={!newCategory.trim() || !newCategoryDescription.trim() || addCategoryMutation.isPending}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Category
                </Button>
              </form>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Existing Categories</CardTitle>
              <CardDescription>
                View and manage existing categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="py-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading categories...</p>
                </div>
              ) : (
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Category</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="text-right">Count</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data && Object.entries(data.categories).length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-4 text-muted-foreground">
                            No categories found
                          </TableCell>
                        </TableRow>
                      ) : (
                        data && Object.entries(data.categories).map(([categoryName, categoryData]) => (
                          <TableRow key={categoryName}>
                            <TableCell className="font-medium">{categoryName}</TableCell>
                            <TableCell>{categoryData.description}</TableCell>
                            <TableCell className="text-right">{categoryData.usernames.length}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
