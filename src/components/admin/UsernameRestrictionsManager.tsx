/**
 * Admin component for managing username restrictions
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, CheckCircle, XCircle, AlertTriangle, Search } from 'lucide-react';

interface UsernameRestrictionStatus {
  isInitialized: boolean;
  totalRestrictions: number;
  categories: string[];
}

interface ValidationResult {
  isValid: boolean;
  reason?: string;
  category?: string;
}

interface HealthStatus {
  isHealthy: boolean;
  issues: string[];
  status: UsernameRestrictionStatus;
}

export default function UsernameRestrictionsManager() {
  const [status, setStatus] = useState<UsernameRestrictionStatus | null>(null);
  const [restrictedUsernames, setRestrictedUsernames] = useState<string[]>([]);
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Validation testing
  const [testUsername, setTestUsername] = useState('');
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [bulkTestUsernames, setBulkTestUsernames] = useState('');
  const [bulkTestResults, setBulkTestResults] = useState<any>(null);

  useEffect(() => {
    loadStatus();
    loadRestrictedUsernames();
    checkHealth();
  }, []);

  const loadStatus = async () => {
    try {
      const response = await fetch('/api/admin/username-restrictions/manage?action=status');
      const data = await response.json();
      
      if (data.success) {
        setStatus(data.data);
      } else {
        setError('Failed to load status');
      }
    } catch (err) {
      setError('Error loading status');
    }
  };

  const loadRestrictedUsernames = async () => {
    try {
      const response = await fetch('/api/admin/username-restrictions/manage?action=list');
      const data = await response.json();
      
      if (data.success) {
        setRestrictedUsernames(data.data.usernames);
      }
    } catch (err) {
      console.error('Error loading restricted usernames:', err);
    }
  };

  const checkHealth = async () => {
    try {
      const response = await fetch('/api/admin/username-restrictions/manage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'health-check' })
      });
      const data = await response.json();
      
      if (data.success) {
        setHealthStatus(data.data);
      }
    } catch (err) {
      console.error('Error checking health:', err);
    }
  };

  const reloadConfiguration = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/username-restrictions/manage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'reload' })
      });
      const data = await response.json();
      
      if (data.success) {
        setStatus(data.data);
        await loadRestrictedUsernames();
        await checkHealth();
        alert('Configuration reloaded successfully!');
      } else {
        setError(data.error || 'Failed to reload configuration');
      }
    } catch (err) {
      setError('Error reloading configuration');
    } finally {
      setLoading(false);
    }
  };

  const testSingleUsername = async () => {
    if (!testUsername.trim()) return;
    
    try {
      const response = await fetch(`/api/admin/username-restrictions/manage?action=validate&username=${encodeURIComponent(testUsername)}`);
      const data = await response.json();
      
      if (data.success) {
        setValidationResult(data.data);
      }
    } catch (err) {
      console.error('Error testing username:', err);
    }
  };

  const testBulkUsernames = async () => {
    if (!bulkTestUsernames.trim()) return;
    
    const usernames = bulkTestUsernames.split('\n').map(u => u.trim()).filter(u => u);
    
    try {
      const response = await fetch('/api/admin/username-restrictions/manage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'test-validation',
          testUsernames: usernames
        })
      });
      const data = await response.json();
      
      if (data.success) {
        setBulkTestResults(data.data);
      }
    } catch (err) {
      console.error('Error testing bulk usernames:', err);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Username Restrictions Management</h2>
        <Button 
          onClick={reloadConfiguration} 
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Reload Configuration
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="status" className="space-y-4">
        <TabsList>
          <TabsTrigger value="status">System Status</TabsTrigger>
          <TabsTrigger value="restrictions">Restrictions List</TabsTrigger>
          <TabsTrigger value="testing">Testing Tools</TabsTrigger>
          <TabsTrigger value="health">Health Check</TabsTrigger>
        </TabsList>

        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
              <CardDescription>Current status of the username restrictions system</CardDescription>
            </CardHeader>
            <CardContent>
              {status ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    {status.isInitialized ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="font-medium">
                      Service {status.isInitialized ? 'Initialized' : 'Not Initialized'}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Total Restrictions</p>
                      <p className="text-2xl font-bold">{status.totalRestrictions}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Categories</p>
                      <p className="text-2xl font-bold">{status.categories.length}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-2">Active Categories</p>
                    <div className="flex flex-wrap gap-2">
                      {status.categories.map(category => (
                        <Badge key={category} variant="secondary">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <p>Loading status...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="restrictions">
          <Card>
            <CardHeader>
              <CardTitle>Restricted Usernames</CardTitle>
              <CardDescription>
                Complete list of currently restricted usernames ({restrictedUsernames.length} total)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="max-h-96 overflow-y-auto">
                <div className="grid grid-cols-3 gap-2">
                  {restrictedUsernames.map(username => (
                    <Badge key={username} variant="outline" className="justify-center">
                      {username}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Single Username Test</CardTitle>
                <CardDescription>Test validation for a single username</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2 mb-4">
                  <Input
                    placeholder="Enter username to test"
                    value={testUsername}
                    onChange={(e) => setTestUsername(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && testSingleUsername()}
                  />
                  <Button onClick={testSingleUsername}>
                    <Search className="h-4 w-4 mr-2" />
                    Test
                  </Button>
                </div>

                {validationResult && (
                  <Alert variant={validationResult.isValid ? "default" : "destructive"}>
                    <div className="flex items-center gap-2">
                      {validationResult.isValid ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <AlertDescription>
                        <strong>{testUsername}</strong> is {validationResult.isValid ? 'allowed' : 'restricted'}
                        {validationResult.reason && (
                          <span className="block mt-1 text-sm">
                            Reason: {validationResult.reason}
                          </span>
                        )}
                        {validationResult.category && (
                          <span className="block mt-1 text-sm">
                            Category: <Badge variant="secondary">{validationResult.category}</Badge>
                          </span>
                        )}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Bulk Username Test</CardTitle>
                <CardDescription>Test multiple usernames at once (one per line)</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <textarea
                    className="w-full h-32 p-2 border rounded-md"
                    placeholder="Enter usernames to test (one per line)"
                    value={bulkTestUsernames}
                    onChange={(e) => setBulkTestUsernames(e.target.value)}
                  />
                  <Button onClick={testBulkUsernames}>Test All</Button>

                  {bulkTestResults && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <p className="text-sm text-gray-600">Total Tested</p>
                          <p className="text-xl font-bold">{bulkTestResults.summary.total}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Allowed</p>
                          <p className="text-xl font-bold text-green-600">{bulkTestResults.summary.allowed}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Restricted</p>
                          <p className="text-xl font-bold text-red-600">{bulkTestResults.summary.restricted}</p>
                        </div>
                      </div>

                      <div className="max-h-64 overflow-y-auto space-y-2">
                        {bulkTestResults.results.map((result: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <span className="font-mono">{result.username}</span>
                            <div className="flex items-center gap-2">
                              {result.validation.isValid ? (
                                <Badge variant="secondary" className="text-green-700">Allowed</Badge>
                              ) : (
                                <Badge variant="destructive">
                                  Restricted ({result.validation.category})
                                </Badge>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="health">
          <Card>
            <CardHeader>
              <CardTitle>Health Check</CardTitle>
              <CardDescription>System health and diagnostic information</CardDescription>
            </CardHeader>
            <CardContent>
              {healthStatus ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    {healthStatus.isHealthy ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="font-medium">
                      System is {healthStatus.isHealthy ? 'Healthy' : 'Unhealthy'}
                    </span>
                  </div>

                  {healthStatus.issues.length > 0 && (
                    <div>
                      <p className="font-medium text-red-600 mb-2">Issues Found:</p>
                      <ul className="list-disc list-inside space-y-1">
                        {healthStatus.issues.map((issue, index) => (
                          <li key={index} className="text-sm text-red-600">{issue}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="pt-4 border-t">
                    <Button onClick={checkHealth} variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh Health Check
                    </Button>
                  </div>
                </div>
              ) : (
                <p>Loading health status...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}