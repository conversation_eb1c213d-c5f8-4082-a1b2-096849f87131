"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Trash2, Plus, RefreshCw, Database, AlertCircle, CheckCircle2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { toast } from "sonner";

// Function to check server status
const checkServerStatus = async () => {
  try {
    const response = await fetch("/api/words", { signal: AbortSignal.timeout(5000) });
    return response.ok;
  } catch (error) {
    return false;
  }
};

// Function to fetch words count
const fetchWords = async () => {
  try {
    const response = await fetch("/api/words", { signal: AbortSignal.timeout(5000) });
    if (!response.ok) {
      throw new Error("Failed to fetch words");
    }
    const words = await response.json();
    return { count: words.length, words, isServerUp: true };
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === "TimeoutError" || error.name === "TypeError") {
        return { count: 0, words: [], isServerUp: false };
      }
      throw error;
    }
    throw new Error("An unknown error occurred");
  }
};

// Function to add a new word
const addWord = async (word: string) => {
  const serverStatus = await checkServerStatus();
  if (!serverStatus) {
    throw new Error("Word server is not running");
  }

  const response = await fetch("/api/words", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ words: word }),
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to add word");
  }
  return response.json();
};

// Function to delete a word
const deleteWord = async (word: string) => {
  const encodedWord = encodeURIComponent(word);
  const response = await fetch(`/api/words/${encodedWord}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    let errorMsg = 'Failed to delete word';
    try {
      const error = await response.json();
      errorMsg = error.error || errorMsg;
    } catch {}
    throw new Error(errorMsg);
  }
  return response.json();
};

export function WordManagement() {
  const [newWord, setNewWord] = useState("");
  const [deleteWordInput, setDeleteWordInput] = useState("");
  const queryClient = useQueryClient();

  // Query for fetching words
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["words"],
    queryFn: fetchWords,
    retry: 1,
    refetchInterval: 30000, // Check server status every 30 seconds
  });

  // Mutation for adding a word
  const addWordMutation = useMutation({
    mutationFn: addWord,
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["words"] });
      setNewWord("");
      // data: { added: [], existing: [] }
      const word = variables;
      if (data?.existing && data.existing.includes(word)) {
        toast.info("Word already exists");
      } else if (data?.added && data.added.includes(word)) {
        toast.success("Word added successfully");
      } else {
        toast.success("Word processed");
      }
    },
    onError: (error) => {
      toast.error(`Failed to add word: ${error.message}`);
    },
  });

  // Mutation for deleting a word
  const deleteWordMutation = useMutation({
    mutationFn: deleteWord,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["words"] });
      setDeleteWordInput("");
      toast.success("Word deleted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to delete word: ${error.message}`);
    },
  });

  const handleAddWord = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newWord.trim()) return;
    addWordMutation.mutate(newWord.trim());
  };

  const handleDeleteWord = (e: React.FormEvent) => {
    e.preventDefault();
    if (!deleteWordInput.trim()) return;
    deleteWordMutation.mutate(deleteWordInput.trim());
  };

  const ServerStatusIndicator = () => (
    <div className="flex items-center gap-2">
      {data?.isServerUp ? (
        <>
          <CheckCircle2 className="w-5 h-5 text-green-500" />
          <span className="text-sm text-green-600">Server Online</span>
        </>
      ) : (
        <>
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span className="text-sm text-red-600">Server Offline</span>
        </>
      )}
    </div>
  );

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-500">Error loading words</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error?.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Word Management</h2>
      
      {/* Server Status Card */}
      <Card className="mb-4 border-2" style={{ borderColor: data?.isServerUp ? '#22c55e' : '#ef4444' }}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-medium">Word Server Status</CardTitle>
              <CardDescription>
                {data?.isServerUp 
                  ? "Connected to word management server"
                  : "Unable to connect to word management server"}
              </CardDescription>
            </div>
            <ServerStatusIndicator />
          </div>
        </CardHeader>
        {!data?.isServerUp && (
          <CardContent>
            <div className="text-sm text-red-600">
              <p>Please ensure:</p>
              <ul className="list-disc list-inside mt-2">
                <li>The word server is running on localhost:3002</li>
                <li>There are no network connectivity issues</li>
                <li>The server is properly configured</li>
              </ul>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Word Count Card */}
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">Total Words in Database</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => queryClient.invalidateQueries({ queryKey: ["words"] })}
              className="flex items-center gap-2"
              disabled={!data?.isServerUp}
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3">
            <Database className="w-8 h-8 text-primary" />
            <div>
              <div className="text-3xl font-bold">
                {isLoading ? "..." : data?.count || 0}
              </div>
              <p className="text-sm text-muted-foreground">
                {data?.isServerUp ? "Total words stored" : "Unable to fetch word count"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Word Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-medium">Add New Word</CardTitle>
          {!data?.isServerUp && (
            <CardDescription className="text-red-600">
              Cannot add words while server is offline
            </CardDescription>
          )}
        </CardHeader>
        <CardContent>
          <form onSubmit={handleAddWord} className="flex gap-2">
            <Input
              type="text"
              value={newWord}
              onChange={(e) => setNewWord(e.target.value)}
              placeholder="Enter a new word"
              className="flex-1"
              disabled={!data?.isServerUp}
            />
            <Button 
              type="submit" 
              disabled={!data?.isServerUp || addWordMutation.isPending}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Delete Word Card */}
      <Card className="mt-4">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Delete Word</CardTitle>
          {!data?.isServerUp && (
            <CardDescription className="text-red-600">
              Cannot delete words while server is offline
            </CardDescription>
          )}
        </CardHeader>
        <CardContent>
          <form onSubmit={handleDeleteWord} className="flex gap-2">
            <Input
              type="text"
              value={deleteWordInput}
              onChange={(e) => setDeleteWordInput(e.target.value)}
              placeholder="Enter a word to delete"
              className="flex-1"
              disabled={!data?.isServerUp}
            />
            <Button
              type="submit"
              variant="destructive"
              disabled={!data?.isServerUp || deleteWordMutation.isPending}
              className="flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}