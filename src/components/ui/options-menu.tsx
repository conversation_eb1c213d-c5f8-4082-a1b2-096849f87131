import React from "react";
import { MoreVertical, Edit2, Trash2 } from "lucide-react";
import { Button } from "./button";

export interface OptionsMenuProps {
  onEdit: () => void;
  onDelete: () => void;
  setIsEditing: (value: boolean) => void;
}

export const OptionsMenu: React.FC<OptionsMenuProps> = ({ onEdit, onDelete, setIsEditing }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleEdit = () => {
    onEdit();
    setIsEditing(true);
    setIsOpen(false);
  };

  const handleDelete = () => {
    onDelete();
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="p-1"
      >
        <MoreVertical className="w-4 h-4" />
      </Button>
      {isOpen && (
        <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50">
          <button
            onClick={handleEdit}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
          >
            <Edit2 className="w-4 h-4" />
            Edit
          </button>
          <button
            onClick={handleDelete}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 text-red-600 flex items-center gap-2"
          >
            <Trash2 className="w-4 h-4" />
            Delete
          </button>
        </div>
      )}
    </div>
  );
};
