import React from "react";
import { Tooltip } from "./tooltip";
import { AlertCircle } from "lucide-react";

interface ReplyRestrictionIndicatorProps {
  reason: string;
}

export const ReplyRestrictionIndicator: React.FC<ReplyRestrictionIndicatorProps> = ({ reason }) => {
  return (
    <Tooltip content={<p>{reason}</p>}>
      <AlertCircle className="w-4 h-4 text-yellow-500" />
    </Tooltip>
  );
};
