import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
    StarIcon,
    TrendingUpIcon,
    TrendingDownIcon,
    EyeIcon,
    UsersIcon,
    MessageSquareIcon,
    ClockIcon,
    ArrowUpRight,
} from "lucide-react";
import RatingDisplayWithThreshold from '@/app/components/RatingDisplayWithThreshold';
import { calculateWeightedRating } from '@/app/util/calculateWeightedRating';
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import NotificationBell from "@/app/components/notification-components/OwnerNotification";
import { iProduct, iProductOwnerNotification } from "@/app/util/Interfaces";
import { Tooltip } from "@mantine/core";
import { cn } from "@/lib/utils";

interface BusinessProductCardProps {
    product: iProduct;
    notifications: iProductOwnerNotification[];
}

export default function BusinessProductCard({ product, notifications }: BusinessProductCardProps) {
    const router = useRouter();
    const productNotifications = notifications.filter(
        (notification: iProductOwnerNotification) =>
            notification?.product_id === product?.id
    );
    const unreadNotifications = productNotifications.filter(
        (notification: iProductOwnerNotification) => notification?.read === false
    );

    const handleNotificationClick = () => {
        router.push(`/notifications?productId=${product.id}`);
    };

    // Calculate engagement metrics
    const viewCount = product.viewCount || 0;
    const reviewCount = product._count?.reviews || 0;
    const averageRating = product.rating || 0;
    const lastReviewDate = product.reviews && product.reviews.length > 0
        ? new Date([...product.reviews].sort((a, b) =>
            new Date(b.createdDate!).getTime() - new Date(a.createdDate!).getTime()
        )[0].createdDate!)
        : null;

    // Calculate trend metrics
    const calculateTrend = () => {
        if (!product.reviews || product.reviews.length < 2) {
            return { trend: 'neutral', reason: 'Insufficient review data' };
        }

        // Sort reviews by date
        const sortedReviews = [...product.reviews].sort((a, b) =>
            new Date(a.createdDate!).getTime() - new Date(b.createdDate!).getTime()
        );

        // Calculate recent vs older ratings
        const recentReviews = sortedReviews.slice(-3); // Last 3 reviews
        const olderReviews = sortedReviews.slice(0, -3); // All reviews except last 3

        const recentAverage = recentReviews.reduce((sum, review) => sum + (review.rating || 0), 0) / recentReviews.length;
        const olderAverage = olderReviews.length > 0
            ? olderReviews.reduce((sum, review) => sum + (review.rating || 0), 0) / olderReviews.length
            : recentAverage;

        // Calculate review velocity (reviews per week)
        const firstReviewDate = new Date(sortedReviews[0].createdDate!);
        const lastReviewDate = new Date(sortedReviews[sortedReviews.length - 1].createdDate!);
        const weeksSinceFirstReview = (lastReviewDate.getTime() - firstReviewDate.getTime()) / (1000 * 60 * 60 * 24 * 7);
        const reviewsPerWeek = weeksSinceFirstReview > 0 ? sortedReviews.length / weeksSinceFirstReview : 0;

        // Calculate rating distribution
        const ratingDistribution = sortedReviews.reduce((acc, review) => {
            const rating = Math.round(review.rating || 0);
            acc[rating] = (acc[rating] || 0) + 1;
            return acc;
        }, {} as Record<number, number>);

        const highRatings = (ratingDistribution[4] || 0) + (ratingDistribution[5] || 0);
        const highRatingPercentage = (highRatings / sortedReviews.length) * 100;

        // Determine trend based on multiple factors
        let trend = 'neutral';
        let reason = '';

        if (recentAverage > olderAverage + 0.5) {
            trend = 'up';
            reason = 'Recent ratings improved';
        } else if (recentAverage < olderAverage - 0.5) {
            trend = 'down';
            reason = 'Recent ratings declined';
        } else if (reviewsPerWeek > 1 && highRatingPercentage > 70) {
            trend = 'up';
            reason = 'High review velocity with positive ratings';
        } else if (reviewsPerWeek > 1 && highRatingPercentage < 30) {
            trend = 'down';
            reason = 'High review velocity with negative ratings';
        } else if (averageRating >= 4 && highRatingPercentage > 80) {
            trend = 'up';
            reason = 'Consistently high ratings';
        } else if (averageRating <= 2 && highRatingPercentage < 20) {
            trend = 'down';
            reason = 'Consistently low ratings';
        }

        return { trend, reason };
    };

    const { trend, reason } = calculateTrend();

    return (
        <Card
            key={product.id}
            className="group flex flex-col hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white border-gray-200"
        >
            <CardHeader className="pb-2">
                <div className="flex justify-between items-start gap-2">
                    <div className="flex-1 min-w-0">
                        <Link
                            href={`/reviews?id=${product.id}`}
                            className="text-xl font-bold hover:text-blue-600 transition-colors truncate block group-hover:text-blue-600"
                        >
                            {product.name}
                        </Link>
                        <p className="text-sm text-gray-500 mt-1 truncate">
                            {product.description?.slice(0, 60)}
                            {product.description && product.description.length > 60 ? '...' : ''}
                        </p>
                    </div>
                    <Badge
                        className="text-white bg-black shrink-0 hover:bg-gray-800 transition-colors"
                        variant="secondary"
                    >
                        {product.tags?.slice(0, 1)?.join(", ") || "No Category"}
                    </Badge>
                </div>
                {product.display_image && (
                    <div className="relative w-full h-48 mt-3 rounded-lg overflow-hidden bg-gray-100">
                        <Image
                            src={product.display_image}
                            alt={product.name}
                            width={600}
                            height={400}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                            onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZTJlOGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzY0NzQ4YiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
                                target.onerror = null;
                            }}
                        />
                    </div>
                )}
            </CardHeader>
            <CardContent className="flex-grow pt-2">
                {/* Performance Metrics */}
                <div className="grid grid-cols-2 gap-3 mb-4">
                    <Tooltip label="Total product views" withArrow>
                        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg flex items-center gap-3 hover:from-blue-100 hover:to-blue-200 transition-colors cursor-help">
                            <div className="bg-blue-100 p-2 rounded-lg">
                                <EyeIcon className="w-4 h-4 text-blue-600" />
                            </div>
                            <div>
                                <p className="text-sm font-semibold text-blue-900">{viewCount.toLocaleString()}</p>
                                <p className="text-xs text-blue-600">Views</p>
                            </div>
                        </div>
                    </Tooltip>

                    <Tooltip label="Total customer reviews" withArrow>
                        <div className="bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg flex items-center gap-3 hover:from-green-100 hover:to-green-200 transition-colors cursor-help">
                            <div className="bg-green-100 p-2 rounded-lg">
                                <MessageSquareIcon className="w-4 h-4 text-green-600" />
                            </div>
                            <div>
                                <p className="text-sm font-semibold text-green-900">{reviewCount}</p>
                                <p className="text-xs text-green-600">Reviews</p>
                            </div>
                        </div>
                    </Tooltip>
                </div>

                {/* Rating and Trend */}
                <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                        <div className="bg-yellow-50 p-2 rounded-lg">
                            <StarIcon className="w-5 h-5 text-yellow-400" />
                        </div>
                        <div>
                            <RatingDisplayWithThreshold
                                ratingData={calculateWeightedRating(product.reviews || [], {
                                    globalAverageRating: product.rating
                                })}
                                size="sm"
                                showReviewCount={true}
                                showConfidence={false}
                                minimumReviewsMessage="No rating yet"
                                className=""
                            />
                        </div>
                    </div>
                    <Tooltip label={reason} withArrow>
                        {trend === 'up' ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 transition-colors">
                                <TrendingUpIcon className="w-3 h-3 mr-1" />
                                Trending Up
                            </Badge>
                        ) : trend === 'down' ? (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 transition-colors">
                                <TrendingDownIcon className="w-3 h-3 mr-1" />
                                Needs Attention
                            </Badge>
                        ) : (
                            <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 transition-colors">
                                <ClockIcon className="w-3 h-3 mr-1" />
                                Stable
                            </Badge>
                        )}
                    </Tooltip>
                </div>

                {/* Notifications and Last Review */}
                <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center cursor-pointer hover:opacity-80 transition-opacity" onClick={handleNotificationClick}>
                        <NotificationBell
                            notifications={unreadNotifications}
                        />
                    </div>
                    {lastReviewDate && (
                        <div className="text-xs text-gray-500 flex items-center bg-gray-50 px-3 py-1.5 rounded-full">
                            <ClockIcon className="w-3 h-3 mr-1.5" />
                            Last review: {lastReviewDate.toLocaleDateString()}
                        </div>
                    )}
                </div>

                {/* Latest Review */}
                {product.reviews && product.reviews.length > 0 ? (
                    <Link
                        href={`/fr?id=${[...product.reviews].sort((a, b) =>
                            new Date(b.createdDate!).getTime() - new Date(a.createdDate!).getTime()
                        )[0].id}&productid=${product.id}`}
                        className="bg-gray-50 p-4 rounded-lg block hover:bg-gray-100 transition-colors group"
                    >
                        <div className="flex justify-between items-start">
                            <p className="text-sm text-gray-700">
                                <span className="font-semibold">Latest review:</span>{" "}
                                &quot;{[...product.reviews].sort((a, b) =>
                                    new Date(b.createdDate!).getTime() - new Date(a.createdDate!).getTime()
                                )[0].title}&quot;
                            </p>
                            <ArrowUpRight className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                            {new Date([...product.reviews].sort((a, b) =>
                                new Date(b.createdDate!).getTime() - new Date(a.createdDate!).getTime()
                            )[0].createdDate!).toLocaleDateString()}
                        </p>
                    </Link>
                ) : (
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-500 italic">No reviews yet</p>
                    </div>
                )}
            </CardContent>
            <CardFooter className="pt-2 flex gap-2">
                <Link
                    href={`/product/${product.id}`}
                    className="flex-1 text-center bg-blue-600 text-white px-4 py-2.5 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center gap-2"
                >
                    Check out this business
                    <ArrowUpRight className="w-4 h-4" />
                </Link>
                <Link
                    href={`/editproduct?pid=${product.id}`}
                    className="flex-1 text-center bg-gray-100 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
                >
                    Edit
                </Link>
            </CardFooter>
        </Card>
    );
}
