import React from "react";
import UserProfileComponent from "@/app/components/UserProfileComponent";
import Link from "next/link";
import { ArrowL<PERSON><PERSON>, User, Star, MessageCircle } from "lucide-react";
import Schema from "@/app/components/Schema";
import { notFound } from "next/navigation";
import { iUser, iReview } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { Metadata } from "next";
import { generateMetadata as generateBaseMetadata } from "@/app/lib/metadata";

interface UserProfilePageProps {
  params: {
    id: string;
  };
}

// Generate dynamic metadata for public user profiles
export async function generateMetadata({ params }: UserProfilePageProps): Promise<Metadata> {
  try {
    // Extract username or ID from params
    const identifier = params.id;
    const isUsername = identifier.startsWith('@');
    const searchValue = isUsername ? identifier.slice(1) : identifier;

    // Fetch user data
    const user = await prisma.user.findFirst({
      where: isUsername 
        ? { userName: searchValue }
        : { id: searchValue },
      include: {
        reviews: {
          where: { isDeleted: false },
          take: 5,
          orderBy: { createdDate: 'desc' }
        },
        _count: {
          select: {
            reviews: {
              where: { isDeleted: false }
            }
          }
        }
      }
    });

    if (!user) {
      return {
        title: 'User Not Found | ReviewIt.gy',
        description: 'The requested user profile could not be found.',
        robots: { index: false, follow: false }
      };
    }

    const username = user.userName || user.firstName || 'User';
    const reviewCount = user._count?.reviews || 0;
    const totalRating = user.reviews?.reduce((acc, review) => acc + review.rating, 0) || 0;
    const averageRating = user.reviews?.length ? totalRating / user.reviews.length : 0;

    return generateBaseMetadata({
      title: `${username}'s Profile | ReviewIt.gy`,
      description: `View ${username}'s reviews and activity on ReviewIt.gy. ${reviewCount} reviews with an average rating of ${averageRating.toFixed(1)} stars. Discover trusted reviewer insights for businesses in Guyana.`,
      path: `/userprofile/${params.id}`,
      type: "article",
      images: user.avatar ? [user.avatar] : undefined
    });

  } catch (error) {
    console.error('Error generating metadata for user profile:', error);
    return {
      title: 'User Profile | ReviewIt.gy',
      description: 'View user profile and reviews on ReviewIt.gy',
      robots: { index: false, follow: false }
    };
  }
}

export default async function UserProfilePage({ params }: UserProfilePageProps) {
  if (!params.id) {
    notFound();
  }

  try {
    // Validate input length to avoid DB lookup for obviously invalid values
    if (params.id.length > 50) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-pink-50 flex items-center justify-center px-4">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">User Not Found</h1>
            <p className="text-gray-600 mb-6">The requested username or ID is invalid.</p>
            <Link
              href="/"
              className="text-blue-600 hover:text-blue-800 flex items-center justify-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Return Home
            </Link>
          </div>
        </div>
      );
    }
    // Support both Clerk ID and username for profile URLs
    const idParam = decodeURIComponent(params.id);
    const isUsername = idParam.startsWith('@');
    const isClerkId = !isUsername && idParam.startsWith('user_');
    const lookupValue = isUsername ? idParam.slice(1) : idParam; // Remove @ prefix for username lookup
    const user = await prisma.user.findUnique({
      where: isClerkId ? { id: idParam } : { userName: lookupValue },
      include: {
        comments: {
          include: {
            review: true,
          },
        },
        reviews: {
          include: {
            product: true,
            user: true,
            voteCount: true,
            comments: {
              include: {
                user: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: true,
            comments: true,
          },
        },
        likedReviews: true,
      },
    });

    if (!user) {
      console.error("User not found with ID:", params.id);
      notFound();
    }

    // Check for automatic unsuspension
    if (user.status === 'SUSPENDED' && user.suspendedUntil) {
      const now = new Date();
      const suspensionEndDate = new Date(user.suspendedUntil);
      
      // If suspension period has expired, automatically unsuspend the user
      if (now >= suspensionEndDate) {
        await prisma.user.update({
          where: {
            id: params.id,
          },
          data: {
            status: 'ACTIVE',
            suspendedUntil: null,
            suspendedReason: null,
          },
        });
        // Update the user object to reflect the change
        user.status = 'ACTIVE';
        user.suspendedUntil = null;
        user.suspendedReason = null;
      }
    }

    const username = user.userName || "User";
    const reviewCount = user._count?.reviews || 0;

    // Calculate average rating with proper type safety and ensure non-zero values
    const reviews = user.reviews || [];
    const totalRating = reviews.reduce((acc: number, review: any) => acc + review.rating, 0);
    const averageRating = reviews.length > 0 ? totalRating / reviews.length : 1; // Default to 1 if no reviews

    // Format reviews for Schema component
    const formattedReviews = reviews.slice(0, 3).map((review: any) => ({
      author: review.user?.userName || username,
      reviewRating: Math.max(1, Math.min(5, review.rating)), // Ensure rating is between 1-5
      reviewBody: review.body,
      datePublished: review.createdDate ? new Date(review.createdDate).toISOString() : new Date().toISOString()
    }));

    // Generate random gradients
    const gradientColors = [
      'from-rose-50 via-white to-pink-50',
      'from-blue-50 via-white to-indigo-50', 
      'from-green-50 via-white to-emerald-50',
      'from-purple-50 via-white to-violet-50',
      'from-orange-50 via-white to-amber-50',
      'from-cyan-50 via-white to-teal-50',
      'from-indigo-50 via-white to-cyan-50'
    ];
    
    const animatedGradients = [
      ['from-rose-400/20 to-pink-600/20', 'from-pink-400/20 to-rose-600/20'],
      ['from-blue-400/20 to-purple-600/20', 'from-cyan-400/20 to-blue-600/20'],
      ['from-green-400/20 to-emerald-600/20', 'from-emerald-400/20 to-teal-600/20'],
      ['from-purple-400/20 to-violet-600/20', 'from-violet-400/20 to-purple-600/20'],
      ['from-orange-400/20 to-amber-600/20', 'from-amber-400/20 to-yellow-600/20'],
      ['from-cyan-400/20 to-teal-600/20', 'from-teal-400/20 to-cyan-600/20'],
      ['from-indigo-400/20 to-blue-600/20', 'from-blue-400/20 to-indigo-600/20']
    ];
    
    const randomIndex = Math.floor(Math.random() * gradientColors.length);
    const selectedGradient = gradientColors[randomIndex];
    const selectedAnimated = animatedGradients[randomIndex];

    return (
      <div className={`min-h-screen bg-gradient-to-br ${selectedGradient} relative`}>
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className={`absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[0]} rounded-full blur-3xl animate-pulse`}></div>
          <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br ${selectedAnimated[1]} rounded-full blur-3xl animate-pulse delay-1000`}></div>
        </div>

        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-cyan-600/5"></div>
          <div className="relative pt-20 pb-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-6xl mx-auto">
              {/* Navigation */}
              <div className="mb-12">
                <Link 
                  href="/browse" 
                  className="inline-flex items-center gap-2 px-5 py-3 text-sm font-medium text-gray-700 bg-white/90 backdrop-blur-md border border-white/50 rounded-2xl hover:bg-white hover:text-blue-600 hover:border-blue-200 hover:shadow-lg transition-all duration-300 shadow-sm group transform hover:-translate-y-0.5"
                >
                  <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
                  Back to Explore
                </Link>
              </div>

              {/* Welcome Message */}
              <div className="text-center mb-8">
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent mb-4 leading-tight">
                  @{username}&apos;s Profile
                </h1>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                  Discover authentic reviews and valuable feedback from this community member
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="relative px-4 sm:px-6 lg:px-8 pb-12">
          <div className="max-w-6xl mx-auto">
            <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl border border-white/50 overflow-hidden">
              <UserProfileComponent userIdFromParams={user.id} />
            </div>
          </div>
        </div>

        {/* Add Schema.org markup for SEO */}
        <Schema
          productName={`${username}&apos;s Profile`}
          description={`${username}&apos;s profile and activity on ReviewIt. ${reviewCount} reviews and counting.`}
          rating={Math.max(1, Math.min(5, averageRating))}
          reviewCount={Math.max(1, reviewCount)}
          reviews={formattedReviews}
          image={user.avatar || "/images/default-avatar.png"}
          category="Profile"
        />
      </div>
    );
  } catch (error) {
    console.error("Error loading user profile:", error);
    notFound();
  }
}
