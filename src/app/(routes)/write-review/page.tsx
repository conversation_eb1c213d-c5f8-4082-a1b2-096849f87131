"use client";

import { use<PERSON><PERSON> } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { iProduct, iReview } from "@/app/util/Interfaces";
import SearchBoxAndListener from "@/app/components/SearchBoxAndListener";
import Link from "next/link";
import PaginatedProductCarousel from "../../components/PaginatedProductCarousel";
import Quote from "@/app/components/Quote";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "@/app/util/serverFunctions";
import { TopReviewsSkeleton } from "@/app/components/skeletons";
import { Skeleton } from "@/app/components/Skeleton";

const WriteReviewPage = () => {
  const [products] = useAtom(allProductsStore);
  const [filteredProducts, setFilteredProducts] = useState<iProduct[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();

  // Fetch site metrics
  const { data: metrics } = useQuery({
    queryKey: ["siteMetrics"],
    queryFn: async () => {
      const response = await fetch('/api/site/metrics');
      if (!response.ok) {
        throw new Error('Failed to fetch site metrics');
      }
      const data = await response.json();
      return data.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch recent reviews
  const { data: recentReviews } = useQuery({
    queryKey: ["recentReviews"],
    queryFn: async () => {
      const response = await getLatestReviews("latest");
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch recent reviews");
      }
      return response.data || [];
    },
  });

  useEffect(() => {
    if (products) {
      setFilteredProducts(products);
    }
  }, [products]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (products) {
      const results = products.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase()) ||
        (product.tags && product.tags.some(tag =>
          tag.toLowerCase().includes(query.toLowerCase())
        ))
      );
      setFilteredProducts(results);
    }
  };

  const handleSelectProduct = (productId: string) => {
    router.push(`/cr?id=${productId}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
      <div className="container mx-auto px-4 py-12">
        {/* Enhanced Hero Section */}
        <div className="text-center mb-16">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-blue-100 text-blue-700 text-sm font-medium rounded-full mb-4">
                Share Your Experience
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Write a Review
            </h1>
            <p className="text-xl text-gray-700 max-w-2xl mx-auto leading-relaxed">
              Your voice matters. Help thousands of people make better decisions by sharing your honest experience.
            </p>
            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center items-center">
              {metrics ? (
                <>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Trusted by {metrics.totalUsers?.toLocaleString()}+ users</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>{metrics.totalReviews?.toLocaleString()}+ reviews shared</span>
                  </div>
                </>
              ) : (
                // Loading skeleton
                <>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="flex items-center">
                      Trusted by <Skeleton className="h-4 w-16 ml-1" /> users
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="flex items-center">
                      <Skeleton className="h-4 w-16" /> reviews shared
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Search Section */}
        <div className="max-w-3xl mx-auto mb-12">
          <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                Find a Product or Service
              </h2>
              <p className="text-gray-600">
                Search for what you'd like to review
              </p>
            </div>
            <SearchBoxAndListener onSearch={handleSearch} />
            <div className="mt-4 flex flex-wrap gap-2 justify-center">
              <span className="text-xs text-gray-500">Popular:</span>
              {['Restaurants', 'Hotels', 'Shops', 'Services'].map(tag => (
                <button
                  key={tag}
                  onClick={() => handleSearch(tag)}
                  className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Reviews Section */}
        {recentReviews === undefined ? (
          <div className="mb-12">
            <TopReviewsSkeleton />
          </div>
        ) : recentReviews && recentReviews.length > 0 && (
          <div className="mb-16">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-3">
                See What Others Are Saying
              </h2>
              <p className="text-gray-600">
                Get inspired by recent reviews from our community
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {recentReviews.slice(0, 3).map((review: iReview) => (
                <div key={review.id} className="transform hover:scale-105 transition-transform duration-200">
                  <Quote
                    userName={review.user?.userName || "Anonymous"}
                    userImage={review.user?.avatar || undefined}
                    quoteText={review.body}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Products Section */}
        <div className="mb-16">
          {filteredProducts && filteredProducts.length > 0 ? (
            <div>
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-3">
                  {searchQuery ? `Search Results` : "Browse Products"}
                </h2>
                <p className="text-gray-600">
                  {searchQuery ? `Found ${filteredProducts.length} results for "${searchQuery}"` : "Select a product to write your review"}
                </p>
              </div>
              <PaginatedProductCarousel
                allProducts={filteredProducts}
                miniCardOptions={{
                  size: "md",
                  showWriteReview: true,
                }}
                itemsPerPage={3}
                title=""
              />
            </div>
          ) : (
            searchQuery && (
              <div className="text-center py-12">
                <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">No products found</h3>
                  <p className="text-gray-600 mb-6">
                    No products found matching "{searchQuery}"
                  </p>
                  <button
                    onClick={() => {
                      setSearchQuery("");
                      setFilteredProducts(products || []);
                    }}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Clear search
                  </button>
                </div>
              </div>
            )
          )}
        </div>

        {/* Enhanced Call to Action */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-3xl p-12 text-white text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold mb-4">
              Can't Find What You're Looking For?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Be the first to add a product or service and share your experience with the community.
            </p>
            <Link 
              href="/submit" 
              className="inline-block bg-white text-blue-600 font-bold px-8 py-4 rounded-xl hover:bg-gray-100 transition-colors duration-200 shadow-lg"
            >
              Add New Product
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WriteReviewPage;
