"use client";

import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { getUser } from "@/app/util/serverFunctions";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle
} from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
    BarChart3,
    PackageOpen,
    Star,
    TrendingUp,
    Users,
    Calendar,
    AlertTriangle
} from "lucide-react";
import { iUser, iBusiness, iProduct } from "@/app/util/Interfaces";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ReadOnlyRating } from "@/app/components/RatingSystem";
import { formatRatingDisplay } from "@/app/util/ratingUtils";

interface StatCardProps {
    title: string;
    value: string | number;
    icon: React.ReactNode;
    description: string;
    color: string;
}

const StatCard = ({ title, value, icon, description, color }: StatCardProps) => (
    <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <div className={`p-2 rounded-full ${color}`}>{icon}</div>
        </CardHeader>
        <CardContent>
            <div className="text-2xl font-bold">{value}</div>
            <p className="text-xs text-muted-foreground">{description}</p>
        </CardContent>
    </Card>
);

export default function OwnerAdminDashboard() {
    const auth = useAuth();

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ["user", auth.userId],
        queryFn: async () => await getUser(),
        refetchOnWindowFocus: false,
    }) as any;

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (isError) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <h3 className="text-xl font-semibold">Error loading dashboard</h3>
                <p className="text-muted-foreground">{error?.toString() || "An unknown error occurred"}</p>
            </div>
        );
    }

    if (!data?.data) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] space-y-4">
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
                <h3 className="text-xl font-semibold">No user data available</h3>
                <p className="text-muted-foreground">We couldn&apos;t load your user profile</p>
            </div>
        );
    }

    const user: iUser = data.data as iUser;

    // Calculate summary statistics
    const businesses = user.businesses || [];
    const allProducts = businesses.flatMap((business) => business.products || []);

    const getReviewCount = (product: iProduct) =>
        product._count?.reviews ?? product.reviews?.length ?? 0;

    const totalReviews = allProducts.reduce((sum, product) => sum + getReviewCount(product), 0);

    // Calculate average rating, excluding products with no reviews (rating = 0)
    const productsWithReviews = allProducts.filter(product => getReviewCount(product) > 0);
    const averageRating = productsWithReviews.length > 0
        ? (productsWithReviews.reduce((sum, product) => sum + product.rating, 0) / productsWithReviews.length).toFixed(1)
        : null; // null indicates no reviews yet
    
    const totalViews = allProducts.reduce((sum, product) => sum + (product.viewCount || 0), 0);

    // Get most recent products
    const recentProducts = [...allProducts].sort((a, b) =>
        new Date(b.updatedAt || b.createdDate).getTime() - new Date(a.updatedAt || a.createdDate).getTime()
    ).slice(0, 5);

    return (
        <div className="space-y-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <h2 className="text-3xl font-bold tracking-tight">Owner Dashboard</h2>
                <div className="flex flex-col md:flex-row gap-2">
                    <Button asChild variant="outline" className="w-full md:w-auto">
                        <Link href="/owner-admin/products">
                            <PackageOpen className="mr-2 h-4 w-4" />
                            Manage Products
                        </Link>
                    </Button>
                    <Button asChild className="w-full md:w-auto">
                        <Link href="/submit">
                            <TrendingUp className="mr-2 h-4 w-4" />
                            Add New Business
                        </Link>
                    </Button>
                </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <StatCard
                    title="Total Products"
                    value={allProducts.length}
                    icon={<PackageOpen className="h-4 w-4 text-white" />}
                    description="Across all your businesses"
                    color="bg-blue-500"
                />
                <StatCard
                    title="Total Reviews"
                    value={totalReviews}
                    icon={<Star className="h-4 w-4 text-white" />}
                    description="Customer feedback received"
                    color="bg-yellow-500"
                />
                <StatCard
                    title="Average Rating"
                    value={averageRating || "No reviews yet"}
                    icon={<TrendingUp className="h-4 w-4 text-white" />}
                    description="Across all products"
                    color="bg-green-500"
                />
                <StatCard
                    title="Total Views"
                    value={totalViews}
                    icon={<Users className="h-4 w-4 text-white" />}
                    description="Product page impressions"
                    color="bg-purple-500"
                />
            </div>

            <Tabs defaultValue="recent-products" className="space-y-4">
                <TabsList>
                    <TabsTrigger value="recent-products">Recent Products</TabsTrigger>
                    <TabsTrigger value="businesses">Your Businesses</TabsTrigger>
                </TabsList>

                <TabsContent value="recent-products" className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {recentProducts.length > 0 ? (
                            recentProducts.map((product) => (
                                <ProductCard key={product.id} product={product} />
                            ))
                        ) : (
                            <div className="col-span-full bg-muted p-6 rounded-lg text-center">
                                <h3 className="font-medium text-lg">No products yet</h3>
                                <p className="text-muted-foreground mt-2">
                                    Add your first product to get started
                                </p>
                                <Button asChild className="mt-4">
                                    <Link href="/submit">Add New Business</Link>
                                </Button>
                            </div>
                        )}
                    </div>
                </TabsContent>

                <TabsContent value="businesses" className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {businesses.length > 0 ? (
                            businesses.map((business) => (
                                <BusinessCard key={business.id} business={business} />
                            ))
                        ) : (
                            <div className="col-span-full bg-muted p-6 rounded-lg text-center">
                                <h3 className="font-medium text-lg">No businesses yet</h3>
                                <p className="text-muted-foreground mt-2">
                                    Add your first business to get started
                                </p>
                                <Button asChild className="mt-4">
                                    <Link href="/submit">Add New Business</Link>
                                </Button>
                            </div>
                        )}
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}

// Product Card Component
function ProductCard({ product }: { product: iProduct }) {
    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-lg font-semibold truncate">{product.name}</CardTitle>
                <CardDescription className="flex items-center space-x-2">
                    {(product._count?.reviews || 0) > 0 ? (
                        <>
                            <ReadOnlyRating
                                name={`product-${product.id}`}
                                rating={product.rating}
                                size="sm"
                            />
                            <span>
                                {formatRatingDisplay(product.rating)} ({product._count?.reviews || 0} reviews)
                            </span>
                        </>
                    ) : (
                        <>
                            <ReadOnlyRating
                                name={`product-${product.id}`}
                                rating={0}
                                size="sm"
                            />
                            <span>No reviews yet</span>
                        </>
                    )}
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
                <div className="flex items-center text-sm space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">
                        Updated {new Date(product.updatedAt || product.createdDate).toLocaleDateString()}
                    </span>
                </div>
                <div className="flex flex-col space-y-2 md:flex-row md:items-center md:space-x-2 md:space-y-0">
                    <Button asChild variant="outline" size="sm">
                        <Link href={`/product/${product.id}`}>
                            View Product
                        </Link>
                    </Button>
                    <Button asChild size="sm">
                        <Link href={`/product/${product.id}`}>
                            Manage
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}

// Business Card Component
function BusinessCard({ business }: { business: iBusiness }) {
    const productCount = business.products?.length || 0;

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-lg font-semibold">
                    {business.ownerName || "Your Business"}
                </CardTitle>
                <CardDescription>
                    {business.businessDescription || "Business management"}
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Products:</span>
                    <span className="font-medium">{productCount}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Subscription:</span>
                    <span className="font-medium capitalize">{business.subscriptionStatus}</span>
                </div>
                {business.subscriptionExpiry && (
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Expires:</span>
                        <span className="font-medium">
                            {new Date(business.subscriptionExpiry).toLocaleDateString()}
                        </span>
                    </div>
                )}
                <div className="flex flex-col space-y-2 md:flex-row md:items-center md:space-x-2 md:space-y-0">
                    <Button asChild variant="outline" size="sm">
                        <Link href={`/owner-admin/subscription?id=${business.id}`}>
                            Subscription
                        </Link>
                    </Button>
                    <Button asChild size="sm">
                        <Link href={`/owner-admin/products?business=${business.id}`}>
                            Products
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}