"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Bell, Send, Users, User, Info, CheckCircle, AlertTriangle, XCircle, Search } from "lucide-react";
import { createSystemNotification } from "@/app/util/NotificationFunctions";

interface NotificationForm {
  title: string;
  message: string;
  icon: "info" | "success" | "warning" | "error";
  targetType: "broadcast" | "targeted";
  userIds: string;
}

interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  _count: {
    reviews: number;
    comments: number;
    product: number;
  };
}

const iconOptions = [
  { value: "info", label: "Info", icon: Info, color: "text-blue-500" },
  { value: "success", label: "Success", icon: CheckCircle, color: "text-green-500" },
  { value: "warning", label: "Warning", icon: AlertTriangle, color: "text-yellow-500" },
  { value: "error", label: "Error", icon: XCircle, color: "text-red-500" },
] as const;

export default function AdminNotificationsPage() {
  const { userId } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingUsers, setIsFetchingUsers] = useState(false);
  const [form, setForm] = useState<NotificationForm>({
    title: "",
    message: "",
    icon: "info",
    targetType: "broadcast",
    userIds: "",
  });
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  // Fetch all users for admin
  useEffect(() => {
    const fetchUsers = async () => {
      if (form.targetType !== "targeted") return;
      
      setIsFetchingUsers(true);
      try {
        const response = await fetch("/api/get/users/all");
        const data = await response.json();
        
        if (data.success) {
          setUsers(data.data);
          setFilteredUsers(data.data);
        }
      } catch (error) {
        console.error("Error fetching users:", error);
        toast.error("Failed to load users");
      } finally {
        setIsFetchingUsers(false);
      }
    };

    fetchUsers();
  }, [form.targetType]);

  // Filter users based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredUsers(users);
      return;
    }
    
    const term = searchTerm.toLowerCase();
    const filtered = users.filter(user => 
      user.username.toLowerCase().includes(term) ||
      user.email.toLowerCase().includes(term) ||
      (user.firstName && user.firstName.toLowerCase().includes(term)) ||
      (user.lastName && user.lastName.toLowerCase().includes(term))
    );
    
    setFilteredUsers(filtered);
  }, [searchTerm, users]);

  // Update form userIds when selectedUsers changes
  useEffect(() => {
    setForm(prev => ({
      ...prev,
      userIds: selectedUsers.join(", ")
    }));
  }, [selectedUsers]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.title.trim() || !form.message.trim()) {
      toast.error("Title and message are required");
      return;
    }

    setIsLoading(true);
    
    try {
      const userIdArray = form.targetType === "targeted" 
        ? selectedUsers
        : [];

      const success = await createSystemNotification(
        userIdArray,
        form.title,
        form.message,
        form.icon
      );

      if (success) {
        toast.success(
          form.targetType === "broadcast" 
            ? "Broadcast notification sent successfully!" 
            : `Targeted notification sent to ${userIdArray.length} user(s)!`
        );
        
        // Reset form
        setForm({
          title: "",
          message: "",
          icon: "info",
          targetType: "broadcast",
          userIds: "",
        });
        setSelectedUsers([]);
        setSearchTerm("");
      } else {
        toast.error("Failed to send notification. Please try again.");
      }
    } catch (error) {
      console.error("Error sending notification:", error);
      toast.error("An error occurred while sending the notification");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const selectedIcon = iconOptions.find(opt => opt.value === form.icon);

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">System Notifications</h1>
        <p className="text-gray-600">
          Send system-wide announcements and targeted notifications to users.
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Notification Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Create Notification
              </CardTitle>
              <CardDescription>
                Send announcements, updates, or alerts to users in real-time.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Title */}
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <Input
                    id="title"
                    value={form.title}
                    onChange={(e) => setForm({ ...form, title: e.target.value })}
                    placeholder="e.g., New Feature Release, Maintenance Alert"
                    maxLength={100}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {form.title.length}/100 characters
                  </p>
                </div>

                {/* Message */}
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <Textarea
                    id="message"
                    value={form.message}
                    onChange={(e) => setForm({ ...form, message: e.target.value })}
                    placeholder="Detailed message content..."
                    rows={4}
                    maxLength={500}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {form.message.length}/500 characters
                  </p>
                </div>

                {/* Icon Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Icon Type
                  </label>
                  <Select value={form.icon} onValueChange={(value: any) => setForm({ ...form, icon: value })}>
                    <SelectTrigger>
                      <SelectValue>
                        {selectedIcon && (
                          <div className="flex items-center gap-2">
                            <selectedIcon.icon className={`h-4 w-4 ${selectedIcon.color}`} />
                            {selectedIcon.label}
                          </div>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {iconOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <option.icon className={`h-4 w-4 ${option.color}`} />
                            {option.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Target Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Audience
                  </label>
                  <Select value={form.targetType} onValueChange={(value: any) => setForm({ ...form, targetType: value })}>
                    <SelectTrigger>
                      <SelectValue>
                        <div className="flex items-center gap-2">
                          {form.targetType === "broadcast" ? (
                            <>
                              <Users className="h-4 w-4" />
                              Broadcast to All Users
                            </>
                          ) : (
                            <>
                              <User className="h-4 w-4" />
                              Targeted Users
                            </>
                          )}
                        </div>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="broadcast">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Broadcast to All Users
                        </div>
                      </SelectItem>
                      <SelectItem value="targeted">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Targeted Users
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* User Selection (only for targeted) */}
                {form.targetType === "targeted" && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Users *
                      </label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Search by username, email, or name..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {selectedUsers.length} user(s) selected
                      </p>
                    </div>
                    
                    <div className="max-h-60 overflow-y-auto border rounded-md">
                      {isFetchingUsers ? (
                        <div className="p-4 text-center text-gray-500">
                          Loading users...
                        </div>
                      ) : filteredUsers.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          {searchTerm ? "No users found" : "No users available"}
                        </div>
                      ) : (
                        <ul className="divide-y">
                          {filteredUsers.map((user) => (
                            <li 
                              key={user.id}
                              className={`p-3 hover:bg-gray-50 cursor-pointer flex items-center justify-between ${selectedUsers.includes(user.id) ? 'bg-blue-50' : ''}`}
                              onClick={() => toggleUserSelection(user.id)}
                            >
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                  <span className="text-xs font-medium text-gray-700">
                                    {user.firstName?.[0] || user.username?.[0] || 'U'}
                                  </span>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">
                                    {user.firstName && user.lastName 
                                      ? `${user.firstName} ${user.lastName}` 
                                      : user.username}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    {user.email}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4 text-xs text-gray-500">
                                <span>{user._count.reviews} reviews</span>
                                <span>{user._count.comments} comments</span>
                                <span>{user._count.product} products</span>
                              </div>
                              {selectedUsers.includes(user.id) && (
                                <div className="ml-2 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
                                  <div className="w-2 h-2 rounded-full bg-white"></div>
                                </div>
                              )}
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  </div>
                )}

                <Button type="submit" disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send Notification
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Usage Guide */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Usage Guide
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Broadcast Notifications</h3>
                <p className="text-sm text-gray-600 mb-2">
                  Send announcements to all users (e.g., feature releases, maintenance alerts).
                </p>
                <Badge variant="secondary">All Users</Badge>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Targeted Notifications</h3>
                <p className="text-sm text-gray-600 mb-2">
                  Send personalized messages to specific users (e.g., account updates, billing notices).
                </p>
                <Badge variant="secondary">Specific Users</Badge>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Icon Types</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Info className="h-4 w-4 text-blue-500" />
                    <span>Info - General announcements</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Success - Positive updates</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <span>Warning - Important notices</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Error - Critical issues</span>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Best Practices</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Keep titles concise and clear</li>
                  <li>• Use appropriate icon types</li>
                  <li>• Test targeted notifications with yourself first</li>
                  <li>• Avoid sending too many notifications</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
