"use client";

import { RestrictedUsernamesManagement } from "@/components/admin/restricted-usernames-management";

export default function RestrictedUsernamesPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Manage Restricted Usernames</h1>
      <p className="text-muted-foreground">
        Add, remove, and manage restricted usernames to prevent users from choosing reserved names.
      </p>
      <RestrictedUsernamesManagement />
    </div>
  );
}
