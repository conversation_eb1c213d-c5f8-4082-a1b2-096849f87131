"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ActionConfig {
  label: string;
  action: string;
  confirm?: string; // Optional confirmation message
  category: 'redis' | 'react-query';
}

const ACTIONS: ActionConfig[] = [
  {
    label: "Invalidate All Products Cache",
    action: "invalidate-all-products",
    confirm: "Are you sure you want to invalidate ALL product caches?",
    category: 'redis'
  },
  {
    label: "Invalidate Search Cache",
    action: "invalidate-search",
    category: 'redis'
  },
  {
    label: "Invalidate Admin Cache",
    action: "invalidate-admin",
    category: 'redis'
  },
  {
    label: "Invalidate Review Caches",
    action: "invalidate-reviews",
    confirm: "This will clear latest, popular, and trending review caches.",
    category: 'redis'
  },
  {
    label: "Invalidate ALL Caches (Complete Redis Flush)",
    action: "invalidate-all",
    confirm:
      "This will completely clear the entire Redis cache database. This is very expensive and will impact performance significantly. Are you sure?",
    category: 'redis'
  },
];

export default function AdminCachePage() {
  const [loadingAction, setLoadingAction] = useState<string | null>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [redisKeys, setRedisKeys] = useState<string[]>([]);
  const [keyPattern, setKeyPattern] = useState('*');
  const [keyValue, setKeyValue] = useState<any>(null);
  const [selectedKey, setSelectedKey] = useState('');
  const [reactQueryKeys, setReactQueryKeys] = useState<any[]>([]);
  
  const queryClient = useQueryClient();

  // Fetch cache statistics
  useEffect(() => {
    const fetchCacheStats = async () => {
      try {
        const res = await fetch("/api/admin/cache/inspect?action=stats");
        const data = await res.json();
        if (data.success) {
          setCacheStats(data.data);
        }
      } catch (error) {
        console.error("Error fetching cache stats:", error);
      }
    };
    
    fetchCacheStats();
  }, []);

  // Fetch React Query cache keys
  useEffect(() => {
    const fetchReactQueryKeys = () => {
      const queryCache = queryClient.getQueryCache();
      const queries = queryCache.getAll();
      const keys = queries.map(query => ({
        queryKey: query.queryKey,
        state: query.state,
        observers: query.getObserversCount()
      }));
      setReactQueryKeys(keys);
    };
    
    fetchReactQueryKeys();
  }, [queryClient]);

  const runAction = async (config: ActionConfig) => {
    if (config.confirm && !window.confirm(config.confirm)) {
      return;
    }
    try {
      setLoadingAction(config.action);
      
      // Step 1: Invalidate Redis cache first
      const res = await fetch("/api/admin/cache/invalidate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action: config.action }),
      });
      const data = await res.json();
      
      if (res.ok && data.success) {
        // Step 2: If Redis invalidation succeeded, also invalidate React Query cache
        try {
          // Determine which React Query caches to invalidate based on action
          switch (config.action) {
            case "invalidate-all-products":
              await queryClient.invalidateQueries({ queryKey: ["products"] });
              await queryClient.invalidateQueries({ queryKey: ["heroPopularReviews"] });
              break;
            case "invalidate-search":
              await queryClient.invalidateQueries({ queryKey: ["products"] });
              break;
            case "invalidate-reviews":
              await queryClient.invalidateQueries({ queryKey: ["reviews"] });
              await queryClient.invalidateQueries({ queryKey: ["comments"] });
              await queryClient.invalidateQueries({ queryKey: ["heroPopularReviews"] });
              break;
            case "invalidate-all":
              // Clear all React Query cache
              queryClient.clear();
              break;
            case "invalidate-admin":
              await queryClient.invalidateQueries({ queryKey: ["admin"] });
              break;
          }
          
          toast.success(`${data.message} React Query cache also cleared.`);
          
          // Refresh cache stats
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } catch (reactQueryError) {
          console.error("React Query invalidation failed:", reactQueryError);
          toast.success(`${data.message} (React Query cache clear failed - you may need to refresh)`);
        }
      } else {
        toast.error(data.message ?? "Failed to invalidate cache.");
      }
    } catch (error) {
      toast.error("Unexpected error invalidating cache.");
      console.error("Cache invalidation error", error);
    } finally {
      setLoadingAction(null);
    }
  };

  const fetchRedisKeys = async () => {
    try {
      const res = await fetch(`/api/admin/cache/inspect?action=keys&pattern=${encodeURIComponent(keyPattern)}`);
      const data = await res.json();
      if (data.success) {
        setRedisKeys(data.data.keys);
      }
    } catch (error) {
      toast.error("Error fetching Redis keys");
      console.error("Error fetching Redis keys:", error);
    }
  };

  const fetchKeyValue = async (key: string) => {
    try {
      const res = await fetch(`/api/admin/cache/inspect?action=key-value&key=${encodeURIComponent(key)}`);
      const data = await res.json();
      if (data.success) {
        setKeyValue(data.data);
        setSelectedKey(key);
      }
    } catch (error) {
      toast.error("Error fetching key value");
      console.error("Error fetching key value:", error);
    }
  };

  const invalidateReactQueryCache = async (queryKey: string[]) => {
    try {
      await queryClient.invalidateQueries({ queryKey });
      toast.success(`React Query cache for ${queryKey.join('.')} invalidated`);
      
      // Refresh React Query keys
      setTimeout(() => {
        const queryCache = queryClient.getQueryCache();
        const queries = queryCache.getAll();
        const keys = queries.map(query => ({
          queryKey: query.queryKey,
          state: query.state,
          observers: query.getObserversCount()
        }));
        setReactQueryKeys(keys);
      }, 500);
    } catch (error) {
      toast.error("Error invalidating React Query cache");
      console.error("Error invalidating React Query cache:", error);
    }
  };

  const clearAllReactQueryCache = () => {
    if (window.confirm("Are you sure you want to clear ALL React Query cache?")) {
      queryClient.clear();
      toast.success("All React Query cache cleared");
      
      // Refresh React Query keys
      setTimeout(() => {
        setReactQueryKeys([]);
      }, 500);
    }
  };

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Cache Management</h1>
        <p className="text-sm md:text-base text-muted-foreground mb-6">
          Manage Redis and React Query caches. These operations impact performance; please use sparingly.
        </p>
      </div>

      {/* Cache Statistics */}
      {cacheStats && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg md:text-xl">Cache Statistics</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Hits</span>
              <span className="text-lg font-semibold">{cacheStats.stats.hits}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Misses</span>
              <span className="text-lg font-semibold">{cacheStats.stats.misses}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Hit Rate</span>
              <span className="text-lg font-semibold">{cacheStats.stats.hitRate.toFixed(2)}%</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Health</span>
              <Badge variant={cacheStats.health ? "default" : "destructive"}>
                {cacheStats.health ? "Healthy" : "Unhealthy"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="invalidate" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="invalidate">Invalidate Cache</TabsTrigger>
          <TabsTrigger value="redis">Redis Inspection</TabsTrigger>
          <TabsTrigger value="react-query">React Query</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>
        
        <TabsContent value="invalidate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Invalidate Cache Groups</CardTitle>
              <p className="text-sm text-muted-foreground">
                Invalidate specific cache groups in both Redis and React Query
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {ACTIONS.map((cfg) => (
                  <Button
                    key={cfg.action}
                    variant="secondary"
                    disabled={loadingAction !== null}
                    className="whitespace-normal text-left h-auto py-3 px-4"
                    onClick={() => runAction(cfg)}
                  >
                    <div className="text-left">
                      <div className="font-medium">{cfg.label}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {cfg.category === 'redis' ? 'Redis + React Query' : 'React Query'}
                      </div>
                      {loadingAction === cfg.action && (
                        <div className="text-xs mt-1">Running...</div>
                      )}
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="redis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Redis Cache Inspection</CardTitle>
              <p className="text-sm text-muted-foreground">
                Inspect Redis cache keys and values
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col md:flex-row gap-3">
                <div className="flex-1 space-y-2">
                  <Label htmlFor="key-pattern">Key Pattern</Label>
                  <Input
                    id="key-pattern"
                    value={keyPattern}
                    onChange={(e) => setKeyPattern(e.target.value)}
                    placeholder="Enter key pattern (e.g., reviewit:v2:*)"
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={fetchRedisKeys} className="w-full md:w-auto">
                    Fetch Keys
                  </Button>
                </div>
              </div>
              
              {redisKeys.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">Matching Keys ({redisKeys.length})</h3>
                  <ScrollArea className="h-60 border rounded-md p-2">
                    <div className="space-y-1">
                      {redisKeys.map((key, index) => (
                        <div 
                          key={index} 
                          className="flex justify-between items-center p-2 hover:bg-muted rounded cursor-pointer"
                          onClick={() => fetchKeyValue(key)}
                        >
                          <span className="text-sm truncate mr-2">{key}</span>
                          <Button size="sm" variant="outline" onClick={(e) => {
                            e.stopPropagation();
                            fetchKeyValue(key);
                          }}>
                            View
                          </Button>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
              
              {keyValue && (
                <div className="mt-4">
                  <Separator className="my-4" />
                  <h3 className="font-medium mb-2">Key Value: {selectedKey}</h3>
                  <div className="border rounded-md p-3 bg-muted">
                    <pre className="text-xs overflow-x-auto max-h-60">
                      {typeof keyValue.value === 'object' 
                        ? JSON.stringify(keyValue.value, null, 2)
                        : String(keyValue.value)
                      }
                    </pre>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="react-query" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                <div>
                  <CardTitle>React Query Cache</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Inspect and manage React Query cache
                  </p>
                </div>
                <Button variant="destructive" onClick={clearAllReactQueryCache} size="sm">
                  Clear All Cache
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {reactQueryKeys.length > 0 ? (
                <ScrollArea className="h-96 border rounded-md p-2">
                  <div className="space-y-3">
                    {reactQueryKeys.map((item, index) => (
                      <div key={index} className="p-3 border rounded-md">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                          <div className="font-mono text-sm break-all">
                            {Array.isArray(item.queryKey) 
                              ? item.queryKey.join(' > ')
                              : String(item.queryKey)
                            }
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">
                              {item.observers} observers
                            </Badge>
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => invalidateReactQueryCache(Array.isArray(item.queryKey) ? item.queryKey : [item.queryKey])}
                            >
                              Invalidate
                            </Button>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            Status: {item.state.status}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            Fetch Status: {item.state.fetchStatus}
                          </Badge>
                          {item.state.dataUpdatedAt && (
                            <Badge variant="outline" className="text-xs">
                              Updated: {new Date(item.state.dataUpdatedAt).toLocaleTimeString()}
                            </Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No React Query cache data found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="monitoring" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cache Monitoring</CardTitle>
              <p className="text-sm text-muted-foreground">
                Real-time cache performance metrics
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Redis Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Status</span>
                        <span>{cacheStats?.health ? 'Healthy' : 'Unhealthy'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Hit Rate</span>
                        <span>{cacheStats?.stats?.hitRate?.toFixed(2) || '0.00'}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Total Requests</span>
                        <span>{cacheStats?.stats?.totalRequests || 0}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">React Query Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Active Queries</span>
                        <span>{reactQueryKeys.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">With Observers</span>
                        <span>{reactQueryKeys.filter(q => q.observers > 0).length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Stale Queries</span>
                        <span>{reactQueryKeys.filter(q => q.state.isStale).length}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="mt-6">
                <h3 className="font-medium mb-3">Cache Usage Tips</h3>
                <ul className="text-sm space-y-2 text-muted-foreground">
                  <li>• Use specific invalidation when possible instead of clearing all cache</li>
                  <li>• Monitor hit rates - below 80% may indicate cache inefficiency</li>
                  <li>• React Query cache is currently configured for immediate expiration</li>
                  <li>• Large cache sizes may impact application performance</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
