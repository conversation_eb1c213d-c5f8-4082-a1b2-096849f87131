import React from "react";

const AdminDocsPage = () => {
  return (
    <div className="max-w-4xl mx-auto p-8">
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          Review-it Documentation
        </h1>
        <p className="text-lg text-gray-600">
          Comprehensive documentation for the Review-it platform, covering all
          aspects from basic operations to advanced features and development
          practices.
        </p>
      </div>

      <section className="mb-12">
        <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">
          Admin Section Documentation
        </h2>
        <p className="text-lg mb-6 text-gray-600">
          Core administrative features and platform management tools.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Project Overview
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              High-level overview of the Review-it project, including goals,
              architecture, technology stack, and development practices.
            </p>
            <a
              href="/admin/docs/project-overview"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              User Management
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Comprehensive guide for managing users, including CRUD operations,
              analytics, role-based access control, and bulk operations.
            </p>
            <a
              href="/admin/docs/user-management"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Product Management
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Complete product management system covering listings, categories,
              approval workflows, analytics, and bulk operations.
            </p>
            <a
              href="/admin/docs/product-management"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Review Management
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Advanced review moderation system with analytics, flagged content
              management, response system, and verification processes.
            </p>
            <a
              href="/admin/docs/review-management"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Product Reporting
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              System for managing user reports about products, including
              moderation workflows, status tracking, and integration with
              existing reporting infrastructure.
            </p>
            <a
              href="/admin/docs/product-reporting"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Bug Reports
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Comprehensive bug report management system with submission
              workflows, priority handling, analytics, and resolution tracking.
            </p>
            <a
              href="/admin/docs/bug-reports"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Promotions
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Full promotional campaign management including targeting,
              analytics, pricing models, and performance optimization tools.
            </p>
            <a
              href="/admin/docs/promotions"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Widget System
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Comprehensive widget system with two-tier security architecture,
              domain verification, token authentication, and monitoring tools.
            </p>
            <a
              href="/admin/docs/widgets"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">
          Technical Documentation
        </h2>
        <p className="text-lg mb-6 text-gray-600">
          API documentation, core concepts, and technical implementation
          details.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Image Management Documentation */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Image Management
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                New
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Dual Cloudinary setup, image optimization, client-side resizing, and multi-image upload components for different use cases.
            </p>
            <a
              href="/admin/docs/image-management"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          {/* Notifications Documentation */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Notification System
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                New
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Real-time notification architecture, data contracts, SSE hub, and
              admin-facing design decisions (e.g., no down-vote alerts).
            </p>
            <a
              href="/admin/docs/notifications"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              API Routes
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                Placeholder
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Complete API documentation covering authentication, user, product,
              review, and business endpoints.
            </p>
            <a
              href="/admin/docs/api-routes"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Core Concepts
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                Needs Content
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Fundamental concepts including component library, database schema,
              project structure, caching, and deployment.
            </p>
            <a
              href="/admin/docs/core-concepts"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Attention Analytics
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                New
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              How scroll-depth and engagement duration events are captured, cached, queried, and displayed in both dashboards. Includes SQL, Redis and React Query examples.
            </p>
            <a
              href="/admin/docs/attention-analytics"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Extending Documentation
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                Developer Guide
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Learn how to extend the documentation system using shared
              components and maintain consistency across user roles.
            </p>
            <a
              href="/admin/docs/extending-documentation"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">
          User-Facing Features
        </h2>
        <p className="text-lg mb-6 text-gray-600">
          Documentation for public-facing features and user experience
          components.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              User-Facing Routes
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Comprehensive documentation for all user-facing routes including
              home page, browsing, search, reviews, profiles, and business
              features.
            </p>
            <a
              href="/admin/docs/user-facing"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              Owner/Business Section
              <span className="inline-block ml-2 px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Comprehensive business owner documentation covering dashboard,
              analytics, product management, promotions, subscriptions, and
              settings.
            </p>
            <a
              href="/admin/docs/owner-business"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              View Documentation →
            </a>
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">
          Getting Started
        </h2>
        <p className="text-lg mb-6 text-gray-600">
          New to the platform? Start with these essential documents:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              🚀 Quick Start
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Begin with the Project Overview to understand the platform
              architecture and key features.
            </p>
            <a
              href="/admin/docs/project-overview"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              Start Here →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              👥 User Management
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Learn how to manage users, roles, and permissions in the admin
              dashboard.
            </p>
            <a
              href="/admin/docs/user-management"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              Learn More →
            </a>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200 hover:-translate-y-1">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">
              📦 Product Management
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Understand how to manage products, categories, and approval
              workflows.
            </p>
            <a
              href="/admin/docs/product-management"
              className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
            >
              Explore →
            </a>
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-3xl font-bold mb-4 pb-2 border-b border-gray-200 text-gray-900">
          Recent Implementation: Shared Documentation Components
        </h2>
        <p className="text-lg mb-6 text-gray-600">
          The documentation system has been enhanced with shared components
          architecture to provide business owners with curated documentation
          while maintaining security.
        </p>

        <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-lg p-6 border border-green-200 mb-6">
          <h3 className="text-xl font-bold mb-4 text-gray-900">What's New</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-green-900 mb-3">
                Business Owner Documentation
              </h4>
              <ul className="space-y-2 text-sm text-green-800">
                <li>
                  • Dedicated documentation hub at{" "}
                  <code>/owner-admin/docs</code>
                </li>
                <li>• Curated content relevant to business owners</li>
                <li>• No access to protected admin documentation</li>
                <li>• Mobile-optimized navigation with proper scrolling</li>
                <li>
                  • Comprehensive guides for products, reviews, and promotions
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-900 mb-3">
                Shared Components Architecture
              </h4>
              <ul className="space-y-2 text-sm text-blue-800">
                <li>
                  • Reusable documentation components in{" "}
                  <code>/src/components/docs</code>
                </li>
                <li>• Single source of truth for content</li>
                <li>
                  • Automatic updates across both admin and business sections
                </li>
                <li>• Role-based content rendering capabilities</li>
                <li>• Easy to extend and maintain</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Implementation Highlights
          </h3>
          <ul className="list-disc list-inside text-sm text-gray-600 space-y-2">
            <li>
              <strong>Security Compliant:</strong> Business owners cannot access
              protected <code>/admin</code> routes
            </li>
            <li>
              <strong>No Content Duplication:</strong> Shared components
              eliminate redundant documentation
            </li>
            <li>
              <strong>Consistent Experience:</strong> Same high-quality
              documentation across user roles
            </li>
            <li>
              <strong>Easy Maintenance:</strong> Update content once, affects
              both admin and business owner sections
            </li>
            <li>
              <strong>Extensible Design:</strong> New documentation can easily
              use the shared component pattern
            </li>
          </ul>
        </div>

        <div className="mt-6 text-center">
          <a
            href="/admin/docs/extending-documentation"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Learn How to Extend the Documentation System →
          </a>
        </div>
      </section>
    </div>
  );
};

export default AdminDocsPage;
