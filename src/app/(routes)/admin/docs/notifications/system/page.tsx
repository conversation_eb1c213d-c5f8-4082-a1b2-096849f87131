import React from "react";
import { ArrowLeft } from "lucide-react";

const SystemNotificationsDocsPage = () => {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Back link */}
      <div className="mb-6">
        <a href="/admin/docs/notifications" className="text-blue-600 hover:underline flex items-center gap-1">
          <ArrowLeft className="w-4 h-4" />
          Notification Docs
        </a>
      </div>

      {/* Header */}
      <div className="text-center mb-12 p-8 bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl">
        <h1 className="text-4xl font-bold mb-4 text-gray-900">System Notifications Integration Guide</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Comprehensive guide for implementing and using system notifications in the Review-it platform.
        </p>
      </div>

      {/* Overview */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Overview</h2>
        <p className="text-gray-700 leading-relaxed mb-4">
          The system notifications feature enables administrators to send real-time announcements and alerts to users throughout the application. This feature supports both broadcast notifications (to all users) and targeted notifications (to specific users).
        </p>
      </section>

      {/* Architecture */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Architecture</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Backend Components</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1 mb-4">
          <li><strong>API Endpoints</strong>: <code className="bg-gray-100 px-1 rounded">/notifications/system</code> (POST) for creating notifications</li>
          <li><strong>SSE Delivery</strong>: Real-time notification delivery via Server-Sent Events</li>
          <li><strong>Database</strong>: Notifications stored with metadata for targeting and tracking</li>
        </ul>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Frontend Components</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1">
          <li><strong>State Management</strong>: Jotai atoms for system notification state</li>
          <li><strong>SSE Hook</strong>: <code className="bg-gray-100 px-1 rounded">useSSENotifications</code> extended to handle system notifications</li>
          <li><strong>UI Components</strong>: Integrated into existing notification dropdown and admin interface</li>
        </ul>
      </section>

      {/* API Endpoints */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">API Endpoints</h2>
        <ul className="list-disc ml-6 text-gray-700 space-y-1">
          <li>
            <code className="font-mono">POST /notifications/system</code> – create broadcast or targeted system notification
          </li>
          <li>
            <code className="font-mono">PUT /notifications/{"{id}"}/read?type=system</code> – mark as read
          </li>
          <li>
            <code className="font-mono">GET /notifications/stream</code> – SSE (already in use)
          </li>
        </ul>
      </section>

      {/* Payload */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Payload Structure</h2>
        <p className="text-gray-700 leading-relaxed mb-4">
          Send JSON with the following keys. <strong>Omit</strong> or pass an empty array for <code>target_user_ids</code> to broadcast to all users.
        </p>
        <pre className="bg-slate-100 rounded p-4 overflow-x-auto text-sm">
{`{
  "target_user_ids": ["user_abc"],     // optional – broadcast when empty / missing
  "title": "Maintenance tonight",
  "message": "Service will be unavailable at 02:00 UTC.",
  "cta_url": "/status",               // optional deep-link
  "icon": "warning"                    // info | success | warning | error
}`}
        </pre>
      </section>

      {/* Usage Patterns */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Usage Patterns</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Creating System Notifications</h3>
        
        <h4 className="text-lg font-medium text-gray-700 mb-2">Broadcast Notification (All Users)</h4>
        <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm mb-4">
{`import { createSystemNotification } from "@/app/util/NotificationFunctions";

await createSystemNotification(
  [], // Empty array for broadcast
  "New Feature Release",
  "We've added exciting new features to improve your experience!",
  "success"
);`}
        </pre>
        
        <h4 className="text-lg font-medium text-gray-700 mb-2">Targeted Notification (Specific Users)</h4>
        <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm mb-4">
{`await createSystemNotification(
  ["user1", "user2", "user3"], // Specific user IDs
  "Account Update Required",
  "Please update your account information by the end of the week.",
  "warning"
);`}
        </pre>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Icon Types</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1">
          <li><strong>info</strong>: General information and updates</li>
          <li><strong>success</strong>: Positive announcements (feature releases, achievements)</li>
          <li><strong>warning</strong>: Important alerts requiring attention</li>
          <li><strong>error</strong>: Critical issues or urgent notifications</li>
        </ul>
      </section>

      {/* Recommended Use Cases */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Recommended Use Cases</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">1. Feature Releases</h3>
        <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm mb-4">
{`await createSystemNotification(
  [],
  "New Review Features Available",
  "Check out our enhanced review system with improved filtering and search capabilities.",
  "success"
);`}
        </pre>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">2. Maintenance Alerts</h3>
        <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm mb-4">
{`await createSystemNotification(
  [],
  "Scheduled Maintenance",
  "The platform will be under maintenance on Sunday from 2-4 AM EST. Expect brief interruptions.",
  "warning"
);`}
        </pre>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">3. Bug Fix Announcements</h3>
        <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm mb-4">
{`await createSystemNotification(
  [],
  "Bug Fixes Released",
  "We've resolved several issues with the notification system and improved overall performance.",
  "info"
);`}
        </pre>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">4. Account-Specific Notifications</h3>
        <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm mb-4">
{`// For users with pending actions
await createSystemNotification(
  ["user123"],
  "Action Required",
  "Your business verification is pending. Please complete the process to continue.",
  "warning"
);`}
        </pre>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">5. Billing Notifications</h3>
        <pre className="bg-slate-50 rounded p-4 overflow-x-auto text-sm">
{`// For premium users
await createSystemNotification(
  ["premium_user1", "premium_user2"],
  "Billing Update",
  "Your subscription will renew on March 1st. Update your payment method if needed.",
  "info"
);`}
        </pre>
      </section>

      {/* Admin Interface */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Admin Interface</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Access</h3>
        <p className="text-gray-700 leading-relaxed mb-4">
          Navigate to <code className="bg-gray-100 px-1 rounded">/admin/notifications</code> to access the system notification management interface.
        </p>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Features</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1 mb-4">
          <li><strong>Form-based Creation</strong>: Easy-to-use form for composing notifications</li>
          <li><strong>Target Selection</strong>: Choose between broadcast or targeted delivery</li>
          <li><strong>User Search & Filter</strong>: Search and select users by username, email, or name</li>
          <li><strong>Icon Selection</strong>: Visual icon picker with appropriate styling</li>
          <li><strong>Character Limits</strong>: Built-in validation (100 chars for title, 500 for message)</li>
          <li><strong>Real-time Feedback</strong>: Toast notifications for success/error states</li>
        </ul>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Best Practices</h3>
        <ol className="list-decimal ml-6 text-gray-700 space-y-1">
          <li><strong>Keep titles concise</strong> - Users see titles first in the notification dropdown</li>
          <li><strong>Use appropriate icons</strong> - Match the icon to the notification urgency/type</li>
          <li><strong>Test with targeted users</strong> - Send to a small group before broadcasting</li>
          <li><strong>Avoid notification spam</strong> - Be mindful of frequency and relevance</li>
          <li><strong>Clear messaging</strong> - Ensure the message is actionable and clear</li>
        </ol>
      </section>

      {/* Integration in Code */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Integration in Code</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Type Definitions</h3>
        <pre className="bg-slate-100 rounded p-4 overflow-x-auto text-sm mb-4">
{`interface SystemNotification {
  id: string;
  title: string;
  message: string;
  icon?: "info" | "success" | "warning" | "error";
  type: "system";
  read: boolean;
  created_at: string;
  user_ids?: string[]; // For targeted notifications
}`}
        </pre>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">API Integration</h3>
        <pre className="bg-slate-100 rounded p-4 overflow-x-auto text-sm mb-4">
{`// Create notification
const success = await createSystemNotification(userIds, title, message, icon);

// Mark as read
await markNotificationAsRead(notificationId, userId, "system");`}
        </pre>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Backend Integration</h3>
        <p className="text-gray-700 leading-relaxed mb-4">
          Any backend service can trigger a system notification by calling the endpoint. Example using <code>fetch</code> inside a server action:
        </p>
        <pre className="bg-slate-100 rounded p-4 overflow-x-auto text-sm">
{`await fetch(process.env.NOTIF_URL + '/notifications/system', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    target_user_ids: [userId],
    title: 'Review approved!',
    message: 'Your review is now public.',
    icon: 'success'
  })
});`}
        </pre>
      </section>

      {/* SSE Event Handling */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">SSE Event Handling</h2>
        <p className="text-gray-700 leading-relaxed mb-4">
          System notifications flow through the same SSE connection as other notifications with <code>type: "system"</code>.
          The system automatically handles these SSE events:
        </p>
        <ul className="list-disc ml-6 text-gray-700 space-y-1">
          <li><code className="bg-gray-100 px-1 rounded">new_notification</code> (type: system) - New system notification received</li>
          <li><code className="bg-gray-100 px-1 rounded">existing_notification</code> (type: system) - Historical notification on connection</li>
          <li><code className="bg-gray-100 px-1 rounded">notification_read</code> (type: system) - Notification marked as read</li>
        </ul>
      </section>

      {/* Security and Permissions */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Security and Permissions</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Admin Access</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1 mb-4">
          <li>Only authenticated admin users can access <code className="bg-gray-100 px-1 rounded">/admin/notifications</code></li>
          <li>User ID validation prevents unauthorized targeting</li>
          <li>Input sanitization prevents XSS attacks</li>
        </ul>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Rate Limiting</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1">
          <li>Consider implementing rate limiting for notification creation</li>
          <li>Monitor for abuse patterns in admin usage logs</li>
        </ul>
      </section>

      {/* Troubleshooting */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Troubleshooting</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Common Issues</h3>
        <ol className="list-decimal ml-6 text-gray-700 space-y-1 mb-4">
          <li><strong>Notifications not appearing</strong>: Check SSE connection status in browser dev tools</li>
          <li><strong>Admin page not accessible</strong>: Verify admin permissions and authentication</li>
          <li><strong>Targeted notifications failing</strong>: Validate user IDs exist in the system</li>
        </ol>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Debug Tools</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1">
          <li>Browser console shows SSE connection events</li>
          <li>Network tab displays notification API calls</li>
          <li>Local storage contains cached notification data</li>
        </ul>
      </section>

      {/* Future Enhancements */}
      <section className="mb-24">
        <h2 className="text-2xl font-bold mb-3 text-gray-900">Future Enhancements</h2>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Potential Improvements</h3>
        <ul className="list-disc ml-6 text-gray-700 space-y-1 mb-4">
          <li><strong>Scheduling</strong>: Ability to schedule notifications for future delivery</li>
          <li><strong>Templates</strong>: Pre-defined notification templates for common use cases</li>
          <li><strong>Analytics</strong>: Detailed read/engagement metrics</li>
          <li><strong>Rich Content</strong>: Support for links, images, or formatted text</li>
          <li><strong>Notification History</strong>: Admin interface to view sent notifications</li>
        </ul>
        <p className="text-gray-700">
          This documentation should be updated as the system evolves and new features are added.
        </p>
      </section>
    </div>
  );
};

export default SystemNotificationsDocsPage;
