"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>sible<PERSON><PERSON><PERSON>,
  BackToDocsButton,
  InfoBox,
} from "@/components/docs";
import { <PERSON><PERSON><PERSON><PERSON>gle, Calendar<PERSON>lock, RotateCcw } from "lucide-react";

interface APIEndpointProps {
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  endpoint: string;
  description: string;
  auth?: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  responseExample?: string;
  errorCodes?: Array<{
    code: number;
    description: string;
  }>;
}

function APIEndpoint({
  method,
  endpoint,
  description,
  auth,
  parameters,
  responseExample,
  errorCodes,
}: APIEndpointProps) {
  const methodColors = {
    GET: "bg-green-100 text-green-800 border-green-200",
    POST: "bg-blue-100 text-blue-800 border-blue-200",
    PUT: "bg-yellow-100 text-yellow-800 border-yellow-200",
    DELETE: "bg-red-100 text-red-800 border-red-200",
    PATCH: "bg-purple-100 text-purple-800 border-purple-200",
  } as const;

  return (
    <div className="border border-gray-200 rounded-lg p-4 mb-4 bg-white">
      <div className="flex items-center gap-3 mb-3">
        <span
          className={`px-2 py-1 text-xs font-semibold rounded border ${methodColors[method]}`}
        >
          {method}
        </span>
        <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
          {endpoint}
        </code>
        {auth && (
          <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded border border-orange-200">
            🔒 {auth}
          </span>
        )}
      </div>

      <p className="text-gray-700 mb-4">{description}</p>

      {parameters && parameters.length > 0 && (
        <div className="mb-4">
          <h5 className="font-semibold text-gray-900 mb-2">Parameters</h5>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-3 py-2 text-left font-medium text-gray-900">Name</th>
                  <th className="px-3 py-2 text-left font-medium text-gray-900">Type</th>
                  <th className="px-3 py-2 text-left font-medium text-gray-900">Required</th>
                  <th className="px-3 py-2 text-left font-medium text-gray-900">Description</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {parameters.map((param, index) => (
                  <tr key={index}>
                    <td className="px-3 py-2 font-mono text-blue-600">{param.name}</td>
                    <td className="px-3 py-2 font-mono text-gray-600">{param.type}</td>
                    <td className="px-3 py-2">
                      <span
                        className={`px-2 py-1 text-xs rounded ${param.required ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-600"}`}
                      >
                        {param.required ? "Required" : "Optional"}
                      </span>
                    </td>
                    <td className="px-3 py-2 text-gray-700">{param.description}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {responseExample && (
        <div className="mb-4">
          <h5 className="font-semibold text-gray-900 mb-2">Response Example</h5>
          <pre className="bg-gray-900 text-gray-100 p-3 rounded text-sm overflow-x-auto">
            {responseExample}
          </pre>
        </div>
      )}

      {errorCodes && errorCodes.length > 0 && (
        <div>
          <h5 className="font-semibold text-gray-900 mb-2">Error Codes</h5>
          <div className="space-y-1">
            {errorCodes.map((error, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <span className="font-mono text-red-600">{error.code}</span>
                <span className="text-gray-700">{error.description}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default function AnalyticsRecalculationDoc() {
  return (
    <DocsContainer>
      <DocsHeader
        title="Business Analytics Recalculation"
        description="Details on how aggregate metrics (views, reviews, average rating, etc.) are recalculated for businesses."
        status="in-progress"
        breadcrumbs={[
          { label: "Documentation", href: "/admin/docs" },
          { label: "Analytics Recalculation", href: "/admin/docs/analytics-recalculation" },
        ]}
      />

      <CollapsibleSection title="⏱️ Scheduled Cron Job" defaultOpen={true}>
        <APIEndpoint
          method="GET"
          endpoint="/api/cron/calculate-analytics"
          description="Recalculates analytics for ALL businesses. Triggered automatically by the Vercel Cron schedule (daily) but can be hit manually for debugging."
          auth="Localhost Only"
          responseExample={`{
  "ok": true,
  "message": "Analytics calculation completed. 42/42 businesses processed successfully",
  "results": {
    "total": 42,
    "successful": 42,
    "failed": 0,
    "errors": []
  },
  "timestamp": "2025-07-28T15:30:06.234Z"
}`}
          errorCodes={[
            { code: 403, description: "Forbidden – request not from localhost" },
            { code: 500, description: "Internal error during calculation" },
          ]}
        />
        <InfoBox type="info" icon={<CalendarClock className="h-4 w-4" />}>
          This endpoint is restricted to requests originating from <code>localhost</code> to prevent abuse. Use <code>curl http://localhost:3000/api/cron/calculate-analytics</code> during local development.
        </InfoBox>
      </CollapsibleSection>

      <CollapsibleSection title="🔄 Incremental Recalculation" defaultOpen={false}>
        <p className="text-gray-700 mb-2">
          A per-business recalculation endpoint is <span className="font-semibold">not yet implemented</span>. Planned design:
        </p>
        <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
          <li>
            <code>POST /api/business/&#123;businessId&#125;/analytics/recalculate</code> (Admin / Owner only)
          </li>
          <li>Triggers <code>calculateBusinessGrowthMetrics(businessId)</code> for that single business.</li>
          <li>Returns the freshly computed analytics row.</li>
        </ul>
      </CollapsibleSection>

      <CollapsibleSection title="🛠️ Developer Notes" defaultOpen={false}>
        <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
          <li>
            Core aggregation logic lives in <code>src/app/lib/analytics-engine.ts</code> → <code>calculateBusinessGrowthMetrics</code>.
          </li>
          <li>
            Cron job iterates all businesses and calls that helper.
          </li>
          <li>
            Review creation / deletion hooks should enqueue an incremental recalculation via Redis queue (<code>src/app/util/redisMetrics.ts</code>).
          </li>
        </ul>
      </CollapsibleSection>

      <BackToDocsButton />
    </DocsContainer>
  );
}
