import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BackToDocsButton,
  InfoBox,
} from "@/components/docs";
import { AlertTriangle } from "lucide-react";

interface APIEndpointProps {
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  endpoint: string;
  description: string;
  auth?: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  responseExample?: string;
  errorCodes?: Array<{
    code: number;
    description: string;
  }>;
}

function APIEndpoint({
  method,
  endpoint,
  description,
  auth,
  parameters,
  responseExample,
  errorCodes,
}: APIEndpointProps) {
  const methodColors = {
    GET: "bg-green-100 text-green-800 border-green-200",
    POST: "bg-blue-100 text-blue-800 border-blue-200",
    PUT: "bg-yellow-100 text-yellow-800 border-yellow-200",
    DELETE: "bg-red-100 text-red-800 border-red-200",
    PATCH: "bg-purple-100 text-purple-800 border-purple-200",
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 mb-4 bg-white">
      <div className="flex items-center gap-3 mb-3">
        <span
          className={`px-2 py-1 text-xs font-semibold rounded border ${methodColors[method]}`}
        >
          {method}
        </span>
        <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
          {endpoint}
        </code>
        {auth && (
          <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded border border-orange-200">
            🔒 {auth}
          </span>
        )}
      </div>

      <p className="text-gray-700 mb-4">{description}</p>

      {parameters && parameters.length > 0 && (
        <div className="mb-4">
          <h5 className="font-semibold text-gray-900 mb-2">Parameters</h5>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-3 py-2 text-left font-medium text-gray-900">
                    Name
                  </th>
                  <th className="px-3 py-2 text-left font-medium text-gray-900">
                    Type
                  </th>
                  <th className="px-3 py-2 text-left font-medium text-gray-900">
                    Required
                  </th>
                  <th className="px-3 py-2 text-left font-medium text-gray-900">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {parameters.map((param, index) => (
                  <tr key={index}>
                    <td className="px-3 py-2 font-mono text-blue-600">
                      {param.name}
                    </td>
                    <td className="px-3 py-2 font-mono text-gray-600">
                      {param.type}
                    </td>
                    <td className="px-3 py-2">
                      <span
                        className={`px-2 py-1 text-xs rounded ${param.required ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-600"}`}
                      >
                        {param.required ? "Required" : "Optional"}
                      </span>
                    </td>
                    <td className="px-3 py-2 text-gray-700">
                      {param.description}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {responseExample && (
        <div className="mb-4">
          <h5 className="font-semibold text-gray-900 mb-2">Response Example</h5>
          <pre className="bg-gray-900 text-gray-100 p-3 rounded text-sm overflow-x-auto">
            {responseExample}
          </pre>
        </div>
      )}

      {errorCodes && errorCodes.length > 0 && (
        <div>
          <h5 className="font-semibold text-gray-900 mb-2">Error Codes</h5>
          <div className="space-y-1">
            {errorCodes.map((error, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <span className="font-mono text-red-600">{error.code}</span>
                <span className="text-gray-700">{error.description}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default function APIRoutesPage() {
  return (
    <DocsContainer>
      <DocsHeader
        title="API Routes Documentation"
        description="Complete reference for all API endpoints in the ReviewIt platform."
        status="complete"
        breadcrumbs={[
          { label: "Documentation", href: "/admin/docs" },
          { label: "API Routes", href: "/admin/docs/api-routes" },
        ]}
      />

      <InfoBox
        type="warning"
        title="Authentication Required"
        icon={<AlertTriangle className="h-5 w-5" />}
      >
        Most API endpoints require authentication. Include the session token in
        your requests. Admin-only endpoints require elevated permissions.
      </InfoBox>

      <CollapsibleSection title="🔐 Authentication Routes" defaultOpen={true}>
        <APIEndpoint
          method="POST"
          endpoint="/api/auth/signin"
          description="Sign in a user with credentials or OAuth provider"
          parameters={[
            {
              name: "email",
              type: "string",
              required: true,
              description: "User email address",
            },
            {
              name: "password",
              type: "string",
              required: false,
              description: "Password (for credential login)",
            },
            {
              name: "provider",
              type: "string",
              required: false,
              description: "OAuth provider (google)",
            },
          ]}
          responseExample={`{
  "success": true,
  "user": {
    "id": "user123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "user"
  },
  "session": "session_token_here"
}`}
          errorCodes={[
            { code: 401, description: "Invalid credentials" },
            { code: 400, description: "Missing required fields" },
          ]}
        />

        <APIEndpoint
          method="POST"
          endpoint="/api/auth/signout"
          description="Sign out the current user and invalidate session"
          auth="User"
          responseExample={`{
  "success": true,
  "message": "Successfully signed out"
}`}
        />
      </CollapsibleSection>

      <CollapsibleSection title="👤 User Management Routes" defaultOpen={true}>
        <APIEndpoint
          method="GET"
          endpoint="/api/users"
          description="Get a list of all users (paginated)"
          auth="Admin"
          parameters={[
            {
              name: "page",
              type: "number",
              required: false,
              description: "Page number (default: 1)",
            },
            {
              name: "limit",
              type: "number",
              required: false,
              description: "Items per page (default: 10)",
            },
            {
              name: "search",
              type: "string",
              required: false,
              description: "Search by name or email",
            },
            {
              name: "role",
              type: "string",
              required: false,
              description: "Filter by user role",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user123",
        "email": "<EMAIL>",
        "name": "John Doe",
        "role": "user",
        "createdAt": "2024-01-01T00:00:00Z",
        "isActive": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 150,
      "pages": 15
    }
  }
}`}
        />

        <APIEndpoint
          method="GET"
          endpoint="/api/users/[id]"
          description="Get detailed information about a specific user"
          auth="Admin or Own Profile"
          parameters={[
            {
              name: "id",
              type: "string",
              required: true,
              description: "User ID",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "id": "user123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "user",
    "profile": {
      "bio": "Love reviewing products!",
      "avatar": "https://example.com/avatar.jpg"
    },
    "stats": {
      "reviewCount": 25,
      "averageRating": 4.2,
      "helpfulVotes": 150
    },
    "createdAt": "2024-01-01T00:00:00Z"
  }
}`}
        />

        <APIEndpoint
          method="PUT"
          endpoint="/api/users/[id]"
          description="Update user information"
          auth="Admin or Own Profile"
          parameters={[
            {
              name: "id",
              type: "string",
              required: true,
              description: "User ID",
            },
            {
              name: "name",
              type: "string",
              required: false,
              description: "User display name",
            },
            {
              name: "bio",
              type: "string",
              required: false,
              description: "User biography",
            },
            {
              name: "role",
              type: "string",
              required: false,
              description: "User role (admin only)",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "User updated successfully",
  "data": {
    "id": "user123",
    "name": "John Doe Updated",
    "bio": "Updated bio"
  }
}`}
        />

        <APIEndpoint
          method="DELETE"
          endpoint="/api/users/[id]"
          description="Deactivate or delete a user account"
          auth="Admin"
          parameters={[
            {
              name: "id",
              type: "string",
              required: true,
              description: "User ID",
            },
            {
              name: "permanent",
              type: "boolean",
              required: false,
              description: "Permanently delete (default: false)",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "User account deactivated"
}`}
        />
      </CollapsibleSection>

      <CollapsibleSection title="📦 Product Management Routes">
        <APIEndpoint
          method="GET"
          endpoint="/api/products"
          description="Get a list of products with optional filtering and search"
          parameters={[
            {
              name: "page",
              type: "number",
              required: false,
              description: "Page number",
            },
            {
              name: "limit",
              type: "number",
              required: false,
              description: "Items per page",
            },
            {
              name: "search",
              type: "string",
              required: false,
              description: "Search term",
            },
            {
              name: "category",
              type: "string",
              required: false,
              description: "Filter by category",
            },
            {
              name: "minRating",
              type: "number",
              required: false,
              description: "Minimum average rating",
            },
            {
              name: "sortBy",
              type: "string",
              required: false,
              description: "Sort by (rating, name, date)",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "products": [
      {
        "id": "prod123",
        "name": "Amazing Product",
        "description": "Product description",
        "category": "Electronics",
        "images": ["image1.jpg"],
        "averageRating": 4.5,
        "reviewCount": 120,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {...}
  }
}`}
        />

        <APIEndpoint
          method="POST"
          endpoint="/api/products"
          description="Create a new product"
          auth="Admin"
          parameters={[
            {
              name: "name",
              type: "string",
              required: true,
              description: "Product name",
            },
            {
              name: "description",
              type: "string",
              required: true,
              description: "Product description",
            },
            {
              name: "category",
              type: "string",
              required: true,
              description: "Product category",
            },
            {
              name: "images",
              type: "string[]",
              required: false,
              description: "Array of image URLs",
            },
            {
              name: "website",
              type: "string",
              required: false,
              description: "Product website",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "Product created successfully",
  "data": {
    "id": "prod456",
    "name": "New Product",
    "slug": "new-product"
  }
}`}
        />

        <APIEndpoint
          method="GET"
          endpoint="/api/products/[id]"
          description="Get detailed product information including reviews"
          parameters={[
            {
              name: "id",
              type: "string",
              required: true,
              description: "Product ID",
            },
            {
              name: "includeReviews",
              type: "boolean",
              required: false,
              description: "Include recent reviews",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "id": "prod123",
    "name": "Amazing Product",
    "description": "Detailed description",
    "category": "Electronics",
    "images": ["image1.jpg", "image2.jpg"],
    "website": "https://product-site.com",
    "stats": {
      "averageRating": 4.5,
      "reviewCount": 120,
      "ratingDistribution": {
        "5": 60,
        "4": 40,
        "3": 15,
        "2": 3,
        "1": 2
      }
    },
    "recentReviews": [...],
    "business": {...}
  }
}`}
        />
      </CollapsibleSection>

      <CollapsibleSection title="⭐ Review Management Routes">
        <APIEndpoint
          method="GET"
          endpoint="/api/reviews"
          description="Get reviews with filtering and pagination"
          parameters={[
            {
              name: "productId",
              type: "string",
              required: false,
              description: "Filter by product",
            },
            {
              name: "userId",
              type: "string",
              required: false,
              description: "Filter by user",
            },
            {
              name: "rating",
              type: "number",
              required: false,
              description: "Filter by rating",
            },
            {
              name: "sortBy",
              type: "string",
              required: false,
              description: "Sort by (date, rating, helpful)",
            },
            {
              name: "page",
              type: "number",
              required: false,
              description: "Page number",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "reviews": [
      {
        "id": "rev123",
        "rating": 5,
        "title": "Great product!",
        "content": "Really happy with this purchase...",
        "user": {
          "id": "user123",
          "name": "John Doe"
        },
        "product": {
          "id": "prod123",
          "name": "Amazing Product"
        },
        "votes": {
          "helpful": 15,
          "unhelpful": 2
        },
        "createdAt": "2024-01-15T00:00:00Z"
      }
    ],
    "pagination": {...}
  }
}`}
        />

        <APIEndpoint
          method="POST"
          endpoint="/api/reviews"
          description="Create a new review for a product"
          auth="User"
          parameters={[
            {
              name: "productId",
              type: "string",
              required: true,
              description: "Product being reviewed",
            },
            {
              name: "rating",
              type: "number",
              required: true,
              description: "Rating (1-5)",
            },
            {
              name: "title",
              type: "string",
              required: true,
              description: "Review title",
            },
            {
              name: "content",
              type: "string",
              required: true,
              description: "Review content",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "Review created successfully",
  "data": {
    "id": "rev456",
    "rating": 5,
    "title": "Great product!"
  }
}`}
        />

        <APIEndpoint
          method="POST"
          endpoint="/api/reviews/[id]/vote"
          description="Vote on review helpfulness"
          auth="User"
          parameters={[
            {
              name: "id",
              type: "string",
              required: true,
              description: "Review ID",
            },
            {
              name: "type",
              type: "string",
              required: true,
              description: "Vote type (helpful/unhelpful)",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "Vote recorded",
  "data": {
    "helpful": 16,
    "unhelpful": 2
  }
}`}
        />
      </CollapsibleSection>

      <CollapsibleSection title="🏢 Business & Claims Routes">
        <APIEndpoint
          method="GET"
          endpoint="/api/businesses"
          description="Get list of businesses"
          auth="Admin"
          parameters={[
            {
              name: "verified",
              type: "boolean",
              required: false,
              description: "Filter by verification status",
            },
            {
              name: "search",
              type: "string",
              required: false,
              description: "Search by business name",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "businesses": [
      {
        "id": "biz123",
        "name": "Acme Corp",
        "email": "<EMAIL>",
        "website": "https://acme.com",
        "verified": true,
        "claimedProducts": 5,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}`}
        />

        <APIEndpoint
          method="POST"
          endpoint="/api/claims"
          description="Submit a business claim for a product"
          auth="User"
          parameters={[
            {
              name: "productId",
              type: "string",
              required: true,
              description: "Product to claim",
            },
            {
              name: "businessName",
              type: "string",
              required: true,
              description: "Business name",
            },
            {
              name: "contactEmail",
              type: "string",
              required: true,
              description: "Business email",
            },
            {
              name: "website",
              type: "string",
              required: false,
              description: "Business website",
            },
            {
              name: "proofDocument",
              type: "string",
              required: false,
              description: "Proof document URL",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "Claim submitted successfully",
  "data": {
    "id": "claim789",
    "status": "pending",
    "submittedAt": "2024-01-20T00:00:00Z"
  }
}`}
        />

        <APIEndpoint
          method="PUT"
          endpoint="/api/claims/[id]"
          description="Update claim status (approve/reject)"
          auth="Admin"
          parameters={[
            {
              name: "id",
              type: "string",
              required: true,
              description: "Claim ID",
            },
            {
              name: "status",
              type: "string",
              required: true,
              description: "New status (approved/rejected)",
            },
            {
              name: "notes",
              type: "string",
              required: false,
              description: "Admin notes",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "Claim status updated",
  "data": {
    "status": "approved",
    "updatedAt": "2024-01-21T00:00:00Z"
  }
}`}
        />
      </CollapsibleSection>

      <CollapsibleSection title="📊 Analytics & Reports Routes">
        <APIEndpoint
          method="GET"
          endpoint="/api/site/metrics"
          description="Get public site metrics including total users, products, and reviews. No authentication required. Rate limited to 50 requests per minute."
          responseExample={`{
  "success": true,
  "data": {
    "totalUsers": 15420,
    "totalReviews": 45230,
    "totalProducts": 8340
  }
}`}
          errorCodes={[
            { code: 429, description: "Rate limit exceeded" },
            { code: 500, description: "Internal server error" },
          ]}
        />

        <APIEndpoint
          method="GET"
          endpoint="/api/analytics/overview"
          description="Get platform overview statistics"
          auth="Admin"
          responseExample={`{
  "success": true,
  "data": {
    "totalUsers": 15420,
    "totalProducts": 8340,
    "totalReviews": 45230,
    "averageRating": 4.2,
    "monthlyGrowth": {
      "users": 12.5,
      "products": 8.3,
      "reviews": 15.7
    }
  }
}`}
        />

        <APIEndpoint
          method="GET"
          endpoint="/api/analytics/top-reviewers"
          description="Get top reviewers based on activity"
          auth="Admin"
          parameters={[
            {
              name: "limit",
              type: "number",
              required: false,
              description: "Number of reviewers to return",
            },
            {
              name: "period",
              type: "string",
              required: false,
              description: "Time period (week/month/year)",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "topReviewers": [
      {
        "userId": "user123",
        "name": "John Doe",
        "reviewCount": 145,
        "averageRating": 4.3,
        "helpfulVotes": 890
      }
    ]
  }
}`}
        />
      </CollapsibleSection>

      <CollapsibleSection title="🐛 Bug Reports & Support Routes">
        <APIEndpoint
          method="GET"
          endpoint="/api/bug-reports"
          description="Get list of bug reports"
          auth="Admin"
          parameters={[
            {
              name: "status",
              type: "string",
              required: false,
              description: "Filter by status",
            },
            {
              name: "priority",
              type: "string",
              required: false,
              description: "Filter by priority",
            },
            {
              name: "assignee",
              type: "string",
              required: false,
              description: "Filter by assignee",
            },
          ]}
          responseExample={`{
  "success": true,
  "data": {
    "reports": [
      {
        "id": "bug123",
        "title": "Login page not loading",
        "description": "Unable to access login page...",
        "status": "open",
        "priority": "high",
        "reporter": {
          "id": "user123",
          "name": "John Doe"
        },
        "createdAt": "2024-01-20T00:00:00Z"
      }
    ]
  }
}`}
        />

        <APIEndpoint
          method="POST"
          endpoint="/api/bug-reports"
          description="Submit a new bug report"
          auth="User"
          parameters={[
            {
              name: "title",
              type: "string",
              required: true,
              description: "Bug title",
            },
            {
              name: "description",
              type: "string",
              required: true,
              description: "Detailed description",
            },
            {
              name: "steps",
              type: "string",
              required: false,
              description: "Steps to reproduce",
            },
            {
              name: "browser",
              type: "string",
              required: false,
              description: "Browser information",
            },
            {
              name: "screenshot",
              type: "string",
              required: false,
              description: "Screenshot URL",
            },
          ]}
          responseExample={`{
  "success": true,
  "message": "Bug report submitted",
  "data": {
    "id": "bug456",
    "status": "open",
    "createdAt": "2024-01-20T00:00:00Z"
  }
}`}
        />
      </CollapsibleSection>

      <InfoBox type="info" title="📝 API Usage Notes">
        <div className="space-y-3">
          <p>
            <strong>Rate Limiting:</strong> API endpoints are rate-limited to
            prevent abuse. Current limits are 100 requests per minute for
            authenticated users.
          </p>
          <p>
            <strong>Response Format:</strong> All API responses follow a
            consistent format with `success`, `data`, and optional `message`
            fields.
          </p>
          <p>
            <strong>Error Handling:</strong> Errors return appropriate HTTP
            status codes with descriptive error messages in the response body.
          </p>
          <p>
            <strong>Pagination:</strong> List endpoints support pagination with
            `page`, `limit`, and return total count information.
          </p>
        </div>
      </InfoBox>

      <BackToDocsButton />
    </DocsContainer>
  );
}
