import React from "react";

const CachingDocumentationPage = () => {
  return (
    <div id="top" className="max-w-4xl mx-auto">
      <div className="text-gray-700 leading-relaxed">
        <div className="mb-8 text-sm text-gray-500">
          <a href="/admin/docs" className="text-blue-600 hover:underline">
            Documentation
          </a>{" "}
          / <span>Caching Documentation</span>
        </div>

        <h1 className="text-4xl font-bold mb-6 text-gray-900">
          Caching System Documentation
          <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
            Complete
          </span>
        </h1>
        <p className="mb-4">
          The application implements a sophisticated Redis-based caching layer
          designed to enhance performance, reduce database load, and improve
          user experience. The system follows a cache-aside pattern with circuit
          breaker protection and comprehensive monitoring.
        </p>

        <h2
          id="architecture"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Architecture
        </h2>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Core Components
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Redis Service</strong> - Primary cache store with connection
            pooling and health monitoring
          </li>
          <li>
            <strong>Cache Module</strong> - Centralized cache system in <code className="bg-gray-100 px-2 py-1 rounded">src/app/lib/cache</code>
          </li>
          <li>
            <strong>Cache-Aside Pattern</strong> - Application explicitly
            manages cache operations
          </li>
          <li>
            <strong>Circuit Breaker</strong> - Prevents cascade failures when
            Redis is unavailable
          </li>
          <li>
            <strong>View Count System</strong> - Redis-based atomic counters
            with periodic database synchronization
          </li>
          <li>
            <strong>Performance Monitoring</strong> - Cache hit/miss tracking
            and performance metrics
          </li>
        </ul>
        
        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Cache Module Structure
        </h3>
        <p className="mb-4">
          The cache system is organized into a modular structure:
        </p>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>keys.ts</strong> - Cache key definitions and generation functions
          </li>
          <li>
            <strong>stats.ts</strong> - Performance tracking and circuit breaker logic
          </li>
          <li>
            <strong>operations.ts</strong> - Safe cache get/set operations
          </li>
          <li>
            <strong>invalidation.ts</strong> - Cache invalidation functions
          </li>
          <li>
            <strong>index.ts</strong> - Main exports and unified interface
          </li>
        </ul>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Cache Versioning
        </h3>
        <p className="mb-4">
          All cache keys use a versioning system (
          <code className="bg-gray-100 px-2 py-1 rounded">v2</code>) to handle
          schema changes gracefully. Key pattern:{" "}
          <code className="bg-gray-100 px-2 py-1 rounded">
            reviewit:v2:prefix:identifier
          </code>
        </p>

        <h2
          id="cache-keys-ttl"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Cache Keys and TTL Configuration
        </h2>
        <p className="mb-4">
          The system uses standardized cache keys with appropriate TTL values
          based on data volatility and access patterns.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Main Cache Functions
        </h3>
        <div className="overflow-x-auto mb-6">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Function
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cache Key Pattern
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  TTL
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getAllProductsFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:all_products
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  1 hour (3600s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  All active products with weighted ratings
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getProductDetailsFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:product_details:[productId]
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  15 minutes (900s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Detailed product information with reviews
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getProductSearchFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:product_search:[hash]
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  10 minutes (600s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Search results with pagination
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getProductsWithReviewsFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:products_with_reviews
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  30 minutes (1800s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Products with review counts and ratings
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getReviewsFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:reviews:[productId]
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  15 minutes (900s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Product reviews with pagination
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getRecentReviewsFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:recent_reviews
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  5 minutes (300s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Latest reviews across all products
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getPromotionsFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:promotions
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  1 hour (3600s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Active promotions and offers
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getCommentsByReviewIdFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:comments:[reviewId]
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  10 minutes (600s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Comments for specific review
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getUsersFromCache
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:users
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  30 minutes (1800s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  User directory with profiles
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-900">
                  getCacheStats
                </td>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:cache_stats
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  1 minute (60s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Cache performance metrics
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2
          id="view-count-system"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          View Count Management System
        </h2>
        <p className="mb-4">
          The application implements a sophisticated Redis-based view counting
          system that provides real-time tracking with database synchronization.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Architecture
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Redis Counters</strong> - Atomic INCR operations for
            real-time view tracking
          </li>
          <li>
            <strong>Database Sync</strong> - Periodic synchronization with
            PostgreSQL
          </li>
          <li>
            <strong>TTL Management</strong> - 1-hour expiration for view count
            keys
          </li>
          <li>
            <strong>Batch Operations</strong> - Efficient bulk updates during
            sync
          </li>
        </ul>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Key Functions
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>incrementViewCount</strong> - Increments Redis counter for
            product views
          </li>
          <li>
            <strong>getViewCount</strong> - Retrieves current view count from
            Redis
          </li>
          <li>
            <strong>syncViewCountsToDatabase</strong> - Batch synchronizes Redis
            counts to database
          </li>
          <li>
            <strong>resetViewCounts</strong> - Clears Redis counters after sync
          </li>
        </ul>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          View Count Cache Keys
        </h3>
        <div className="overflow-x-auto mb-6">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Key Pattern
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  TTL
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Purpose
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  reviewit:v2:view_count:[productId]
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">
                  1 hour (3600s)
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Individual product view counter
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2
          id="circuit-breaker"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Circuit Breaker Implementation
        </h2>
        <p className="mb-4">
          The caching system includes a circuit breaker pattern to handle Redis
          failures gracefully and prevent cascade failures.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Configuration
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Failure Threshold</strong> - 5 consecutive failures trigger
            circuit opening
          </li>
          <li>
            <strong>Timeout Period</strong> - 30 seconds before attempting to
            close circuit
          </li>
          <li>
            <strong>State Management</strong> - CLOSED, OPEN, HALF_OPEN states
          </li>
          <li>
            <strong>Fallback Behavior</strong> - Direct database queries when
            circuit is open
          </li>
        </ul>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Circuit States
        </h3>
        <div className="overflow-x-auto mb-6">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  State
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Behavior
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Transition Condition
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-4 text-sm font-bold text-green-600">
                  CLOSED
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Normal operation, cache requests proceed
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Opens after 5 consecutive failures
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-bold text-red-600">
                  OPEN
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Cache bypassed, direct database access
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Half-opens after 30 seconds
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-bold text-yellow-600">
                  HALF_OPEN
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Test single request to cache
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Closes on success, opens on failure
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2
          id="performance-monitoring"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Performance Monitoring
        </h2>
        <p className="mb-4">
          The caching system includes comprehensive monitoring and statistics
          collection to track performance and identify optimization
          opportunities.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Cache Statistics
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Hit Rate</strong> - Percentage of cache hits vs total
            requests
          </li>
          <li>
            <strong>Miss Rate</strong> - Percentage of cache misses vs total
            requests
          </li>
          <li>
            <strong>Error Rate</strong> - Percentage of cache errors vs total
            requests
          </li>
          <li>
            <strong>Response Times</strong> - Average cache operation latency
          </li>
          <li>
            <strong>Memory Usage</strong> - Redis memory consumption and key
            counts
          </li>
        </ul>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Monitoring Endpoints
        </h3>
        <div className="overflow-x-auto mb-6">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Endpoint
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Purpose
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Response Format
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  /api/cache/stats
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Retrieve cache performance metrics
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  JSON with hit/miss rates and counts
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  /api/cache/health
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Redis connection health check
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  JSON with status and response time
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  /api/cache/test
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Test cache operations
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  JSON with test results
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2
          id="cache-invalidation"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Cache Invalidation
        </h2>
        <p className="mb-4">
          The system implements strategic cache invalidation to ensure data
          consistency while maintaining performance.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Invalidation Strategies
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Time-Based Expiration</strong> - Automatic TTL-based
            invalidation
          </li>
          <li>
            <strong>Event-Driven Invalidation</strong> - Invalidate on data
            mutations
          </li>
          <li>
            <strong>Pattern-Based Invalidation</strong> - Clear related cache
            entries
          </li>
          <li>
            <strong>Manual Invalidation</strong> - Admin controls for cache
            management
          </li>
        </ul>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Invalidation Functions
        </h3>
        <p className="mb-4">
          All cache invalidation functions are now centralized in <code className="bg-gray-100 px-2 py-1 rounded">src/app/lib/cache</code> module:
        </p>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>invalidateAllProductsCache</strong> - Clears all
            product-related cache entries
          </li>
          <li>
            <strong>invalidateProductCache</strong> - Clears specific product
            cache
          </li>
          <li>
            <strong>invalidateReviewCaches</strong> - Clears review-related
            cache entries
          </li>
          <li>
            <strong>invalidateSearchCache</strong> - Clears search result cache
          </li>
          <li>
            <strong>invalidateCommentCache</strong> - Clears comment cache for a specific review
          </li>
          <li>
            <strong>invalidateBusinessCaches</strong> - Clears business-related cache entries
          </li>
          <li>
            <strong>invalidateCachesOnOwnershipChange</strong> - Comprehensive invalidation when product ownership changes
          </li>
          <li>
            <strong>batchInvalidateCache</strong> - Efficiently invalidates multiple cache keys with retry logic
          </li>
        </ul>
        
        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Product Claim Invalidation
        </h3>
        <p className="mb-4">
          When a product claim is approved, the system performs comprehensive cache invalidation:
        </p>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Product-specific caches</strong> - Details and reviews for the claimed product
          </li>
          <li>
            <strong>All products cache</strong> - Since ownership status affects product listings
          </li>
          <li>
            <strong>Search cache</strong> - Since ownership affects search results
          </li>
          <li>
            <strong>Business caches</strong> - If a business was created or updated
          </li>
          <li>
            <strong>Admin caches</strong> - Dashboard metrics and reports
          </li>
        </ul>

        <h2
          id="configuration"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Configuration
        </h2>
        <p className="mb-4">
          The caching system is configured through environment variables and can
          be customized for different deployment environments.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Environment Variables
        </h3>
        <div className="overflow-x-auto mb-6">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Variable
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Default
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  REDIS_URL
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  redis://localhost:6379
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Redis connection string
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  CACHE_ENABLED
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">true</td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Enable/disable caching globally
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  CACHE_PREFIX
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">reviewit:v2</td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Cache key prefix
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  CIRCUIT_BREAKER_THRESHOLD
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">5</td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Failure threshold for circuit breaker
                </td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-mono text-gray-600">
                  CIRCUIT_BREAKER_TIMEOUT
                </td>
                <td className="px-4 py-4 text-sm text-gray-600">30000</td>
                <td className="px-4 py-4 text-sm text-gray-600">
                  Circuit breaker timeout (ms)
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Cache Configuration
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Connection Pooling</strong> - Redis connection pool size and
            timeout settings
          </li>
          <li>
            <strong>Retry Logic</strong> - Automatic retry for failed cache
            operations
          </li>
          <li>
            <strong>Compression</strong> - Optional compression for large cache
            values
          </li>
          <li>
            <strong>Serialization</strong> - JSON serialization for complex
            objects
          </li>
        </ul>

        <h2
          id="best-practices"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Best Practices
        </h2>
        <p className="mb-4">
          Follow these guidelines to ensure optimal cache performance and
          reliability.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Key Design Principles
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Appropriate TTL Values</strong> - Set TTL based on data
            volatility and access patterns
          </li>
          <li>
            <strong>Consistent Key Naming</strong> - Use standardized key
            patterns for easy management
          </li>
          <li>
            <strong>Error Handling</strong> - Always provide fallback mechanisms
            for cache failures
          </li>
          <li>
            <strong>Monitoring</strong> - Regularly monitor cache hit rates and
            performance
          </li>
          <li>
            <strong>Graceful Degradation</strong> - Ensure application works
            without cache
          </li>
        </ul>
        
        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Using the Cache System
        </h3>
        <p className="mb-4">
          When working with the cache system, follow these guidelines:
        </p>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Import from the correct location</strong> - Use <code className="bg-gray-100 px-2 py-1 rounded">import {'{'} ... {'}'} from '@/app/lib/cache'</code>
          </li>
          <li>
            <strong>Use specific invalidation</strong> - Invalidate only what's necessary, not everything
          </li>
          <li>
            <strong>Batch operations</strong> - Use <code className="bg-gray-100 px-2 py-1 rounded">batchInvalidateCache()</code> for multiple keys
          </li>
          <li>
            <strong>Safe operations</strong> - Use <code className="bg-gray-100 px-2 py-1 rounded">safeGetFromCache()</code> and <code className="bg-gray-100 px-2 py-1 rounded">safeSetToCache()</code> with circuit breaker protection
          </li>
          <li>
            <strong>Comprehensive invalidation</strong> - For ownership changes, use <code className="bg-gray-100 px-2 py-1 rounded">invalidateCachesOnOwnershipChange()</code>
          </li>
        </ul>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Performance Optimization
        </h3>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Batch Operations</strong> - Use batch operations for
            multiple cache updates
          </li>
          <li>
            <strong>Compression</strong> - Compress large cache values to reduce
            memory usage
          </li>
          <li>
            <strong>Connection Pooling</strong> - Use connection pooling to
            reduce connection overhead
          </li>
          <li>
            <strong>Async Operations</strong> - Use asynchronous operations
            where possible
          </li>
          <li>
            <strong>Cache Warming</strong> - Pre-populate cache with frequently
            accessed data
          </li>
        </ul>

        <h2
          id="recent-changes"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Recent Changes
        </h2>
        <p className="mb-4">
          The caching system was recently refactored to improve architecture and maintainability:
        </p>
        <ul className="mb-4 pl-6 space-y-2">
          <li>
            <strong>Moved from Analytics</strong> - Cache system moved from <code className="bg-gray-100 px-2 py-1 rounded">src/app/util/analytics/cache.ts</code> to <code className="bg-gray-100 px-2 py-1 rounded">src/app/lib/cache/</code>
          </li>
          <li>
            <strong>Modular Structure</strong> - Split into keys, stats, operations, and invalidation modules
          </li>
          <li>
            <strong>Enhanced Product Claims</strong> - Improved cache invalidation for product ownership changes
          </li>
          <li>
            <strong>Better Organization</strong> - Centralized cache key definitions and invalidation logic
          </li>
          <li>
            <strong>Backward Compatibility</strong> - Old import paths still work but redirect to the new module
          </li>
        </ul>
        <p className="mb-4">
          For more details, see <code className="bg-gray-100 px-2 py-1 rounded">docs/cache-system-refactoring.md</code>
        </p>

        <h2
          id="troubleshooting"
          className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900"
        >
          Troubleshooting
        </h2>
        <p className="mb-4">
          Common issues and solutions for the caching system.
        </p>

        <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">
          Common Issues
        </h3>
        <div className="space-y-4 mb-6">
          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-2">
              Redis Connection Failures
            </h4>
            <p className="text-sm text-gray-600 mb-2">
              <strong>Symptoms:</strong> Cache operations fail, circuit breaker
              opens frequently
            </p>
            <p className="text-sm text-gray-600 mb-2">
              <strong>Solutions:</strong>
            </p>
            <ul className="text-sm text-gray-600 pl-4 space-y-1">
              <li>• Check Redis server status and connectivity</li>
              <li>• Verify REDIS_URL environment variable</li>
              <li>• Review Redis server logs for errors</li>
              <li>• Check network connectivity and firewall settings</li>
            </ul>
          </div>

          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-2">
              Low Cache Hit Rate
            </h4>
            <p className="text-sm text-gray-600 mb-2">
              <strong>Symptoms:</strong> High database load, slow response times
            </p>
            <p className="text-sm text-gray-600 mb-2">
              <strong>Solutions:</strong>
            </p>
            <ul className="text-sm text-gray-600 pl-4 space-y-1">
              <li>• Review TTL values and adjust as needed</li>
              <li>• Identify frequently accessed data for caching</li>
              <li>• Implement cache warming strategies</li>
              <li>• Optimize cache key patterns</li>
            </ul>
          </div>

          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-2">
              Memory Usage Issues
            </h4>
            <p className="text-sm text-gray-600 mb-2">
              <strong>Symptoms:</strong> Redis memory warnings, eviction of
              cache keys
            </p>
            <p className="text-sm text-gray-600 mb-2">
              <strong>Solutions:</strong>
            </p>
            <ul className="text-sm text-gray-600 pl-4 space-y-1">
              <li>• Monitor memory usage with Redis INFO command</li>
              <li>• Implement compression for large values</li>
              <li>• Review and optimize TTL values</li>
              <li>• Consider Redis memory optimization settings</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            💡 Additional Resources
          </h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>
              •{" "}
              <a
                href="/api/cache/stats"
                className="underline hover:text-blue-600"
              >
                Cache Statistics API
              </a>
            </li>
            <li>
              •{" "}
              <a
                href="/api/cache/health"
                className="underline hover:text-blue-600"
              >
                Cache Health Check
              </a>
            </li>
            <li>
              •{" "}
              <a
                href="/admin/docs/api-routes"
                className="underline hover:text-blue-600"
              >
                API Routes Documentation
              </a>
            </li>
            <li>
              •{" "}
              <a
                href="/admin/docs/core-concepts"
                className="underline hover:text-blue-600"
              >
                Core Concepts
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CachingDocumentationPage;
