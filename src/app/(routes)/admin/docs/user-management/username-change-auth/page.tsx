import React from 'react';

const UsernameChangeAuthPage = () => {
    return (
        <div id="top" className="max-w-4xl mx-auto">
            <div className="text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / 
                    <a href="/admin/docs/user-management" className="text-blue-600 hover:underline"> User Management</a> / 
                    <span> Username Change Authentication</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    Username Change Authentication
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                </h1>
                <p className="mb-4">This documentation explains the authentication flow for username changes, including special handling for restricted usernames and admin users.</p>

                <h2 id="overview" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">System Overview</h2>
                <p className="mb-4">The username change authentication system handles several key scenarios:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Regular Username Changes</strong> - Standard flow with rate limiting and validation</li>
                    <li><strong>Restricted Username Changes</strong> - Special handling for users with <code>usernameNeedsChange</code> flag</li>
                    <li><strong>Admin Username Privileges</strong> - Special handling for admin users</li>
                    <li><strong>Clerk ID Synchronization</strong> - Ensuring consistency between Clerk and database IDs</li>
                </ul>

                <h2 id="authentication-flow" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Authentication Flow</h2>
                <p className="mb-4">The username change authentication flow follows these steps:</p>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">1. Authentication Verification</h3>
                <p className="mb-4">When a user attempts to change their username via the <code>/api/update/user/[userId]</code> endpoint:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li>The API verifies the Clerk authentication token</li>
                    <li>The API retrieves the authenticated Clerk user ID</li>
                    <li>The API looks up the target user in the database using either the database ID or Clerk ID</li>
                    <li>The API verifies that the authenticated user is the owner of the account</li>
                </ul>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm overflow-x-auto">
                    <pre>{`// Authentication verification
const { userId: clerkUserId } = await auth();
if (!clerkUserId) {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}

// User lookup with flexible ID matching
const targetUser = await prisma.user.findFirst({
  where: {
    OR: [
      { id: userId },
      { clerkUserId: userId },
    ],
  },
  select: { id: true, clerkUserId: true },
});`}</pre>
                </div>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">2. Special Handling for Restricted Usernames</h3>
                <p className="mb-4">Users with the <code>usernameNeedsChange</code> flag set to <code>true</code> are allowed to bypass normal authentication checks:</p>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm overflow-x-auto">
                    <pre>{`// Special case for username changes
if (updatedFields.userName) {
  // First try to find the user by ID
  const userById = await prisma.user.findUnique({
    where: { id: userId },
    select: { usernameNeedsChange: true, id: true, clerkUserId: true },
  });
  
  // If found and has usernameNeedsChange flag, allow the update
  if (userById?.usernameNeedsChange === true) {
    console.log("User has usernameNeedsChange flag set, allowing update regardless of Clerk ID");
    
    // Allow this user to update their username regardless of Clerk ID mismatch
    targetUser = userById;
    effectiveUserId = targetUser.id;
    bypassNormalAuth = true;
  }
}`}</pre>
                </div>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">3. Rate Limiting Exceptions</h3>
                <p className="mb-4">Users with the <code>usernameNeedsChange</code> flag are also exempted from rate limiting:</p>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm overflow-x-auto">
                    <pre>{`// Skip rate limit check if username needs to be changed
if (!user.usernameNeedsChange) {
  // Check if user has changed username recently
  const recentNameChange = await prisma.usernameChange.findFirst({
    where: {
      userId: effectiveUserId,
      createdAt: {
        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days
      }
    }
  });

  if (recentNameChange) {
    return NextResponse.json(
      { error: "Username can only be changed once every 30 days" },
      { status: 429 }
    );
  }
}`}</pre>
                </div>

                <h2 id="webhook-processing" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Webhook Processing</h2>
                <p className="mb-4">The Clerk webhook handler processes user creation and updates:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">1. Username Validation</h3>
                <p className="mb-4">When a user signs up, their username is validated against restrictions:</p>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm overflow-x-auto">
                    <pre>{`// Determine user role based on email
const userRole = isAdmin(userEmail) ? 'ADMIN' : 'USER';

// Admins can bypass username restrictions entirely
if (userRole !== 'ADMIN') {
  const validation = validateUsername(requestedUsername);
  if (!validation.isValid) {
    const idSuffix = payload.data.id.replace(/^user_/, "").slice(-8);
    finalUsername = \`user_\${idSuffix}\`;
    usernameNeedsChange = true;
  } else {
    // If the chosen username matches our auto-generated fallback pattern,
    // keep prompting the user to change it.
    const autoGenerated = /^user_[A-Za-z0-9]{8}$/.test(requestedUsername);
    usernameNeedsChange = autoGenerated;
  }
}
// If admin, username is accepted as-is`}</pre>
                </div>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">2. Database ID Synchronization</h3>
                <p className="mb-4">The webhook ensures that the database ID matches the Clerk ID:</p>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm overflow-x-auto">
                    <pre>{`const user = await prisma.user.upsert({
  where: { email: userEmail },
  update: {
    avatar: payload.data.image_url,
    lastName: payload.data.last_name,
    firstName: payload.data.first_name,
    userName: finalUsername,
    role: userRole, // Update role in case email was added to admin list
    usernameNeedsChange: usernameNeedsChange,
  },
  create: {
    id: payload.data.id, // Set database ID to Clerk ID
    userName: finalUsername,
    avatar: payload.data.image_url,
    email: userEmail,
    firstName: payload.data.first_name,
    lastName: payload.data.last_name,
    createdDate: new Date(),
    clerkUserId: payload.data.id,
    role: userRole, // Set role based on admin email check
    usernameNeedsChange: usernameNeedsChange,
  },
});`}</pre>
                </div>

                <h2 id="admin-privileges" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Admin Privileges</h2>
                <p className="mb-4">Admin users receive special privileges in the username system:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Username Restrictions Bypass</strong> - Admins can use any username, including reserved ones</li>
                    <li><strong>Automatic Role Assignment</strong> - Users with emails in the admin list automatically get the ADMIN role</li>
                    <li><strong>Role Persistence</strong> - Admin role is preserved during username changes</li>
                </ul>
                <p className="mb-4">Admin emails are configured in <code>src/app/config/admin.ts</code>.</p>

                <h2 id="troubleshooting" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Troubleshooting</h2>
                <p className="mb-4">Common issues and their solutions:</p>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">401 Unauthorized Errors</h3>
                <p className="mb-4">If users receive 401 errors when changing usernames:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li>Check if the user's <code>clerkUserId</code> in the database matches their current Clerk ID</li>
                    <li>Verify that users with <code>usernameNeedsChange=true</code> can bypass authentication checks</li>
                    <li>Ensure the client is sending the correct Authorization header with the Clerk token</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Rate Limiting Issues</h3>
                <p className="mb-4">If users are unexpectedly rate-limited:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li>Confirm that <code>usernameNeedsChange</code> flag is properly set in the database</li>
                    <li>Verify that the rate limit bypass is working for flagged users</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Clerk ID Mismatches</h3>
                <p className="mb-4">If Clerk IDs are inconsistent:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li>This can happen when Clerk assigns a new ID during the signup process</li>
                    <li>The system is designed to handle this by allowing updates for users with <code>usernameNeedsChange=true</code></li>
                    <li>Ensure the webhook is correctly setting <code>id: payload.data.id</code> in the user creation</li>
                </ul>

                <div className="mt-12 pt-6 border-t border-gray-200">
                    <p className="text-sm text-gray-500">Last updated: July 24, 2025</p>
                </div>
            </div>
        </div>
    );
};

export default UsernameChangeAuthPage;
