import React from 'react';

const UserManagementPage = () => {
    return (
        <div id="top" className="max-w-4xl mx-auto">
            <div className="text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / <span>User Management</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    User Management
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                </h1>
                <p className="mb-4">This comprehensive guide explains how to effectively manage users in the admin dashboard, including basic operations, advanced features, and analytics.</p>

                <h2 id="viewing-users" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Viewing Users</h2>
                <p className="mb-4">To see a list of all users, navigate to the "Users" tab in the admin dashboard. You will see a comprehensive table with all registered users, displaying their essential details:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Email address</strong> - Primary identifier and contact method</li>
                    <li><strong>Full name</strong> - User's display name</li>
                    <li><strong>Role</strong> - Current permission level (Admin, Moderator, User)</li>
                    <li><strong>Registration date</strong> - When the user joined the platform</li>
                    <li><strong>Last activity</strong> - Most recent login or action</li>
                    <li><strong>Status</strong> - Active, suspended, or pending verification</li>
                </ul>

                <h2 id="related-guides" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Related Guides</h2>
                <p className="mb-4">Explore these additional resources for more specific user management tasks:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><a href="/admin/docs/user-management/test-user-status" className="text-blue-600 hover:underline"><strong>Test User Status</strong></a> - How to manage test accounts and their lifecycle</li>
                    <li><a href="/admin/docs/user-management/username-change-auth" className="text-blue-600 hover:underline"><strong>Username Change Authentication</strong></a> - Authentication flow for username changes and special cases</li>
                    <li><a href="/admin/docs/user-status-enforcement" className="text-blue-600 hover:underline"><strong>User Status Enforcement</strong></a> - Managing user account status and restrictions</li>
                    <li><a href="/admin/docs/username-restrictions" className="text-blue-600 hover:underline"><strong>Username Restrictions</strong></a> - Configuration and management of restricted usernames</li>
                </ul>

                <h2 id="editing-users" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Editing Users</h2>
                <p className="mb-4">To edit a user's information, click on the "Edit" button next to the user you want to modify. The edit form allows you to update:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Personal Information</strong> - Name, email, and profile details</li>
                    <li><strong>Role Assignment</strong> - Change user permissions and access levels</li>
                    <li><strong>Account Status</strong> - Activate, suspend, or mark for verification</li>
                    <li><strong>Preferences</strong> - Notification settings and privacy controls</li>
                </ul>
                <p className="mb-4">Once you are done making changes, click "Save" to apply the updates. The system will log all modifications for audit purposes.</p>

                <h2 id="deleting-users" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Deleting Users</h2>
                <p className="mb-4">To delete a user, click on the "Delete" button next to the user you want to remove. A confirmation dialog will appear with important warnings:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Data Impact</strong> - Reviews and content associated with the user</li>
                    <li><strong>Irreversible Action</strong> - Deletion cannot be undone</li>
                    <li><strong>Alternative Options</strong> - Consider suspension instead of deletion</li>
                </ul>
                <p className="mb-4">Click "Confirm" to permanently delete the user from the system. This action will be logged in the audit trail.</p>

                <h2 id="search-filtering" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">User Search and Filtering</h2>
                <p className="mb-4">The user management dashboard includes powerful search and filtering capabilities:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Search Features</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Text Search</strong> - Find users by name, email, or username</li>
                    <li><strong>Fuzzy Matching</strong> - Handles typos and partial matches</li>
                    <li><strong>Advanced Filters</strong> - Combine multiple criteria for precise results</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Filter Options</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Role Filter</strong> - Admin, Moderator, User, or custom roles</li>
                    <li><strong>Status Filter</strong> - Active, suspended, pending, or banned</li>
                    <li><strong>Date Range</strong> - Registration date or last activity</li>
                    <li><strong>Activity Level</strong> - High, medium, low, or inactive users</li>
                </ul>

                <h2 id="user-analytics" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">User Analytics</h2>
                <p className="mb-4">The user analytics section provides comprehensive insights into user engagement and platform health:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Key Metrics</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Active Users</strong> - Daily, weekly, and monthly active user counts</li>
                    <li><strong>New Registrations</strong> - Growth trends and registration sources</li>
                    <li><strong>User Retention</strong> - Cohort analysis and retention rates</li>
                    <li><strong>Engagement Metrics</strong> - Reviews written, products viewed, time spent</li>
                    <li><strong>Geographic Distribution</strong> - User locations and regional trends</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Analytics Dashboard</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Real-time Data</strong> - Live user activity and statistics</li>
                    <li><strong>Historical Trends</strong> - Long-term growth and usage patterns</li>
                    <li><strong>Comparative Analysis</strong> - Period-over-period comparisons</li>
                    <li><strong>Export Capabilities</strong> - Download reports for external analysis</li>
                </ul>

                <h2 id="role-based-access" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Role-Based Access Control</h2>
                <p className="mb-4">The system uses a sophisticated role-based access control (RBAC) system to manage user permissions:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Default Roles</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Super Admin</strong> - Full system access and user management</li>
                    <li><strong>Admin</strong> - Platform management and content moderation</li>
                    <li><strong>Moderator</strong> - Content review and user support</li>
                    <li><strong>Business Owner</strong> - Business profile and product management</li>
                    <li><strong>User</strong> - Standard platform features and review submission</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Permission Management</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Granular Permissions</strong> - Fine-tuned access control</li>
                    <li><strong>Custom Roles</strong> - Create specialized roles for specific needs</li>
                    <li><strong>Temporary Permissions</strong> - Grant time-limited access</li>
                    <li><strong>Permission Inheritance</strong> - Role hierarchy and permission cascading</li>
                </ul>

                <h2 id="activity-tracking" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">User Activity Tracking</h2>
                <p className="mb-4">The system maintains comprehensive activity logs for security and audit purposes:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Tracked Activities</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Authentication Events</strong> - Login, logout, and failed attempts</li>
                    <li><strong>Content Actions</strong> - Creating, editing, or deleting reviews and products</li>
                    <li><strong>Profile Changes</strong> - Updates to user information and settings</li>
                    <li><strong>Administrative Actions</strong> - Role changes and account modifications</li>
                    <li><strong>Security Events</strong> - Suspicious activities and security violations</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Audit Trail Features</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Detailed Logs</strong> - Timestamp, IP address, and action details</li>
                    <li><strong>Search and Filter</strong> - Find specific activities quickly</li>
                    <li><strong>Export Options</strong> - Download logs for compliance reporting</li>
                    <li><strong>Retention Policy</strong> - Configurable log retention periods</li>
                </ul>

                <h2 id="bulk-operations" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Bulk User Operations</h2>
                <p className="mb-4">The user management dashboard supports efficient bulk operations for managing multiple users simultaneously:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Available Bulk Actions</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Bulk Delete</strong> - Remove multiple users in a single operation</li>
                    <li><strong>Role Assignment</strong> - Change roles for multiple users at once</li>
                    <li><strong>Status Updates</strong> - Activate, suspend, or verify multiple accounts</li>
                    <li><strong>Email Notifications</strong> - Send messages to selected user groups</li>
                    <li><strong>Export Data</strong> - Download user information for selected accounts</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Safety Features</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Confirmation Dialogs</strong> - Prevent accidental bulk operations</li>
                    <li><strong>Preview Mode</strong> - Review changes before applying them</li>
                    <li><strong>Rollback Options</strong> - Undo certain bulk operations if needed</li>
                    <li><strong>Progress Tracking</strong> - Monitor bulk operation status and completion</li>
                </ul>
            </div>

            <a
                href="#top"
                className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
            >
                ↑
            </a>
        </div>
    );
};

export default UserManagementPage;