import React from 'react';

const UsernameRestrictionsPage = () => {
    return (
        <div id="top" className="max-w-4xl mx-auto">
            <div className="text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / <span>Username Restrictions</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    Username Restrictions
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                </h1>
                <p className="mb-4">This guide explains the username restriction system that prevents users from selecting reserved, inappropriate, or system-critical usernames during registration and profile updates.</p>

                <h2 id="overview" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">System Overview</h2>
                <p className="mb-4">The username restriction system is a configuration-driven service that validates usernames against predefined lists of restricted terms. It operates at multiple levels:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Registration Flow</strong> - Prevents restricted usernames during user signup</li>
                    <li><strong>Profile Updates</strong> - Validates username changes in user profiles</li>
                    <li><strong>Real-time Validation</strong> - Provides immediate feedback in forms</li>
                    <li><strong>API Endpoints</strong> - Offers programmatic access for validation</li>
                </ul>

                <h2 id="configuration" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Configuration Management</h2>
                <p className="mb-4">Username restrictions are managed through a JSON configuration file located at:</p>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm">
                    <code>src/app/config/restrictedUsernames.json</code>
                </div>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Configuration Structure</h3>
                <p className="mb-4">The configuration file uses a categorized structure for better organization:</p>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm overflow-x-auto">
                    <pre>{`{
  "version": "1.0",
  "lastUpdated": "2025-01-21",
  "categories": {
    "system": {
      "description": "System and platform reserved names",
      "usernames": ["admin", "administrator", "root", "system", "api"]
    },
    "brand": {
      "description": "Brand and product related names", 
      "usernames": ["reviewit", "review-it", "support", "help"]
    },
    "common": {
      "description": "Common reserved terms",
      "usernames": ["user", "guest", "anonymous", "test"]
    },
    "inappropriate": {
      "description": "Inappropriate or offensive terms",
      "usernames": ["spam", "fake", "bot", "abuse"]
    },
    "security": {
      "description": "Security-related reserved terms",
      "usernames": ["auth", "login", "password", "token"]
    }
  }
}`}</pre>
                </div>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Current Categories</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>System</strong> - Platform infrastructure and system-level usernames</li>
                    <li><strong>Brand</strong> - ReviewIt brand names and official support channels</li>
                    <li><strong>Common</strong> - Generic reserved terms and placeholder names</li>
                    <li><strong>Inappropriate</strong> - Terms that could be used maliciously</li>
                    <li><strong>Security</strong> - Authentication and security-related terms</li>
                </ul>

                <h2 id="adding-restrictions" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Managing Restrictions</h2>
                <p className="mb-4">There are two ways to manage restricted usernames: through the admin UI (recommended) or by manually editing the configuration file.</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Method 1: Admin UI (Recommended)</h3>
                <p className="mb-4">The easiest way to manage restricted usernames is through the admin interface:</p>
                <ol className="mb-4 pl-6 space-y-2 list-decimal">
                    <li>Navigate to <strong>Admin Panel</strong> → <strong>Manage Restricted Usernames</strong></li>
                    <li>Use the <strong>View Usernames</strong> tab to see all current restrictions</li>
                    <li>Use the <strong>Add Username</strong> tab to add new restrictions to existing categories</li>
                    <li>Use the <strong>Manage Categories</strong> tab to create new categories or view existing ones</li>
                    <li>Changes are applied immediately without requiring a server restart</li>
                </ol>

                <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                    <div className="flex">
                        <div className="ml-3">
                            <p className="text-sm text-blue-700">
                                <strong>💡 Pro Tip:</strong> The admin UI automatically handles validation, normalization, and configuration reloading. It's the safest way to manage restrictions.
                            </p>
                        </div>
                    </div>
                </div>

                <h4 className="text-xl font-bold mt-6 mb-2 text-gray-900">Admin UI Features</h4>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Real-time View</strong> - See all restricted usernames with category filtering</li>
                    <li><strong>Bulk Management</strong> - Add/remove usernames efficiently</li>
                    <li><strong>Category Management</strong> - Create new categories with descriptions</li>
                    <li><strong>Validation</strong> - Prevents duplicate entries and invalid data</li>
                    <li><strong>Auto-reload</strong> - Changes take effect immediately</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Method 2: Manual File Editing</h3>
                <p className="mb-4">For advanced users or bulk operations, you can manually edit the configuration file:</p>
                <ol className="mb-4 pl-6 space-y-2 list-decimal">
                    <li>Open <code className="bg-gray-100 px-2 py-1 rounded">src/app/config/restrictedUsernames.json</code></li>
                    <li>Choose the appropriate category or create a new one</li>
                    <li>Add the username to the usernames array</li>
                    <li>Update the lastUpdated field with the current date</li>
                    <li>Save the file and restart the server or use the reload API</li>
                </ol>

                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                    <div className="flex">
                        <div className="ml-3">
                            <p className="text-sm text-yellow-700">
                                <strong>⚠️ Warning:</strong> Manual editing requires careful attention to JSON syntax and proper validation. Use the admin UI when possible.
                            </p>
                        </div>
                    </div>
                </div>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Example Addition</h3>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm">
                    <pre>{`// Adding a new restricted username to the brand category
"brand": {
  "description": "Brand and product related names",
  "usernames": [
    "reviewit",
    "review-it", 
    "support",
    "help",
    "newrestriction"  // <- Add here
  ]
}`}</pre>
                </div>

                <h2 id="validation-behavior" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Validation Behavior</h2>

                {/* --- NEW SECTION: Auto-fallback & Username Change Flow --- */}
                <h3 id="autofallback" className="text-2xl font-bold mt-8 mb-3 text-gray-900">Auto-Fallback &amp; Username Change Flow <span className="inline-block ml-1 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded">since 2025-07-23</span></h3>
                <p className="mb-4">
                    If a restricted username slips through (e.g.&nbsp;via Clerk&rsquo;s own signup UI), the <code>user.created</code> and <code>user.updated</code> webhooks automatically:
                </p>
                <ol className="mb-4 pl-6 space-y-2 list-decimal">
                    <li>Generate a safe temporary handle: <code>user_&lt;8&nbsp;chars&gt;</code></li>
                    <li>Persist it to Clerk and the database</li>
                    <li>Set <code>publicMetadata.usernameNeedsChange&nbsp;=&nbsp;true</code></li>
                </ol>
                <p className="mb-4">The front-end banner (<code>&lt;UsernameChangePrompt/&gt;</code>) watches this flag and prompts the user to pick a real, unrestricted handle via the profile page. On success, the profile-update API clears the flag so the banner disappears.</p>

                <h4 className="text-xl font-bold mt-6 mb-2 text-gray-900">Why this matters</h4>
                <ul className="mb-4 pl-6 space-y-2 list-disc">
                    <li>Signup is friction-free — no hard fail on reserved names.</li>
                    <li>Security is preserved — reserved names never become permanent.</li>
                    <li>Users remain aware — the banner stays until they choose a valid handle.</li>
                </ul>
                <p className="mb-4">The username restriction system uses intelligent validation logic:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Normalization Rules</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Case Insensitive</strong> - "Admin", "ADMIN", and "admin" are all restricted</li>
                    <li><strong>Separator Removal</strong> - Hyphens, underscores, and dots are ignored</li>
                    <li><strong>Whitespace Trimming</strong> - Leading and trailing spaces are removed</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Validation Examples</h3>
                <div className="bg-gray-100 p-4 rounded-lg mb-4">
                    <table className="w-full text-sm">
                        <thead>
                            <tr className="border-b">
                                <th className="text-left py-2">Input</th>
                                <th className="text-left py-2">Normalized</th>
                                <th className="text-left py-2">Result</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr className="border-b">
                                <td className="py-2">admin</td>
                                <td className="py-2">admin</td>
                                <td className="py-2 text-red-600">❌ Restricted</td>
                            </tr>
                            <tr className="border-b">
                                <td className="py-2">ADMIN</td>
                                <td className="py-2">admin</td>
                                <td className="py-2 text-red-600">❌ Restricted</td>
                            </tr>
                            <tr className="border-b">
                                <td className="py-2">a-d-m-i-n</td>
                                <td className="py-2">admin</td>
                                <td className="py-2 text-red-600">❌ Restricted</td>
                            </tr>
                            <tr className="border-b">
                                <td className="py-2">john_doe</td>
                                <td className="py-2">johndoe</td>
                                <td className="py-2 text-green-600">✅ Allowed</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h2 id="error-messages" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Error Messages</h2>
                <p className="mb-4">The system provides category-specific error messages to users:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Message Categories</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>System</strong> - "This username is reserved for system use. Please choose another."</li>
                    <li><strong>Brand</strong> - "This username is reserved. Please choose another."</li>
                    <li><strong>Security</strong> - "This username is reserved for security purposes. Please choose another."</li>
                    <li><strong>General</strong> - "This username is not available. Please choose another."</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Security Considerations</h3>
                <p className="mb-4">Error messages are designed to:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Avoid Information Disclosure</strong> - Don't reveal the complete restricted list</li>
                    <li><strong>Provide Clear Guidance</strong> - Help users understand why their choice was rejected</li>
                    <li><strong>Maintain Consistency</strong> - Use similar messaging across all restriction types</li>
                </ul>

                <h2 id="api-endpoints" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">API Endpoints</h2>
                <p className="mb-4">The system provides several API endpoints for programmatic access:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Admin Management APIs</h3>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm">
                    <strong>GET</strong> /api/admin/restricted-usernames<br/>
                    <strong>POST</strong> /api/admin/restricted-usernames<br/>
                    <strong>DELETE</strong> /api/admin/restricted-usernames<br/>
                    <strong>POST</strong> /api/admin/restricted-usernames/category<br/>
                    <strong>DELETE</strong> /api/admin/restricted-usernames/category
                </div>
                <p className="mb-4">Admin-only endpoints for managing the restricted usernames configuration. Requires admin authentication.</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Username Validation</h3>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm">
                    <strong>POST</strong> /api/validate/username<br/>
                    <strong>GET</strong> /api/validate/username?username=testuser
                </div>
                <p className="mb-4">Validates username availability and restrictions. Returns detailed validation results including availability status and restriction reasons.</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Example API Usage</h3>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm overflow-x-auto">
                    <pre>{`// Validation API
fetch('/api/validate/username', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'testuser' })
})
.then(response => response.json())
.then(data => {
  if (data.data.isValid) {
    console.log('Username is available');
  } else {
    console.log('Error:', data.data.reason);
  }
});

// Admin API - Add username (requires admin auth)
fetch('/api/admin/restricted-usernames', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'x-user-email': '<EMAIL>'
  },
  body: JSON.stringify({ 
    username: 'newrestriction', 
    category: 'brand' 
  })
})
.then(response => response.json())
.then(data => console.log(data));

// Admin API - Get all restrictions
fetch('/api/admin/restricted-usernames')
.then(response => response.json())
.then(data => console.log(data));`}</pre>
                </div>

                <h2 id="monitoring" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Monitoring and Logging</h2>
                <p className="mb-4">The username restriction system includes comprehensive logging for monitoring and security purposes:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Logged Events</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Configuration Loading</strong> - System startup and configuration reload events</li>
                    <li><strong>Restriction Hits</strong> - When users attempt to use restricted usernames</li>
                    <li><strong>Validation Errors</strong> - System errors during validation process</li>
                    <li><strong>Performance Metrics</strong> - Response times and throughput statistics</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Log Examples</h3>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm">
                    <pre>{`[USERNAME_RESTRICTIONS] Loaded 85 restricted usernames
[USERNAME_RESTRICTIONS] Configuration loaded successfully
[USERNAME_RESTRICTIONS] Username validation: restricted (category: system)
[USERNAME_RESTRICTIONS] Failed to reload configuration: file not found`}</pre>
                </div>

                <h2 id="performance" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Performance Characteristics</h2>
                <p className="mb-4">The system is optimized for high-performance validation:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Optimization Features</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>In-Memory Caching</strong> - Restricted usernames are cached in memory for O(1) lookup</li>
                    <li><strong>Singleton Pattern</strong> - Single instance manages all validation requests</li>
                    <li><strong>Pre-computed Normalization</strong> - Usernames are normalized once during loading</li>
                    <li><strong>Fail-Safe Design</strong> - System errors don't block user registration</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Performance Targets</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Validation Speed</strong> - Under 100ms per validation request</li>
                    <li><strong>Memory Usage</strong> - Minimal footprint with efficient data structures</li>
                    <li><strong>Concurrent Requests</strong> - Handles multiple simultaneous validations</li>
                    <li><strong>Startup Time</strong> - Fast initialization without blocking other services</li>
                </ul>

                <h2 id="troubleshooting" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Troubleshooting</h2>
                <p className="mb-4">Common issues and their solutions:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Configuration Issues</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Invalid JSON</strong> - Check file syntax and validate JSON structure</li>
                    <li><strong>Missing Categories</strong> - Ensure all required categories are present</li>
                    <li><strong>Empty Arrays</strong> - Verify username arrays contain valid strings</li>
                    <li><strong>File Permissions</strong> - Ensure the application can read the configuration file</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Runtime Issues</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Service Not Initialized</strong> - Check application logs for initialization errors</li>
                    <li><strong>Validation Failures</strong> - Review error logs for specific failure reasons</li>
                    <li><strong>Performance Problems</strong> - Monitor response times and memory usage</li>
                    <li><strong>Inconsistent Behavior</strong> - Verify configuration consistency across environments</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Diagnostic Commands</h3>
                <div className="bg-gray-100 p-4 rounded-lg mb-4 font-mono text-sm">
                    <pre>{`// Check service status
console.log(getUsernameRestrictionStatus());

// Get all restricted usernames
console.log(getAllRestrictedUsernames());

// Test specific username
console.log(validateUsername('testuser'));

// Reload configuration
reloadUsernameRestrictions();`}</pre>
                </div>

                <h2 id="security" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Security Considerations</h2>
                <p className="mb-4">Important security aspects of the username restriction system:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Protection Measures</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Input Sanitization</strong> - All usernames are sanitized before validation</li>
                    <li><strong>Rate Limiting</strong> - API endpoints should be rate-limited to prevent abuse</li>
                    <li><strong>Fail-Safe Behavior</strong> - System errors don't expose restricted lists</li>
                    <li><strong>Audit Logging</strong> - All restriction events are logged for security monitoring</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Best Practices</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Regular Updates</strong> - Review and update restricted lists periodically</li>
                    <li><strong>Monitor Attempts</strong> - Watch for patterns in restriction attempts</li>
                    <li><strong>Secure Configuration</strong> - Protect configuration files from unauthorized access</li>
                    <li><strong>Backup Procedures</strong> - Maintain backups of configuration changes</li>
                </ul>

                <h2 id="maintenance" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Maintenance Procedures</h2>
                <p className="mb-4">Regular maintenance tasks to keep the system running smoothly:</p>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Weekly Tasks</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Review Logs</strong> - Check for unusual patterns or errors</li>
                    <li><strong>Monitor Performance</strong> - Verify response times are within targets</li>
                    <li><strong>Check Configuration</strong> - Ensure configuration file integrity</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Monthly Tasks</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Update Restrictions</strong> - Add new restricted terms as needed</li>
                    <li><strong>Review Categories</strong> - Assess if new categories are needed</li>
                    <li><strong>Performance Analysis</strong> - Analyze usage patterns and optimization opportunities</li>
                    <li><strong>Security Review</strong> - Evaluate security logs and update procedures</li>
                </ul>
            </div>

            <a
                href="#top"
                className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
            >
                ↑
            </a>
        </div>
    );
};

export default UsernameRestrictionsPage;