"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>s<PERSON><PERSON><PERSON>,
  CollapsibleSec<PERSON>,
  BackToDocsButton,
  InfoBox,
  CodeBlock,
} from "@/components/docs";
import { Link2, Info } from "lucide-react";
// CodeBlock is exported from the main docs index file

export default function HomeLinkDoc() {
  const componentSnippet = `import HomeLink from '@/app/components/HomeLink';

export default function Header() {
  return (
    <header className="flex items-center gap-4 p-4">
      <HomeLink />
      {/* ...other nav items */}
    </header>
  );
}`;

  return (
    <DocsContainer>
      <DocsHeader
        title="HomeLink Component"
        description="Clickable logo link used across the application to navigate back to the home page."
        status="complete"
        breadcrumbs={[
          { label: "Documentation", href: "/admin/docs" },
          { label: "Core Concepts", href: "/admin/docs/core-concepts" },
          { label: "Components", href: "/admin/docs/core-concepts/components" },
          { label: "HomeLink", href: "/admin/docs/core-concepts/components/home-link" },
        ]}
      />

      <CollapsibleSection title="✨ Overview" defaultOpen>
        <p className="text-gray-700">
          <code className="font-mono text-sm">HomeLink</code> renders the Review&nbsp;It logo and brand text wrapped
          in a Next.js <code className="font-mono text-sm">&lt;Link&gt;</code> that points to <code className="font-mono text-sm">/</code>.
          It provides a consistent, accessible way for users to return to the landing page from anywhere in the product.
        </p>
      </CollapsibleSection>

      <CollapsibleSection title="🔧 Implementation Details" defaultOpen>
        <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
          <li>
            Uses <code className="font-mono text-sm">next/link</code> for client-side navigation.
          </li>
          <li>
            Logo image loaded via <code className="font-mono text-sm">next/image</code> from <code className="font-mono text-sm">/public/logo.png</code>.
          </li>
          <li>
            Brand name wrapped with <code className="font-mono text-sm">BungeeTintText</code> for animated tint effect.
          </li>
          <li>No props – simply render the component where the logo link is needed.</li>
        </ul>
      </CollapsibleSection>

      <CollapsibleSection title="⚡ Quick Start" defaultOpen>
        <CodeBlock language="tsx" code={componentSnippet} />
      </CollapsibleSection>

      <CollapsibleSection title="ℹ️ Accessibility & UX Tips" defaultOpen={false}>
        <InfoBox type="info" icon={<Info className="h-4 w-4" />}>
          Ensure there is sufficient color contrast for the hover state and that the text alternative for the logo (<code className="font-mono text-sm">alt="Review It Logo"</code>) remains descriptive.
        </InfoBox>
      </CollapsibleSection>

      <BackToDocsButton />
    </DocsContainer>
  );
}
