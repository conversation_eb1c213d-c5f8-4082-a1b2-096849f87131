import React from 'react';

const ImageManagementPage = () => {
    return (
        <div id="top" className="max-w-4xl mx-auto">
            <div className="text-gray-700 leading-relaxed">
                <div className="mb-8 text-sm text-gray-500">
                    <a href="/admin/docs" className="text-blue-600 hover:underline">Documentation</a> / <span>Image Management</span>
                </div>

                <h1 className="text-4xl font-bold mb-6 text-gray-900">
                    Image Management
                    <span className="inline-block ml-2 px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Complete</span>
                </h1>

                <h2 id="cloudinary-instances" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Cloudinary Instances</h2>
                <p className="mb-4">The application uses two separate Cloudinary instances for different image handling needs:</p>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">General Purpose Instance</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Use Cases</strong> - Profile pictures, claim images, promotion banners</li>
                    <li><strong>Configuration</strong> - Uses standard environment variables:
                        <ul className="mt-2 ml-6 space-y-1 text-sm">
                            <li><code>CLOUDINARY_CLOUD_NAME</code></li>
                            <li><code>CLOUDINARY_API_KEY</code></li>
                            <li><code>CLOUDINARY_API_SECRET</code></li>
                        </ul>
                    </li>
                    <li><strong>Image Specs</strong> - 800x800px max, ~300KB limit, 0.7 quality</li>
                    <li><strong>Folders</strong> - Organized by type (profile, claim, promotion)</li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Product Gallery Instance</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Use Cases</strong> - High-quality product images and galleries</li>
                    <li><strong>Configuration</strong> - Uses dedicated environment variables:
                        <ul className="mt-2 ml-6 space-y-1 text-sm">
                            <li><code>PRODUCT_CLOUDINARY_CLOUD_NAME</code></li>
                            <li><code>PRODUCT_CLOUDINARY_API_KEY</code></li>
                            <li><code>PRODUCT_CLOUDINARY_API_SECRET</code></li>
                        </ul>
                    </li>
                    <li><strong>Image Specs</strong> - 1000x1000px max, ~800KB limit, 0.85 quality</li>
                    <li><strong>Folder</strong> - All product images stored in 'product_gallery'</li>
                </ul>

                <h2 id="image-processing" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Image Processing</h2>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Client-Side Resizing</h3>
                <p className="mb-4">Images are processed in the browser before upload to optimize performance:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>General Images</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>Max dimensions: 800x800 pixels</li>
                            <li>Quality: 0.7 (70%)</li>
                            <li>File size limit: ~300KB</li>
                            <li>Uses: Profile pictures, claim images</li>
                        </ul>
                    </li>
                    <li><strong>Product Images</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>Max dimensions: 1000x1000 pixels</li>
                            <li>Quality: 0.85 (85%)</li>
                            <li>File size limit: ~800KB</li>
                            <li>Uses: Product galleries and displays</li>
                        </ul>
                    </li>
                    <li><strong>Optimization Features</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>Early exit for already-small files</li>
                            <li>Progressive quality reduction if needed</li>
                            <li>Aspect ratio preservation</li>
                            <li>Memory-efficient canvas operations</li>
                        </ul>
                    </li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Server-Side Processing</h3>
                <p className="mb-4">Cloudinary performs additional optimizations after upload:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Automatic Format Selection</strong> - Serves WebP when supported</li>
                    <li><strong>Quality Optimization</strong> - Uses 'auto:good' for optimal quality/size</li>
                    <li><strong>Responsive Delivery</strong> - Serves appropriate sizes for different devices</li>
                    <li><strong>CDN Distribution</strong> - Global edge caching for fast delivery</li>
                </ul>

                <h2 id="ui-components" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">UI Components</h2>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Single Image Upload</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Component</strong> - ImageUpload.tsx</li>
                    <li><strong>Features</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>Drag and drop support</li>
                            <li>File picker button</li>
                            <li>Progress indicator</li>
                            <li>Preview thumbnail</li>
                            <li>Error handling</li>
                        </ul>
                    </li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Product Gallery Upload</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Component</strong> - ProductImageUploadModal.tsx</li>
                    <li><strong>Features</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>Multi-image upload support</li>
                            <li>Drag and drop for multiple files</li>
                            <li>Individual progress tracking</li>
                            <li>Grid preview with delete options</li>
                            <li>10 image limit enforcement</li>
                        </ul>
                    </li>
                    <li><strong>Integration</strong> - Used in NewProductForm and EditProductForm</li>
                </ul>

                <h2 id="performance-optimization" className="text-3xl font-bold mt-10 mb-4 pb-2 border-b border-gray-200 text-gray-900">Performance Optimization</h2>
                
                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Web Worker Implementation</h3>
                <p className="mb-4">Image processing uses Web Workers for off-main-thread processing when supported:</p>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>Worker Features</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>OffscreenCanvas for non-blocking canvas operations</li>
                            <li>Binary search for optimal quality (faster than linear steps)</li>
                            <li>Concurrent processing of multiple images</li>
                            <li>Automatic memory management with ImageBitmap cleanup</li>
                        </ul>
                    </li>
                    <li><strong>Browser Support</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>Chrome/Edge/Opera, Safari ≥ 5.1, Firefox ≥ 3.5</li>
                            <li>Android Chrome/WebView, iOS Safari</li>
                            <li>Feature detection ensures compatibility</li>
                        </ul>
                    </li>
                    <li><strong>Fallback Strategy</strong>
                        <ul className="mt-2 ml-6 space-y-1">
                            <li>Automatic fallback to main thread if worker unsupported</li>
                            <li>Graceful degradation for older browsers</li>
                            <li>Same API regardless of processing method</li>
                        </ul>
                    </li>
                </ul>

                <h3 className="text-2xl font-bold mt-8 mb-3 text-gray-900">Performance Benefits</h3>
                <ul className="mb-4 pl-6 space-y-2">
                    <li><strong>UI Responsiveness</strong> - No main thread blocking during image processing</li>
                    <li><strong>Smooth Animations</strong> - Progress bars and UI updates remain fluid</li>
                    <li><strong>Concurrent Processing</strong> - Multiple images can be processed simultaneously</li>
                    <li><strong>Faster Quality Optimization</strong> - Binary search vs linear quality reduction</li>
                    <li><strong>Early Exit</strong> - Small images skip processing entirely</li>
                </ul>
            </div>
        </div>
    );
};

export default ImageManagementPage;
