import React from 'react';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getProductById } from '@/app/actions/productActions';
import SchemaMarkup from '@/components/product/SchemaMarkup';
import ProductPageClient from '@/components/product/ProductPageClient';
import { headers } from 'next/headers';
import ClaimProductSection from '@/components/product/ClaimProductSection';
import { ResponseTimeStat } from '@/components/ResponseTimeStat';
import { getCachedMetric, getFreshResponseMetric, getResponseTimeCacheKey } from '@/app/util/redisMetrics';

interface ProductPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const product = await getProductById(params.id);

  if (!product) {
    return {
      title: 'Product Not Found | Review It',
      description: 'The requested product could not be found.',
    };
  }

  return {
    title: `${product.name} | Review It`,
    description: product.description?.substring(0, 160) || `Read reviews for ${product.name} on Review It.`,
    openGraph: {
      title: product.name,
      description: product.description,
      images: product.display_image ? [product.display_image] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: product.name,
      description: product.description,
      images: product.display_image ? [product.display_image] : [],
    }
  };
}

// Fresh response metric calculation is now handled by redisMetrics utility

export default async function ProductPage({ params }: ProductPageProps) {
  const product = await getProductById(params.id);

  if (!product) {
    notFound();
  }

  // Get user agent and referrer for analytics
  const headersList = headers();
  const userAgent = headersList.get('user-agent');
  const referrer = headersList.get('referer');

  // Determine device type from user agent
  const deviceType = userAgent
    ? /mobile|android|iphone/i.test(userAgent)
      ? 'mobile'
      : /tablet|ipad/i.test(userAgent)
        ? 'tablet'
        : 'desktop'
    : undefined;


  // Get cached response time metric
  const averageResponse = await getCachedMetric(
    getResponseTimeCacheKey('product', params.id),
    () => getFreshResponseMetric(params.id),
    3600 // 1 hour TTL
  );

  return (
    <>
      <SchemaMarkup product={product} />
      <ProductPageClient product={product} />
      <ResponseTimeStat 
        averageHours={averageResponse ?? 0}
        locale={headers().get('accept-language')?.includes('fr') ? 'fr' : 'en'}
      />
      {/* {!product.hasOwner && product.id && ( */}
      {/*     <ClaimProductSection productId={product.id} /> */}
      {/* )} */}
    </>
  );
}
