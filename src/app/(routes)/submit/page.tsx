
import ProductForm from "@/app/components/NewProductForm";

const page = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-myTheme-lightbg via-myTheme-light to-white">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-myTheme-primary to-myTheme-secondary">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4">
              Submit Your Business
            </h1>
            <p className="text-lg sm:text-xl text-white/90 max-w-2xl mx-auto">
              Share your business with our community and start receiving reviews
            </p>
          </div>
        </div>
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
      </div>
      
      {/* Form Section */}
      <div className="relative -mt-8 px-4 sm:px-6 lg:px-8 pb-16">
        <ProductForm />
      </div>
    </div>
  );
};

export default page;
