import Redis, { RedisOptions } from 'ioredis';

interface RedisConfig {
  host?: string;
  port?: number;
  password?: string;
  tlsEnabled?: boolean;
}

class RedisService {
  private static instance: RedisService;
  private client: Redis;
  private healthy: boolean = true;
  private lastHealthCheck: number = 0;
  private readonly HEALTH_CHECK_INTERVAL: number;
  private readonly COMMAND_TIMEOUT: number;
  private readonly SET_TIMEOUT: number;

  private constructor() {
    this.HEALTH_CHECK_INTERVAL = parseInt(process.env.REDIS_HEALTH_CHECK_INTERVAL || '30000');
    this.COMMAND_TIMEOUT = parseInt(process.env.REDIS_COMMAND_TIMEOUT || '5000');
    this.SET_TIMEOUT = parseInt(process.env.REDIS_SET_TIMEOUT || '2000');
    this.client = this.createRedisClient();
    this.setupEventHandlers();
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  private createRedisClient(): Redis {
    const config = this.getRedisConfig();

    const options: RedisOptions = {
      host: config.host,
      port: config.port,
      password: config.password,
      connectTimeout: 30000,
      commandTimeout: 10000,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      enableOfflineQueue: false,
      retryStrategy: (times) => {
        const delay = Math.min(times * 100, 2000); // Exponential backoff
        return delay;
      },
      family: 4,
      keepAlive: 30000,
      showFriendlyErrorStack: true
    };

    if (config.tlsEnabled) {
      options.tls = {};
    }

    this.logConfiguration(config, options);
    return new Redis(options);
  }

  private getRedisConfig(): RedisConfig {
    return {
      host: process.env.UPSTASH_REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      tlsEnabled: process.env.REDIS_TLS_ENABLED === 'true'
    };
  }

  private logConfiguration(config: RedisConfig, options: RedisOptions): void {
    const redactedConnectionString = `redis${config.tlsEnabled ? 's' : ''}://${config.password ? ':REDACTED@' : ''}${config.host}:${config.port}`;
  }

  private setupEventHandlers(): void {
    this.client.on('error', (err: any) => {
      this.healthy = false;
    });

    this.client.on('connect', () => {
      this.healthy = true;
    });

    this.client.on('ready', () => {
      this.healthy = true;
    });

    this.client.on('close', () => {
      this.healthy = false;
    });

    this.client.on('reconnecting', () => {
    });

    this.client.on('end', () => {
      this.healthy = false;
    });
  }

  public async isHealthy(): Promise<boolean> {
    const now = Date.now();
    if (now - this.lastHealthCheck < this.HEALTH_CHECK_INTERVAL) {
      return this.healthy;
    }

    try {
      if (this.client.status !== 'ready') {
        await this.client.connect().catch(err => {
          throw err;
        });
      }

      const pingPromise = this.client.ping();
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Health check timeout after ${this.COMMAND_TIMEOUT}ms`)), this.COMMAND_TIMEOUT)
      );

      await Promise.race([pingPromise, timeoutPromise]);
      this.healthy = true;
      this.lastHealthCheck = now;
      return true;
    } catch (error: any) {
      this.healthy = false;
      this.lastHealthCheck = now;
      return false;
    }
  }

  public async get(key: string): Promise<string | null> {
    try {
      const getPromise = this.client.get(key);
      const timeoutPromise = new Promise<null>((_, reject) =>
        setTimeout(() => reject(new Error('Redis get timeout')), this.COMMAND_TIMEOUT)
      );

      const result = await Promise.race([getPromise, timeoutPromise]);
      return result;
    } catch (error) {
      this.healthy = false;
      return null;
    }
  }

  public async set(key: string, value: string, ttl: number): Promise<void> {
    if (!await this.isHealthy()) {
      return;
    }

    try {
      await this.client.setex(key, ttl, value);
    } catch (error) {
    }
  }

  public async del(key: string): Promise<void> {
    if (!(await this.isHealthy())) {
      return;
    }

    try {
      await this.client.del(key);
    } catch (error) {
    }
  }

  public async getFromCache<T>(key: string): Promise<T | null> {
    try {
      const data = await this.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      return null;
    }
  }

  public async setInCache(key: string, value: any, ttl: number): Promise<void> {
    try {
      await this.set(key, JSON.stringify(value), ttl);
    } catch (error) {
    }
  }

  // Advanced operations for complex use cases
  public async keys(pattern: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const stream = this.client.scanStream({
        match: pattern,
      });
      const keys: string[] = [];
      stream.on('data', (resultKeys) => {
        keys.push(...resultKeys);
      });
      stream.on('end', () => {
        resolve(keys);
      });
      stream.on('error', (err) => {
        reject(err);
      });
    });
  }

  public async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key);
    } catch (error) {
      return 0;
    }
  }

  public async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.client.expire(key, seconds);
    } catch (error) {
    }
  }

  // For testing purposes - allows graceful shutdown
  public async disconnect(): Promise<void> {
    await this.client.disconnect();
  }

  // Get raw client for advanced operations (use sparingly)
  public getClient(): Redis {
    return this.client;
  }
}

// Export singleton instance
export const redisService = RedisService.getInstance();
export default redisService;