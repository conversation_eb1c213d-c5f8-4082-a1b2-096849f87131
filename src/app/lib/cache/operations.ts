/**
 * Safe cache operations with circuit breaker
 */

import { redisService } from '../redis';
import { shouldUseCache, trackCacheHit, trackCacheMiss, recordCacheFailure } from './stats';

// Safe cache operations with circuit breaker
export async function safeGetFromCache<T>(key: string): Promise<T | null> {
  if (!shouldUseCache()) {
    return null;
  }
  
  try {
    const result = await redisService.get(key);
    if (result) {
      trackCacheHit(key);
      return JSON.parse(result);
    } else {
      trackCacheMiss(key);
      return null;
    }
  } catch (error) {
    recordCacheFailure();
    return null;
  }
}

export async function safeSetToCache(key: string, value: any, ttl?: number): Promise<boolean> {
  if (!shouldUseCache()) {
    return false;
  }
  
  try {
    if (ttl) {
      await redisService.set(key, JSON.stringify(value), ttl);
    } else {
      await redisService.setInCache(key, value, 3600); // Default 1 hour TTL
    }
    return true;
  } catch (error) {
    recordCacheFailure();
    return false;
  }
}