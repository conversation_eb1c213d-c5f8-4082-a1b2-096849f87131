/**
 * Cache performance tracking and circuit breaker functionality
 */

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
}

// Cache performance tracking
const cacheStats = {
  hits: 0,
  misses: 0,
  errors: 0,
  lastError: null as Date | null
};

// Circuit breaker state
const circuitBreaker = {
  isOpen: false,
  failureCount: 0,
  lastFailureTime: null as Date | null,
  threshold: 5, // Open circuit after 5 failures
  timeout: 30000 // 30 seconds timeout
};

export function trackCacheHit(key: string): void {
  cacheStats.hits++;
}

export function trackCacheMiss(key: string): void {
  cacheStats.misses++;
}

export function getCacheStats(): CacheStats {
  const total = cacheStats.hits + cacheStats.misses;
  return {
    hits: cacheStats.hits,
    misses: cacheStats.misses,
    hitRate: total > 0 ? (cacheStats.hits / total) * 100 : 0,
    totalRequests: total
  };
}

export function getCacheHitRate(): number {
  return getCacheStats().hitRate;
}

// Circuit breaker functions
export function recordCacheFailure(): void {
  cacheStats.errors++;
  cacheStats.lastError = new Date();
  circuitBreaker.failureCount++;
  circuitBreaker.lastFailureTime = new Date();
  
  if (circuitBreaker.failureCount >= circuitBreaker.threshold) {
    circuitBreaker.isOpen = true;
  }
}

export function resetCircuitBreaker(): void {
  circuitBreaker.isOpen = false;
  circuitBreaker.failureCount = 0;
  circuitBreaker.lastFailureTime = null;
}

export function shouldUseCache(): boolean {
  if (!circuitBreaker.isOpen) {
    return true;
  }
  
  // Check if timeout has passed
  if (circuitBreaker.lastFailureTime) {
    const timeSinceLastFailure = Date.now() - circuitBreaker.lastFailureTime.getTime();
    if (timeSinceLastFailure > circuitBreaker.timeout) {
      resetCircuitBreaker();
      return true;
    }
  }
  
  return false;
}

// Cache health check
export async function checkCacheHealth(): Promise<{
  isHealthy: boolean;
  stats: CacheStats & { errors: number; lastError: Date | null };
  circuitBreakerStatus: typeof circuitBreaker;
}> {
  const { redisService } = await import('../redis');
  const { generateCacheKey } = await import('./keys');
  
  const testKey = generateCacheKey('health_check', Date.now().toString());
  const testValue = { timestamp: new Date().toISOString() };
  
  try {
    // Test write
    await redisService.set(testKey, JSON.stringify(testValue), 10);
    
    // Test read
    const retrieved = await redisService.get(testKey);
    const isHealthy = retrieved !== null && JSON.parse(retrieved).timestamp === testValue.timestamp;
    
    // Cleanup
    await redisService.del(testKey);
    
    return {
      isHealthy,
      stats: {
        ...getCacheStats(),
        errors: cacheStats.errors,
        lastError: cacheStats.lastError
      },
      circuitBreakerStatus: { ...circuitBreaker }
    };
  } catch (error) {
    recordCacheFailure();
    return {
      isHealthy: false,
      stats: {
        ...getCacheStats(),
        errors: cacheStats.errors,
        lastError: cacheStats.lastError
      },
      circuitBreakerStatus: { ...circuitBreaker }
    };
  }
}