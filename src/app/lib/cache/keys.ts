/**
 * Cache key generators for standardized cache keys
 */

// Cache versioning for schema changes
const CACHE_VERSION = 'v2';

// Cache key generators for standardized cache keys
function generateCacheKey(prefix: string, ...parts: string[]): string {
  return `reviewit:${CACHE_VERSION}:${prefix}:${parts.join(':')}`;
}

function hashQuery(query: string): string {
  return require('crypto').createHash('md5').update(query.toLowerCase().trim()).digest('hex');
}

// Specific key generators for each endpoint
export const CacheKeys = {
  topReviewers: (limit: number = 6) => generateCacheKey('top_reviewers', limit.toString()),
  productSearch: (query: string) => generateCacheKey('product_search', hashQuery(query)),
  productDetails: (productId: string) => generateCacheKey('product_details', productId),
  productReviews: (productId: string, isPublic: boolean) =>
    generateCacheKey('product_reviews', productId, isPublic.toString()),
  allProducts: () => generateCacheKey('all_products'),
  allProductsWeighted: () => generateCacheKey('all_products', '_weighted'),
  viewCount: (productId: string) => generateCacheKey('view_count', productId),
  adminRecentReviews: () => generateCacheKey('admin', 'recent_reviews'),
  adminReviewStats: () => generateCacheKey('admin', 'review_stats'),
  adminDashboardMetrics: () => generateCacheKey('admin', 'dashboard_metrics'),
  adminReports: () => generateCacheKey('admin', 'reports'),
  adminReportStats: () => generateCacheKey('admin', 'report_stats'),
  latestReviews: () => generateCacheKey('reviews', 'latest'),
  popularReviews: () => generateCacheKey('reviews', 'popular'),
  trendingReviews: () => generateCacheKey('reviews', 'trending'),
  // Top attention products (admin global)
  topAttention: (limit: number = 5) => generateCacheKey('attention', 'top', limit.toString()),
  // Owner-admin: top attention products for business
  businessTopAttention: (businessId: string, limit: number = 5) => generateCacheKey('attention', 'business', businessId, limit.toString()),
  // Comment-specific cache keys
  reviewComments: (reviewId: string) => generateCacheKey('comments', 'review', reviewId),
  // Analytics cache keys
  weightedRatingAnalytics: () => generateCacheKey('analytics', 'weighted_rating')
};

export { generateCacheKey, hashQuery };