/**
 * Cache invalidation utilities
 */

import { redisService } from '../redis';
import { CacheKeys, generateCache<PERSON>ey } from './keys';

// Cache invalidation utilities
export async function invalidateProductCache(productId: string): Promise<void> {
  const keys = [
    CacheKeys.productDetails(productId),
    CacheKeys.productReviews(productId, true),
    CacheKeys.productReviews(productId, false)
  ];

  for (const key of keys) {
    try {
      await redisService.del(key);
    } catch (error) {
      // Silent fail for cache invalidation
    }
  }
}

// Invalidate product cache when reviews are added/updated
export async function invalidateAggregatedCachesOnReviewChange(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.popularReviews(),
      CacheKeys.trendingReviews(),
      CacheKeys.latestReviews(),
      CacheKeys.topReviewers(6),  // Invalidate top reviewers when review counts change
      CacheKeys.allProducts(),    // Invalidate all products cache
      CacheKeys.allProducts() + '_weighted'  // Invalidate weighted products cache
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
        // Silent fail for cache invalidation
      }
    }
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

export async function invalidateSearchCache(): Promise<void> {
  try {
    const pattern = generateCacheKey('product_search', '*');
    const keys = await redisService.keys(pattern);
    if (keys.length > 0) {
      for (const key of keys) {
        await redisService.del(key);
      }
    }
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

export async function invalidateAdminCache(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminReports(),
      CacheKeys.adminReportStats()
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
        // Silent fail for cache invalidation
      }
    }
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

export async function invalidateReviewCaches(): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.latestReviews(),
      CacheKeys.popularReviews(),
      CacheKeys.trendingReviews(),
      CacheKeys.topReviewers(6)
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
        // Silent fail for cache invalidation
      }
    }
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

export async function invalidateAllCaches(): Promise<void> {
  try {
    // Instead of FLUSHDB, selectively clear only application cache keys
    // This preserves username restrictions analytics and other non-cache data
    
    // Clear main cache categories
    await invalidateAllProductsCache();
    await invalidateSearchCache();
    await invalidateAdminCache();
    await invalidateReviewCaches();
    
    // Clear product-specific caches with wildcard patterns
    try {
      const patterns = [
        generateCacheKey('product_details', '*'),
        generateCacheKey('product_reviews', '*'),
        generateCacheKey('view_count', '*'),
        generateCacheKey('comments', '*'),
        generateCacheKey('analytics', '*'),
        generateCacheKey('attention', '*'),
        generateCacheKey('reviews', '*'),
        'reviewit:*' // Clear all reviewit-prefixed keys
      ];
      
      for (const pattern of patterns) {
        const keys = await redisService.keys(pattern);
        for (const key of keys) {
          // Skip username restrictions analytics keys
          if (!key.startsWith('username_restrictions:')) {
            await redisService.del(key);
          }
        }
      }
    } catch (patternError) {
      console.error('Error clearing pattern-based cache keys:', patternError);
    }
    
    console.log('[CACHE] Selective cache invalidation completed, preserved username restrictions analytics');
  } catch (error) {
    console.error('Error in selective cache invalidation:', error);
    // If selective clearing fails, fall back to the old method but warn about analytics loss
    console.warn('[CACHE] Falling back to FLUSHDB - this will clear username restrictions analytics');
    await redisService.getClient().flushdb();
  }
}

export async function invalidateAllProductsCache(): Promise<void> {
  try {
    const allProductsKey = CacheKeys.allProducts();
    await redisService.del(allProductsKey);
    
    // Also clear the weighted products cache
    const weightedProductsKey = CacheKeys.allProductsWeighted();
    await redisService.del(weightedProductsKey);
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

// NEW: Invalidate only comment caches when comments change
export async function invalidateCommentCache(reviewId: string): Promise<void> {
  try {
    const commentCacheKey = CacheKeys.reviewComments(reviewId);
    await redisService.del(commentCacheKey);
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

// UPDATED: This function is now used for votes, not comments
export async function invalidateCachesOnVote(productId: string): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.productDetails(productId),
      CacheKeys.productReviews(productId, true),
      CacheKeys.productReviews(productId, false),
    ];

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
        // Silent fail for cache invalidation
      }
    }
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

// DEPRECATED: Use invalidateCommentCache instead for comment changes
export async function invalidateCachesOnComment(productId: string): Promise<void> {
  return invalidateCachesOnVote(productId);
}

// Business cache invalidation utilities
export async function invalidateBusinessCaches(businessId: string): Promise<void> {
  try {
    const pattern = generateCacheKey('business_analytics', businessId, '*');
    const keys = await redisService.keys(pattern);
    
    const keysToInvalidate = [
      ...keys,
      generateCacheKey('traffic_sources', businessId, '*'),
      generateCacheKey('promotions', 'business', businessId),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats()
    ];

    for (const key of keysToInvalidate) {
      try {
        if (key.includes('*')) {
          // Handle wildcard patterns
          const wildcardKeys = await redisService.keys(key);
          for (const wildcardKey of wildcardKeys) {
            await redisService.del(wildcardKey);
          }
        } else {
          await redisService.del(key);
        }
      } catch (error) {
        // Silent fail for cache invalidation
      }
    }
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

// Product ownership change cache invalidation
export async function invalidateCachesOnOwnershipChange(productId: string, businessId?: string): Promise<void> {
  try {
    const keysToInvalidate = [
      CacheKeys.productDetails(productId),
      CacheKeys.productReviews(productId, true),
      CacheKeys.productReviews(productId, false),
      CacheKeys.allProducts(),
      CacheKeys.adminDashboardMetrics(),
      CacheKeys.adminRecentReviews(),
      CacheKeys.adminReviewStats()
    ];

    // Add business-specific cache invalidation if businessId is provided
    if (businessId) {
      const businessPattern = generateCacheKey('business_analytics', businessId, '*');
      const businessKeys = await redisService.keys(businessPattern);
      keysToInvalidate.push(...businessKeys);
      keysToInvalidate.push(generateCacheKey('promotions', 'business', businessId));
    }

    // Invalidate search cache as product ownership affects search results
    await invalidateSearchCache();

    for (const key of keysToInvalidate) {
      try {
        await redisService.del(key);
      } catch (error) {
        // Silent fail for cache invalidation
      }
    }
  } catch (error) {
    // Silent fail for cache invalidation
  }
}

// Batch cache invalidation with circuit breaker pattern
export async function batchInvalidateCache(keys: string[], maxRetries: number = 3): Promise<void> {
  // Use the raw Redis client for pipeline operations
  const client = redisService.getClient();
  const pipeline = client.pipeline();
  
  for (const key of keys) {
    pipeline.del(key);
  }

  let retries = 0;
  while (retries < maxRetries) {
    try {
      await pipeline.exec();
      return;
    } catch (error) {
      retries++;
      
      if (retries >= maxRetries) {
        // Fall back to individual deletions
        for (const key of keys) {
          try {
            await redisService.del(key);
          } catch (individualError) {
            // Silent fail for cache invalidation
          }
        }
      } else {
        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
      }
    }
  }
}