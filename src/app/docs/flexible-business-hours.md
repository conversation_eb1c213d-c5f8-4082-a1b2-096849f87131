# Flexible Business Hours Feature

## Overview
The flexible business hours feature allows businesses to specify different opening and closing times for each day of the week, as well as indicate whether they are open on holidays. This is a significant improvement over the previous implementation which only supported a single opening and closing time applied to all selected days.

## Data Model

### BusinessHours Type
```typescript
type DayOfWeek = 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';

interface DaySchedule {
  day: DayOfWeek;
  isClosed: boolean;
  openTime: string | null; // Format: "HH:MM" (24-hour)
  closeTime: string | null; // Format: "HH:MM" (24-hour)
}

interface BusinessHours {
  schedules: DaySchedule[];
  openOnHolidays: boolean;
}
```

### Database Schema
The flexible business hours are stored in a JSON field called `businessHours` in the `Product` model. The existing fields (`openingHrs`, `closingHrs`, and `openingDays`) are maintained for backward compatibility.

## Components

### BusinessHoursInput
A reusable React component for inputting flexible business hours. It provides:
- Per-day schedule configuration (open/closed, opening time, closing time)
- Quick setup tabs for weekday/weekend hours
- Holiday toggle
- Copy hours functionality between days

**Usage:**
```tsx
<BusinessHoursInput
  value={product.businessHours || createDefaultBusinessHours()}
  onChange={(businessHours) => {
    setProduct({
      ...product,
      businessHours,
      // Update legacy fields for backward compatibility
      openingDays: businessHours.schedules
        .filter(schedule => !schedule.isClosed)
        .map(schedule => schedule.day),
    });
  }}
/>
```

### FlexibleBusinessHours
A display component for showing business hours in product cards and profiles. It provides:
- Compact view with today's hours and expandable full schedule
- Full view with grouped days that share the same hours
- Holiday status indicator
- Today's status (open/closed)

**Usage:**
```tsx
<FlexibleBusinessHours product={product} compact={true} />
```

## Utility Functions

### createDefaultBusinessHours
Creates a default business hours object with all weekdays open 9am-5pm and weekends closed.

```typescript
const defaultHours = createDefaultBusinessHours();
```

### formatBusinessHours
Formats business hours for display.

```typescript
const formattedHours = formatBusinessHours(product.businessHours);
```

### isBusinessOpen
Determines if a business is currently open based on business hours and current time.

```typescript
const { isOpen, message } = isBusinessOpen(product.businessHours);
```

## Backward Compatibility
The feature maintains backward compatibility with existing data:
- Legacy fields (`openingHrs`, `closingHrs`, `openingDays`) are still supported
- The UI will display flexible hours if available, otherwise fall back to legacy format
- When updating business hours with the new input, legacy fields are also updated

## Implementation Notes
1. The flexible business hours input is integrated into both the new product form and edit product form
2. Validation ensures at least one day is open in either legacy or flexible business hours
3. The product card UI displays the new format when available, with a compact view by default
4. Hours are intelligently grouped by matching schedules to provide a cleaner display

## Future Enhancements
- Support for multiple time ranges per day (e.g., lunch breaks)
- Special holiday schedules
- Seasonal hours
- Integration with a holiday API to automatically determine if today is a holiday
