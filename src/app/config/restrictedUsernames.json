{"version": "1.0", "lastUpdated": "2025-01-21", "categories": {"system": {"description": "System and platform reserved names", "usernames": ["admin", "administrator", "root", "system", "api", "www", "mail", "email", "ftp", "http", "https", "ssl", "tls", "dns", "server", "database", "db", "cache", "redis", "postgres", "mysql", "mongodb"]}, "brand": {"description": "Brand and product related names", "usernames": ["reviewit", "review-it", "reviewit-admin", "reviewitadmin", "support", "help", "contact", "info", "sales", "marketing", "press", "media", "news", "blog", "team", "staff", "official"]}, "common": {"description": "Common reserved terms", "usernames": ["user", "guest", "anonymous", "anon", "null", "undefined", "test", "testing", "demo", "example", "sample", "default", "temp", "temporary", "placeholder", "nobody", "everyone", "all", "public", "private"]}, "inappropriate": {"description": "Inappropriate or offensive terms", "usernames": ["spam", "fake", "bot", "abuse", "scam", "fraud", "phishing", "malware", "virus", "hack", "hacker", "exploit", "attack", "ddos", "troll", "toxic"]}, "security": {"description": "Security-related reserved terms", "usernames": ["security", "auth", "authentication", "authorization", "login", "logout", "signin", "signout", "register", "registration", "password", "token", "session", "cookie", "o<PERSON>h", "sso", "2fa", "mfa"]}}}