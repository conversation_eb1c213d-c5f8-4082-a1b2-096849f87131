/**
 * Username restrictions analytics and monitoring
 * Tracks usage patterns, restriction hits, and system performance
 */

import { redisService } from '../lib/redis';
import { getUsernameRestrictionStatus } from './usernameRestrictions';

// Analytics interfaces
export interface UsernameRestrictionMetrics {
  totalValidations: number;
  restrictionHits: number;
  restrictionRate: number;
  categoryBreakdown: { [category: string]: number };
  averageResponseTime: number;
  errorRate: number;
  lastUpdated: Date;
}

export interface UsernameRestrictionEvent {
  timestamp: Date;
  username: string; // Hashed for privacy
  category?: string;
  isRestricted: boolean;
  responseTime: number;
  userAgent?: string;
  ipAddress?: string; // Hashed for privacy
}

// Redis keys for analytics
const ANALYTICS_KEYS = {
  metrics: 'username_restrictions:metrics',
  dailyStats: (date: string) => `username_restrictions:daily:${date}`,
  categoryStats: 'username_restrictions:categories',
  errorLog: 'username_restrictions:errors',
  performanceLog: 'username_restrictions:performance'
};

class UsernameRestrictionsAnalytics {
  private static instance: UsernameRestrictionsAnalytics;

  private constructor() {}

  public static getInstance(): UsernameRestrictionsAnalytics {
    if (!UsernameRestrictionsAnalytics.instance) {
      UsernameRestrictionsAnalytics.instance = new UsernameRestrictionsAnalytics();
    }
    return UsernameRestrictionsAnalytics.instance;
  }

  /**
   * Track a username validation event
   */
  public async trackValidation(event: {
    username: string;
    isRestricted: boolean;
    category?: string;
    responseTime: number;
    userAgent?: string;
    ipAddress?: string;
  }): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const hashedUsername = this.hashString(event.username);
      const hashedIP = event.ipAddress ? this.hashString(event.ipAddress) : undefined;

      // Update daily statistics
      const dailyKey = ANALYTICS_KEYS.dailyStats(today);
      await Promise.all([
        redisService.incr(`${dailyKey}:total`),
        event.isRestricted ? redisService.incr(`${dailyKey}:restricted`) : Promise.resolve(),
        event.category ? redisService.incr(`${dailyKey}:category:${event.category}`) : Promise.resolve(),
      ]);

      // Update response time sum separately
      const currentSum = await redisService.get(`${dailyKey}:response_time_sum`);
      const newSum = (parseInt(currentSum || '0') + event.responseTime).toString();
      await redisService.set(`${dailyKey}:response_time_sum`, newSum, 86400);

      // Set expiration for daily stats (30 days)
      await redisService.expire(dailyKey, 30 * 24 * 60 * 60);

      // Update overall metrics
      await this.updateOverallMetrics(event);

      // Log significant events
      if (event.isRestricted) {
        console.log(`[USERNAME_RESTRICTIONS] Restriction hit: category=${event.category}, response_time=${event.responseTime}ms`);
      }

      // Track performance issues
      if (event.responseTime > 1000) {
        console.warn(`[USERNAME_RESTRICTIONS] Slow validation: ${event.responseTime}ms`);
        await redisService.incr(`${ANALYTICS_KEYS.performanceLog}:slow_validations`);
      }

    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Analytics tracking error:', error);
      // Don't throw - analytics failures shouldn't break validation
    }
  }

  /**
   * Track validation errors
   */
  public async trackError(error: {
    message: string;
    stack?: string;
    username?: string;
    timestamp?: Date;
  }): Promise<void> {
    try {
      const errorEvent = {
        timestamp: error.timestamp || new Date(),
        message: error.message,
        stack: error.stack,
        username: error.username ? this.hashString(error.username) : undefined
      };

      // Store error count and latest error info
      await Promise.all([
        redisService.incr(`${ANALYTICS_KEYS.errorLog}:count`),
        redisService.setInCache(`${ANALYTICS_KEYS.errorLog}:latest`, errorEvent, 86400)
      ]);

      console.error(`[USERNAME_RESTRICTIONS] Error tracked: ${error.message}`);
    } catch (trackingError) {
      console.error('[USERNAME_RESTRICTIONS] Error tracking failed:', trackingError);
    }
  }

  /**
   * Get current metrics
   */
  public async getMetrics(): Promise<UsernameRestrictionMetrics> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const dailyKey = ANALYTICS_KEYS.dailyStats(today);

      const [
        totalValidations,
        restrictionHits,
        categoryStats,
        responseTimeSum,
        errorCount
      ] = await Promise.all([
        redisService.get(`${dailyKey}:total`).then(val => parseInt(val || '0')),
        redisService.get(`${dailyKey}:restricted`).then(val => parseInt(val || '0')),
        this.getCategoryBreakdown(today),
        redisService.get(`${dailyKey}:response_time_sum`).then(val => parseInt(val || '0')),
        redisService.get(`${ANALYTICS_KEYS.errorLog}:count`).then(val => parseInt(val || '0'))
      ]);

      const averageResponseTime = totalValidations > 0 ? responseTimeSum / totalValidations : 0;

      const restrictionRate = totalValidations > 0 ? (restrictionHits / totalValidations) * 100 : 0;
      const errorRate = totalValidations > 0 ? (errorCount / totalValidations) * 100 : 0;

      return {
        totalValidations,
        restrictionHits,
        restrictionRate,
        categoryBreakdown: categoryStats,
        averageResponseTime,
        errorRate,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Metrics retrieval error:', error);
      return this.getDefaultMetrics();
    }
  }

  /**
   * Get historical data for a date range
   */
  public async getHistoricalData(startDate: string, endDate: string): Promise<{
    daily: { [date: string]: UsernameRestrictionMetrics };
    summary: UsernameRestrictionMetrics;
  }> {
    try {
      const dates = this.getDateRange(startDate, endDate);
      const dailyData: { [date: string]: UsernameRestrictionMetrics } = {};
      
      let totalValidations = 0;
      let totalRestrictions = 0;
      let totalResponseTime = 0;
      let totalErrors = 0;
      const combinedCategories: { [category: string]: number } = {};

      for (const date of dates) {
        const dailyKey = ANALYTICS_KEYS.dailyStats(date);
        
        const [
          validations,
          restrictions,
          categoryStats,
          responseTimeSum
        ] = await Promise.all([
          redisService.get(`${dailyKey}:total`).then(val => parseInt(val || '0')),
          redisService.get(`${dailyKey}:restricted`).then(val => parseInt(val || '0')),
          this.getCategoryBreakdown(date),
          redisService.get(`${dailyKey}:response_time_sum`).then(val => parseInt(val || '0'))
        ]);

        const avgResponseTime = validations > 0 ? responseTimeSum / validations : 0;

        dailyData[date] = {
          totalValidations: validations,
          restrictionHits: restrictions,
          restrictionRate: validations > 0 ? (restrictions / validations) * 100 : 0,
          categoryBreakdown: categoryStats,
          averageResponseTime: avgResponseTime,
          errorRate: 0, // Calculate if needed
          lastUpdated: new Date()
        };

        // Accumulate for summary
        totalValidations += validations;
        totalRestrictions += restrictions;
        totalResponseTime += avgResponseTime * validations;
        
        Object.entries(categoryStats).forEach(([category, count]) => {
          combinedCategories[category] = (combinedCategories[category] || 0) + (typeof count === 'number' ? count : parseInt(count as string) || 0);
        });
      }

      const summary: UsernameRestrictionMetrics = {
        totalValidations,
        restrictionHits: totalRestrictions,
        restrictionRate: totalValidations > 0 ? (totalRestrictions / totalValidations) * 100 : 0,
        categoryBreakdown: combinedCategories,
        averageResponseTime: totalValidations > 0 ? totalResponseTime / totalValidations : 0,
        errorRate: 0,
        lastUpdated: new Date()
      };

      return { daily: dailyData, summary };
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Historical data error:', error);
      return { daily: {}, summary: this.getDefaultMetrics() };
    }
  }

  /**
   * Get system health status
   */
  public async getHealthStatus(): Promise<{
    isHealthy: boolean;
    issues: string[];
    metrics: UsernameRestrictionMetrics;
    systemStatus: ReturnType<typeof getUsernameRestrictionStatus>;
  }> {
    try {
      const [metrics, systemStatus] = await Promise.all([
        this.getMetrics(),
        Promise.resolve(getUsernameRestrictionStatus())
      ]);

      const issues: string[] = [];

      // Check for issues
      if (!systemStatus.isInitialized) {
        issues.push('Username restriction service not initialized');
      }

      if (metrics.errorRate > 5) {
        issues.push(`High error rate: ${metrics.errorRate.toFixed(2)}%`);
      }

      if (metrics.averageResponseTime > 500) {
        issues.push(`Slow response times: ${metrics.averageResponseTime.toFixed(0)}ms average`);
      }

      if (systemStatus.totalRestrictions === 0) {
        issues.push('No restrictions loaded - using fallback configuration');
      }

      return {
        isHealthy: issues.length === 0,
        issues,
        metrics,
        systemStatus
      };
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Health check error:', error);
      return {
        isHealthy: false,
        issues: ['Health check failed'],
        metrics: this.getDefaultMetrics(),
        systemStatus: { isInitialized: false, totalRestrictions: 0, categories: [] }
      };
    }
  }

  /**
   * Clear analytics data
   */
  public async clearAnalytics(olderThanDays: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      
      const keys = await redisService.keys('username_restrictions:daily:*');
      const keysToDelete = keys.filter(key => {
        const dateStr = key.split(':').pop();
        if (!dateStr) return false;
        const keyDate = new Date(dateStr);
        return keyDate < cutoffDate;
      });

      if (keysToDelete.length > 0) {
        await Promise.all(keysToDelete.map(key => redisService.del(key)));
        console.log(`[USERNAME_RESTRICTIONS] Cleared ${keysToDelete.length} old analytics keys`);
      }
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Analytics cleanup error:', error);
    }
  }

  // Private helper methods
  private async updateOverallMetrics(event: {
    isRestricted: boolean;
    category?: string;
    responseTime: number;
  }): Promise<void> {
    try {
      await Promise.all([
        redisService.incr(`${ANALYTICS_KEYS.metrics}:total`),
        event.isRestricted ? redisService.incr(`${ANALYTICS_KEYS.metrics}:restricted`) : Promise.resolve(),
        event.category ? redisService.incr(`${ANALYTICS_KEYS.categoryStats}:${event.category}`) : Promise.resolve()
      ]);
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Overall metrics update error:', error);
    }
  }

  private async getCategoryBreakdown(date: string): Promise<{ [category: string]: number }> {
    try {
      const dailyKey = ANALYTICS_KEYS.dailyStats(date);
      const keys = await redisService.keys(`${dailyKey}:category:*`);
      const breakdown: { [category: string]: number } = {};

      for (const key of keys) {
        const category = key.split(':').pop();
        if (category) {
          const count = await redisService.get(key);
          breakdown[category] = parseInt(count || '0');
        }
      }

      return breakdown;
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Category breakdown error:', error);
      return {};
    }
  }

  private hashString(input: string): string {
    // Simple hash for privacy - in production, use a proper hashing library
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private getDateRange(startDate: string, endDate: string): string[] {
    const dates: string[] = [];
    const current = new Date(startDate);
    const end = new Date(endDate);

    while (current <= end) {
      dates.push(current.toISOString().split('T')[0]);
      current.setDate(current.getDate() + 1);
    }

    return dates;
  }

  private getDefaultMetrics(): UsernameRestrictionMetrics {
    return {
      totalValidations: 0,
      restrictionHits: 0,
      restrictionRate: 0,
      categoryBreakdown: {},
      averageResponseTime: 0,
      errorRate: 0,
      lastUpdated: new Date()
    };
  }
}

// Export singleton instance and convenience functions
const usernameRestrictionsAnalytics = UsernameRestrictionsAnalytics.getInstance();

export async function trackUsernameValidation(event: {
  username: string;
  isRestricted: boolean;
  category?: string;
  responseTime: number;
  userAgent?: string;
  ipAddress?: string;
}): Promise<void> {
  return usernameRestrictionsAnalytics.trackValidation(event);
}

export async function trackUsernameValidationError(error: {
  message: string;
  stack?: string;
  username?: string;
  timestamp?: Date;
}): Promise<void> {
  return usernameRestrictionsAnalytics.trackError(error);
}

export async function getUsernameRestrictionMetrics(): Promise<UsernameRestrictionMetrics> {
  return usernameRestrictionsAnalytics.getMetrics();
}

export async function getUsernameRestrictionHealthStatus(): Promise<{
  isHealthy: boolean;
  issues: string[];
  metrics: UsernameRestrictionMetrics;
  systemStatus: ReturnType<typeof getUsernameRestrictionStatus>;
}> {
  return usernameRestrictionsAnalytics.getHealthStatus();
}

export async function getUsernameRestrictionHistoricalData(startDate: string, endDate: string): Promise<{
  daily: { [date: string]: UsernameRestrictionMetrics };
  summary: UsernameRestrictionMetrics;
}> {
  return usernameRestrictionsAnalytics.getHistoricalData(startDate, endDate);
}

export async function clearUsernameRestrictionAnalytics(olderThanDays: number = 30): Promise<void> {
  return usernameRestrictionsAnalytics.clearAnalytics(olderThanDays);
}

export default usernameRestrictionsAnalytics;