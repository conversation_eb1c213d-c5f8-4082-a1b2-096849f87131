/**
 * Username restrictions configuration
 * This file manages restricted usernames to prevent users from choosing
 * reserved system names, brand names, or inappropriate usernames.
 */

import restrictedUsernamesConfig from './restrictedUsernames.json';

// TypeScript interfaces for configuration structure
export interface RestrictedUsernamesConfig {
  version: string;
  lastUpdated: string;
  categories: {
    [categoryName: string]: {
      description: string;
      usernames: string[];
    };
  };
}

export interface UsernameValidationResult {
  isValid: boolean;
  reason?: string;
  category?: string;
}

// Error messages for different restriction categories
const ERROR_MESSAGES = {
  system: "This username is reserved for system use. Please choose another.",
  brand: "This username is reserved. Please choose another.",
  security: "This username is reserved for security purposes. Please choose another.",
  inappropriate: "This username is not available. Please choose another.",
  general: "This username is not available. Please choose another.",
  validation_error: "Unable to validate username. Please try again."
};

// Fallback hardcoded list for reliability
const FALLBACK_RESTRICTED_USERNAMES = [
  'admin', 'administrator', 'root', 'system', 'api', 'www',
  'reviewit', 'review-it', 'support', 'help', 'contact',
  'user', 'guest', 'anonymous', 'test', 'null', 'undefined',
  'spam', 'fake', 'bot', 'abuse', 'security', 'auth'
];

class UsernameRestrictionService {
  private static instance: UsernameRestrictionService;
  private restrictedUsernames: Set<string> = new Set();
  private categoryMap: Map<string, string> = new Map();
  private isInitialized = false;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): UsernameRestrictionService {
    if (!UsernameRestrictionService.instance) {
      UsernameRestrictionService.instance = new UsernameRestrictionService();
    }
    return UsernameRestrictionService.instance;
  }

  /**
   * Initialize the restriction service by loading configuration
   */
  private initialize(): void {
    try {
      this.loadConfiguration();
      this.isInitialized = true;
      console.log(`[USERNAME_RESTRICTIONS] Loaded ${this.restrictedUsernames.size} restricted usernames`);
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Failed to initialize:', error);
      this.loadFallbackConfiguration();
      this.isInitialized = true;
    }
  }

  /**
   * Load configuration from JSON file
   */
  private loadConfiguration(): void {
    try {
      const config = restrictedUsernamesConfig as RestrictedUsernamesConfig;
      
      // Validate configuration structure
      if (!config.categories || typeof config.categories !== 'object') {
        throw new Error('Invalid configuration structure');
      }

      // Clear existing data
      this.restrictedUsernames.clear();
      this.categoryMap.clear();

      // Process each category
      for (const [categoryName, categoryData] of Object.entries(config.categories)) {
        if (!Array.isArray(categoryData.usernames)) {
          console.warn(`[USERNAME_RESTRICTIONS] Invalid usernames array for category: ${categoryName}`);
          continue;
        }

        // Add normalized usernames to the set and map
        for (const username of categoryData.usernames) {
          const normalized = this.normalizeUsername(username);
          this.restrictedUsernames.add(normalized);
          this.categoryMap.set(normalized, categoryName);
        }
      }

      console.log(`[USERNAME_RESTRICTIONS] Configuration loaded successfully`);
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Error loading configuration:', error);
      throw error;
    }
  }

  /**
   * Load fallback configuration when main config fails
   */
  private loadFallbackConfiguration(): void {
    console.warn('[USERNAME_RESTRICTIONS] Using fallback configuration');
    
    this.restrictedUsernames.clear();
    this.categoryMap.clear();

    for (const username of FALLBACK_RESTRICTED_USERNAMES) {
      const normalized = this.normalizeUsername(username);
      this.restrictedUsernames.add(normalized);
      this.categoryMap.set(normalized, 'general');
    }

    console.log(`[USERNAME_RESTRICTIONS] Fallback configuration loaded with ${this.restrictedUsernames.size} usernames`);
  }

  /**
   * Normalize username for consistent comparison
   * Converts to lowercase, trims whitespace, and removes common separators
   */
  private normalizeUsername(username: string): string {
    if (!username || typeof username !== 'string') {
      return '';
    }
    
    return username
      .toLowerCase()
      .trim()
      .replace(/[-_\.]/g, ''); // Remove hyphens, underscores, and dots
  }

  /**
   * Check if a username is restricted
   */
  public isRestricted(username: string): boolean {
    if (!this.isInitialized) {
      console.warn('[USERNAME_RESTRICTIONS] Service not initialized, allowing username');
      return false;
    }

    const normalized = this.normalizeUsername(username);
    return this.restrictedUsernames.has(normalized);
  }

  /**
   * Get detailed validation result for a username
   */
  public validateUsername(username: string): UsernameValidationResult {
    try {
      if (!this.isInitialized) {
        console.warn('[USERNAME_RESTRICTIONS] Service not initialized during validation');
        return {
          isValid: true // Fail-safe: allow username if service isn't working
        };
      }

      const normalized = this.normalizeUsername(username);
      
      if (!this.restrictedUsernames.has(normalized)) {
        return { isValid: true };
      }

      const category = this.categoryMap.get(normalized) || 'general';
      const reason = ERROR_MESSAGES[category as keyof typeof ERROR_MESSAGES] || ERROR_MESSAGES.general;

      return {
        isValid: false,
        reason,
        category
      };
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Error during validation:', error);
      // Fail-safe: allow username on error
      return {
        isValid: true
      };
    }
  }

  /**
   * Get all restricted usernames (flattened)
   */
  public getAllRestrictedUsernames(): string[] {
    return Array.from(this.restrictedUsernames);
  }

  /**
   * Reload configuration (for admin use)
   */
  public reloadConfiguration(): void {
    try {
      this.loadConfiguration();
      console.log('[USERNAME_RESTRICTIONS] Configuration reloaded successfully');
    } catch (error) {
      console.error('[USERNAME_RESTRICTIONS] Failed to reload configuration:', error);
      this.loadFallbackConfiguration();
    }
  }

  /**
   * Get service status and statistics
   */
  public getStatus(): {
    isInitialized: boolean;
    totalRestrictions: number;
    categories: string[];
  } {
    return {
      isInitialized: this.isInitialized,
      totalRestrictions: this.restrictedUsernames.size,
      categories: Array.from(new Set(this.categoryMap.values()))
    };
  }
}

// Export singleton instance and convenience functions
const usernameRestrictionService = UsernameRestrictionService.getInstance();

/**
 * Check if a username is restricted
 * @param username The username to check
 * @returns boolean indicating if the username is restricted
 */
export function isUsernameRestricted(username: string): boolean {
  return usernameRestrictionService.isRestricted(username);
}

/**
 * Validate a username and get detailed result
 * @param username The username to validate
 * @returns Validation result with details
 */
export function validateUsername(username: string): UsernameValidationResult {
  return usernameRestrictionService.validateUsername(username);
}

/**
 * Get all restricted usernames
 * @returns Array of all restricted usernames
 */
export function getAllRestrictedUsernames(): string[] {
  return usernameRestrictionService.getAllRestrictedUsernames();
}

/**
 * Reload the username restrictions configuration
 */
export function reloadUsernameRestrictions(): void {
  usernameRestrictionService.reloadConfiguration();
}

/**
 * Get service status
 */
export function getUsernameRestrictionStatus(): {
  isInitialized: boolean;
  totalRestrictions: number;
  categories: string[];
} {
  return usernameRestrictionService.getStatus();
}

export default usernameRestrictionService;