"use server";

import { iProduct } from "@/app/util/Interfaces";
import { prisma } from "@/app/util/prismaClient";
import { convertPrismaProductToIProduct } from "@/app/util/businessHoursUtils";

export async function getProductById(id: string): Promise<iProduct | null> {
  try {
    const product = await prisma.product.findUnique({
      where: { id },
      select: {
        id: true,
        address: true,
        latitude: true,
        longitude: true,
        createdDate: true,
        description: true,
        display_image: true,
        images: true,
        videos: true,
        links: true,
        name: true,
        tags: true,
        openingHrs: true,
        closingHrs: true,
        openingDays: true,
        businessHours: true,
        telephone: true,
        website: true,
        rating: true,
        hasOwner: true,
        ownerId: true,
        createdById: true,
        isDeleted: true,
        email: true,
        businessId: true,
        updatedAt: true,
        viewCount: true,
        rating5Stars: true,
        rating4Stars: true,
        rating3Stars: true,
        rating2Stars: true,
        rating1Star: true,
        featuredPosition: true,

        business: {
          select: {
            id: true,
            ownerName: true,
            isVerified: true,
            ownerId: true,
            subscriptionStatus: true,
            subscriptionExpiry: true,
            createdDate: true,
            owner: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        },
        reviews: {
          take: 3,
          orderBy: {
            createdDate: "desc",
          },
          select: {
            id: true,
            body: true,
            createdDate: true,
            
            rating: true,
            title: true,
            productId: true,
            userId: true,
            isVerified: true,
            verifiedBy: true,
            isPublic: true,
            images: true,
            videos: true,
            links: true,
            createdBy: true,
            isDeleted: true,
            verifiedAt: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                userName: true,
              },
            },
            _count: {
              select: {
                comments: true,
              },
            },
            voteCount: {
              select: {
                id: true,
                reviewId: true,
              },
            },
            likedBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                userName: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: {
              where: {
                isDeleted: false,
              },
            },
          },
        },
      },
    });

    if (!product) {
      return null;
    }

    const totalReviewsCount = product._count?.reviews || 0;
    const reviewsForAvg = await prisma.review.findMany({
      where: { productId: id, isDeleted: false },
      select: { rating: true },
    });
    const averageRating =
      totalReviewsCount > 0
        ? reviewsForAvg.reduce((acc, review) => acc + review.rating, 0) /
          totalReviewsCount
        : 0;

    let relatedProducts: any[] = [];
    if (product.tags && product.tags.length > 0) {
      relatedProducts = await prisma.product.findMany({
        where: {
          id: { not: id },
          tags: { hasSome: product.tags },
          isDeleted: false,
        },
        take: 4,
        select: {
          id: true,
          name: true,
          display_image: true,
          rating: true,
          _count: {
            select: {
              reviews: true,
            },
          },
        },
        orderBy: {
          createdDate: "desc",
        },
      });
    }

    const result = {
      ...product,
      rating: averageRating,
      relatedProducts: relatedProducts,
    };

    // Convert Prisma product to iProduct with parsed businessHours
    return convertPrismaProductToIProduct(result);
  } catch (error) {
    console.error("Error fetching product:", error);
    return null;
  }
}
