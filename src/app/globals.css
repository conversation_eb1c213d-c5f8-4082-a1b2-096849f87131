@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --primary: 59 130 246; /* blue-500 */
    --primary-foreground: 255 255 255;
    --muted: 248 250 252; /* slate-50 */
    --muted-foreground: 100 116 139; /* slate-500 */
  }

  html {
    color-scheme: light !important;
  }

  body {
    @apply bg-myTheme-light text-myTheme-lightTextBody;
  }

  @media (prefers-color-scheme: dark) {
    html,
    body {
      color-scheme: light !important;
      @apply bg-myTheme-light text-myTheme-lightTextBody;
    }
  }
}
.mantine-RichTextEditor-toolbar {
  @apply z-0;
}
/*.mantine-RichTextEditor-root {*/
/*  @apply dark:bg-myTheme-dark;*/
/*}*/
/*.mantine-RichTextEditor-toolbar {*/
/*  @apply dark:bg-myTheme-dark;*/
/*}*/
/*.mantine-UnstyledButton-root {*/
/*  @apply text-base dark:text-myTheme-light;*/
/*}*/
.mantine-TypographyStylesProvider-root
  .mantine-RichTextEditor-content
  .ProseMirror {
  @apply h-[180px] overflow-scroll;
}

/*.cl-signIn-root,*/
/*.cl-cardBox,*/
/*.cl-pageScrollBox,*/
/*.cl-navbar,*/
/*.cl-card {*/
/*  @apply dark:bg-myTheme-dark dark:text-myTheme-light text-myTheme-dark;*/
/*}*/
/**/
/*.cl-footerActionText,*/
/*.cl-navbarMobileMenuButton,*/
/*.cl-headerTitle,*/
/*.cl-headerSubtitle,*/
/*.cl-profileSectionTitleText,*/
/*.cl-userPreviewMainIdentifier,*/
/*.cl-accordionTriggerButton,*/
/*.cl-internal-of57g,*/
/*.cl-internal-fqx4fd,*/
/*.cl-internal-l1ab9q,*/
/*.cl-breadcrumbsItem,*/
/*.cl-breadcrumbsItemDivider,*/
/*.cl-internal-3vf5mz,*/
/*.cl-formFieldLabel,*/
/*.cl-navbarButton {*/
/*  @apply dark:text-myTheme-light;*/
/*}*/

.cl-footerActionLink {
  @apply text-sky-500;
}
/* hide username change button */
.cl-profileSectionPrimaryButton__username {
  @apply hidden;
}

body {
  overscroll-behavior: none;
}

/* .cl-cardBox, .cl-signIn-start{ */
/*   @apply dark:bg-myTheme-dark dark:text-myTheme-light text-myTheme-dark */
/* } */

/* body { */
/*   touch-action: manipulation; */
/* } */

/* Enhanced active states for sidebar navigation */
.sidebar-nav-active {
  @apply bg-gradient-to-r from-blue-50 to-white text-blue-700 border-l-4 border-blue-500 font-semibold shadow-sm relative;
  box-shadow: inset 0 1px 0 0 rgba(59, 130, 246, 0.1);
}

.sidebar-nav-active::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(59, 130, 246, 0.02) 100%
  );
  border-radius: 0.375rem;
  pointer-events: none;
}

.sidebar-nav-active .lucide {
  @apply text-blue-600;
}

.sidebar-nav-hover:hover {
  @apply bg-gray-50 transition-colors duration-200;
}

.sidebar-nav-hover:hover:not(.sidebar-nav-active) {
  @apply bg-gray-100;
}

/* -------------------------------------------------
   Homepage refresh variables & utilities
   ------------------------------------------------- */
:root {
  /* Dark navy accent for metric bands / footer */
  --myTheme-navy: 0 45 98;
  /* Extremely light slate border for card hairlines */
  --hairline: 229 234 245;
}

/* 1-px neutral border utility */
.border-hairline {
  @apply border border-[color:rgb(var(--hairline))];
}
