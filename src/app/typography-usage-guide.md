# Typography Usage Guide

This guide explains how to use the typography system that has been implemented across the ReviewIt home page. The system is based on the principles defined in `typography-guide.md` and uses Tailwind CSS classes for consistent styling.

## Implementation Overview

The typography system has been implemented across the following components on the home page:

1. **HeroSection** - Main hero headline, subtext, stats
2. **LiveFeedStrip** - Latest products feed with product names and captions
3. **QuickTabs** - Category tabs with titles
4. **EnhancedTopReviews** - Latest reviews with section heading and pagination controls
5. **TopReviewers** - Top reviewers list with header and user info
6. **AdvertCard** - Advertisement cards with title, description, and buttons
7. **CompanyCategories** - Review categories with section header and category cards
8. **ValueProposition** - Value proposition section with headings, benefits, and CTAs

## Responsive Typography Classes

All text elements use responsive classes that adapt to different screen sizes:

- **Mobile First**: Base classes apply to mobile screens
- **Small Screens**: `sm:` prefix classes apply to screens ≥ 640px
- **Medium Screens**: `md:` prefix classes apply to screens ≥ 768px
- **Large Screens**: `lg:` prefix classes apply to screens ≥ 1024px
- **Extra Large Screens**: `xl:` prefix classes apply to screens ≥ 1280px

## Font Weights Usage

The system uses consistent font weights across components:

- **Headings**: `font-extrabold` or `font-black`
- **Subheadings**: `font-bold` or `font-semibold`
- **Body Text**: `font-medium`
- **Captions/Labels**: `font-medium`
- **Buttons**: `font-bold` or `font-medium`

## Implementation Examples

### Headings

```jsx
// Large responsive heading
<h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black text-white leading-tight">
  Your Voice, Your Choice
</h1>

// Medium responsive heading
<h2 className="text-2xl sm:text-3xl lg:text-4xl font-extrabold text-gray-900">
  Latest Reviews
</h2>

// Small responsive heading
<h3 className="text-base sm:text-lg font-semibold text-gray-800">
  Category Name
</h3>
```

### Body Text

```jsx
// Primary body text
<p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
  Join thousands of Guyanese sharing honest reviews...
</p>

// Secondary body text
<p className="text-gray-700 text-sm leading-relaxed line-clamp-3 font-medium">
  Description text...
</p>
```

### Labels and Captions

```jsx
// Stats labels
<span className="text-xs sm:text-sm text-gray-400 font-medium">{stat.label}</span>

// Category counts
<span className="text-xs sm:text-sm text-myTheme-lightTextBody group-hover:text-myTheme-primary/80 transition-colors duration-300">businesses</span>
```

### Buttons

```jsx
// Primary button text
<span className="font-bold text-sm sm:text-base">Get Deal Now!</span>

// Secondary button text
<span className="font-medium text-sm sm:text-base">View All Categories</span>
```

## Best Practices

1. **Consistency**: Always use the established font sizes and weights for similar elements
2. **Responsiveness**: Ensure text elements look good on all screen sizes
3. **Accessibility**: Maintain sufficient color contrast for readability
4. **Performance**: Use `font-medium` instead of `font-normal` when possible for better rendering
5. **Future Expansion**: When adding new components, refer to this guide for consistent typography

## Extending to Other Pages

To implement this typography system on other pages:

1. Review the existing components to understand the patterns
2. Use the same responsive class structures
3. Maintain the same font weight hierarchy
4. Test on multiple screen sizes
5. Update this guide with any new patterns or exceptions

## Color Palette Integration

The typography system integrates with the existing color palette:

- **Headings**: `text-gray-800` or `text-gray-900`
- **Body Text**: `text-gray-600` or `text-gray-700`
- **Labels/Captions**: `text-gray-400` or `text-gray-500`
- **Interactive Elements**: Use theme colors with hover states

## Maintenance

When updating the typography system:

1. Update this guide with any changes
2. Ensure all components are updated consistently
3. Test responsiveness across different devices
4. Verify accessibility standards are maintained
