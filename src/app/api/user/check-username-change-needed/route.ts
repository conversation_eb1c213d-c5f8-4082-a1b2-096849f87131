import { auth, clerkClient } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";

export const dynamic = 'force-dynamic';

/**
 * API endpoint to check if a user needs to change their username
 * Uses the database as the source of truth but also syncs Clerk metadata
 */
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ needsChange: false, error: "Not authenticated" }, { status: 401 });
    }
    
    // Check database value (source of truth)
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId },
      select: { usernameNeedsChange: true, id: true }
    });
    
    if (!user) {
      return NextResponse.json({ 
        needsChange: false, 
        error: "User not found in database" 
      }, { status: 404 });
    }
    
    // Database is source of truth
    const needsChange = <PERSON><PERSON>an(user.usernameNeedsChange);
    
    // Get Clerk user to check metadata
    const clerk = await clerkClient();
    const clerkUser = await clerk.users.getUser(userId);
    const metadataNeedsChange = Boolean(clerkUser.publicMetadata?.usernameNeedsChange);
    
    // If Clerk metadata is wrong, update it
    if (metadataNeedsChange !== needsChange) {
      console.log(`[Username Change Check] Metadata out of sync. DB: ${needsChange}, Clerk: ${metadataNeedsChange}. Updating Clerk.`);
      
      await clerk.users.updateUser(userId, {
        publicMetadata: { 
          ...clerkUser.publicMetadata,
          userInDb: true,
          id: user.id,
          usernameNeedsChange: needsChange 
        }
      });
    }
    
    return NextResponse.json({ 
      needsChange,
      metadataWasOutOfSync: metadataNeedsChange !== needsChange
    });
  } catch (error) {
    console.error("[Username Change Check] Error:", error);
    return NextResponse.json({ 
      needsChange: false, 
      error: "Failed to check username change status" 
    }, { status: 500 });
  }
}
