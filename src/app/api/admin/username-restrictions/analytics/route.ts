/**
 * Admin API endpoint for username restrictions analytics
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import {
  getUsernameRestrictionMetrics,
  getUsernameRestrictionHealthStatus,
  getUsernameRestrictionHistoricalData,
  clearUsernameRestrictionAnalytics
} from '@/app/config/usernameRestrictionsAnalytics';

// Verify admin access
async function verifyAdminAccess(clerkUserId: string): Promise<boolean> {
  try {
    const user = await prisma.user.findFirst({
      where: { clerkUserId },
      select: { role: true }
    });
    
    return user?.role === 'ADMIN';
  } catch (error) {
    console.error('Admin verification error:', error);
    return false;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate request
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify admin access
    const isAdmin = await verifyAdminAccess(userId);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'metrics';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    switch (type) {
      case 'metrics':
        const metrics = await getUsernameRestrictionMetrics();
        return NextResponse.json({
          success: true,
          data: metrics
        });

      case 'health':
        const health = await getUsernameRestrictionHealthStatus();
        return NextResponse.json({
          success: true,
          data: health
        });

      case 'historical':
        if (!startDate || !endDate) {
          return NextResponse.json(
            { error: 'startDate and endDate parameters required for historical data' },
            { status: 400 }
          );
        }
        
        const historical = await getUsernameRestrictionHistoricalData(startDate, endDate);
        return NextResponse.json({
          success: true,
          data: historical
        });

      default:
        return NextResponse.json(
          { error: 'Invalid type parameter. Use: metrics, health, or historical' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('[USERNAME_RESTRICTIONS_ANALYTICS] API error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve analytics data' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate request
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify admin access
    const isAdmin = await verifyAdminAccess(userId);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const olderThanDays = parseInt(searchParams.get('olderThanDays') || '30');

    await clearUsernameRestrictionAnalytics(olderThanDays);

    return NextResponse.json({
      success: true,
      message: `Analytics data older than ${olderThanDays} days has been cleared`
    });

  } catch (error) {
    console.error('[USERNAME_RESTRICTIONS_ANALYTICS] Cleanup error:', error);
    return NextResponse.json(
      { error: 'Failed to clear analytics data' },
      { status: 500 }
    );
  }
}