/**
 * Admin API endpoint for managing username restrictions
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import {
  getUsernameRestrictionStatus,
  getAllRestrictedUsernames,
  reloadUsernameRestrictions,
  validateUsername
} from '@/app/config/usernameRestrictions';

// Verify admin access
async function verifyAdminAccess(clerkUserId: string): Promise<boolean> {
  try {
    const user = await prisma.user.findFirst({
      where: { clerkUserId },
      select: { role: true }
    });
    
    return user?.role === 'ADMIN';
  } catch (error) {
    console.error('Admin verification error:', error);
    return false;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate request
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify admin access
    const isAdmin = await verifyAdminAccess(userId);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'status';

    switch (action) {
      case 'status':
        const status = getUsernameRestrictionStatus();
        return NextResponse.json({
          success: true,
          data: status
        });

      case 'list':
        const restrictedUsernames = getAllRestrictedUsernames();
        return NextResponse.json({
          success: true,
          data: {
            usernames: restrictedUsernames,
            count: restrictedUsernames.length
          }
        });

      case 'validate':
        const username = searchParams.get('username');
        if (!username) {
          return NextResponse.json(
            { error: 'Username parameter required for validation' },
            { status: 400 }
          );
        }

        const validation = validateUsername(username);
        return NextResponse.json({
          success: true,
          data: validation
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: status, list, or validate' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('[USERNAME_RESTRICTIONS_MANAGE] API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate request
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify admin access
    const isAdmin = await verifyAdminAccess(userId);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { action } = await request.json();

    switch (action) {
      case 'reload':
        try {
          reloadUsernameRestrictions();
          const newStatus = getUsernameRestrictionStatus();
          
          return NextResponse.json({
            success: true,
            message: 'Username restrictions configuration reloaded successfully',
            data: newStatus
          });
        } catch (error) {
          console.error('[USERNAME_RESTRICTIONS] Reload error:', error);
          return NextResponse.json(
            { error: 'Failed to reload configuration' },
            { status: 500 }
          );
        }

      case 'health-check':
        const status = getUsernameRestrictionStatus();
        const issues: string[] = [];

        if (!status.isInitialized) {
          issues.push('Service not initialized');
        }

        if (status.totalRestrictions === 0) {
          issues.push('No restrictions loaded - using fallback configuration');
        }

        if (status.categories.length === 0) {
          issues.push('No categories found in configuration');
        }

        return NextResponse.json({
          success: true,
          data: {
            isHealthy: issues.length === 0,
            issues,
            status
          }
        });

      case 'test-validation':
        const { testUsernames } = await request.json();
        
        if (!Array.isArray(testUsernames)) {
          return NextResponse.json(
            { error: 'testUsernames must be an array' },
            { status: 400 }
          );
        }

        const results = testUsernames.map((username: string) => ({
          username,
          validation: validateUsername(username)
        }));

        return NextResponse.json({
          success: true,
          data: {
            results,
            summary: {
              total: results.length,
              restricted: results.filter(r => !r.validation.isValid).length,
              allowed: results.filter(r => r.validation.isValid).length
            }
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: reload, health-check, or test-validation' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('[USERNAME_RESTRICTIONS_MANAGE] POST error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}