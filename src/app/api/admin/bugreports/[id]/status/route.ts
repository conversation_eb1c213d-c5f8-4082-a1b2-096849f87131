import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";
import { createSystemNotification } from "@/app/util/NotificationFunctions";

// Validation schema for status update
const statusUpdateSchema = z.object({
    status: z.enum(["OPEN", "IN_PROGRESS", "RESOLVED", "CLOSED", "WONT_FIX"]),
    resolution_notes: z.string().optional(),
});

export async function PUT(
    req: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        // Check admin authentication
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Get user and check admin role
        const user = await prisma.user.findFirst({
            where: { clerkUserId: userId },
        });

        if (!user || user.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Forbidden: Admin access required" },
                { status: 403 }
            );
        }

        // Validate request body
        const body = await req.json();
        const validatedData = statusUpdateSchema.parse(body);

        // Update bug report status
        const updatedBugReport = await prisma.bugReport.update({
            where: { id: params.id },
            data: {
                status: validatedData.status,
                resolution_notes: validatedData.resolution_notes,
                resolved_at: validatedData.status === "RESOLVED" ? new Date() : null,
                resolved_by: validatedData.status === "RESOLVED" ? user.id : null,
            },
        });

        // Log admin action
        await prisma.adminAction.create({
            data: {
                adminId: user.id,
                actionType: "UPDATE_BUG_STATUS",
                targetId: params.id,
                targetType: "BUG_REPORT",
                description: `Updated bug report status to ${validatedData.status}`,
                bugReportId: params.id,
            },
        });

        // Send system notification to reporter
        try {
            const reporterId = updatedBugReport?.reporterId;
            if (reporterId) {
                await createSystemNotification(
                    [reporterId],
                    "Bug report status updated",
                    `Your bug report has been marked ${validatedData.status} by an admin.`,
                    validatedData.status === "RESOLVED" ? "success" : "info"
                );
            }
        } catch (notifErr) {
            console.error("Failed to send system notification (bug report):", notifErr);
        }

        return NextResponse.json(updatedBugReport);
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json(
                { error: "Invalid request data", details: error.errors },
                { status: 400 }
            );
        }

        console.error("Error updating bug report status:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
} 