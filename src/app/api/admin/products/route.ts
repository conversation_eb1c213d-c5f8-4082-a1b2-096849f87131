import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { Prisma } from '@prisma/client';
import { prisma } from '@/app/util/prismaClient';

export const dynamic = 'force-dynamic';

async function handler(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const search = searchParams.get('search') || '';
        const sortBy = searchParams.get('sortBy') || 'createdDate';
        const sortOrder = searchParams.get('sortOrder') || 'desc';

        // Handle isDeleted filter
        const isDeletedParam = searchParams.get('isDeleted');
        let isDeletedFilter: boolean | undefined;
        if (isDeletedParam === 'true') {
            isDeletedFilter = true;
        } else if (isDeletedParam === 'false') {
            isDeletedFilter = false;
        } else {
            isDeletedFilter = false; // Default to non-deleted products
        }

        const where: Prisma.ProductWhereInput = {
            isDeleted: isDeletedFilter,
            ...(search && {
                OR: [
                    {
                        name: {
                            contains: search,
                            mode: Prisma.QueryMode.insensitive,
                        },
                    },
                    {
                        description: {
                            contains: search,
                            mode: Prisma.QueryMode.insensitive,
                        },
                    },
                    {
                        tags: {
                            has: search,
                        },
                    },
                ],
            }),
            
        };

        const [products, total] = await Promise.all([
            prisma.product.findMany({
                where,
                include: {
                    createdBy: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    business: {
                        select: {
                            id: true,
                            ownerName: true,
                            isVerified: true,
                            subscriptionStatus: true,
                        },
                    },
                    reviews: {
                        where: {
                            isDeleted: false,
                            isPublic: true,
                        },
                        select: {
                            rating: true,
                        },
                    },
                    _count: {
                        select: {
                            reviews: {
                                where: {
                                    isDeleted: false,
                                    isPublic: true,
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    [sortBy]: sortOrder,
                },
                skip: (page - 1) * limit,
                take: limit,
            }),
            prisma.product.count({ where }),
        ]);

        // Calculate real ratings from reviews
        const productsWithRealRatings = products.map(product => {
            const validReviews = product.reviews || [];
            let calculatedRating = 0;
            
            if (validReviews.length > 0) {
                const totalRating = validReviews.reduce((sum, review) => sum + review.rating, 0);
                calculatedRating = totalRating / validReviews.length;
            } else {
                // No reviews yet, show 0 instead of default 3
                calculatedRating = 0;
            }
            
            // Remove reviews from response to keep it clean, but preserve _count
            const { reviews, ...productWithoutReviews } = product;
            
            return {
                ...productWithoutReviews,
                rating: calculatedRating,
            };
        });

        return NextResponse.json({
            success: true,
            data: {
                products: productsWithRealRatings,
                pagination: {
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                },
            },
        });
    } catch (error) {
        console.error('Error fetching products:', error);
        return NextResponse.json(
            { success: false, error: 'Failed to fetch products' },
            { status: 500 }
        );
    }
}

export const GET = withAdminAuth(handler);