import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/app/middleware/adminAuth';
import { prisma } from '@/app/util/prismaClient';
import { invalidateProductCache, invalidateAllProductsCache, invalidateSearchCache } from '@/app/lib/cache';

export const dynamic = 'force-dynamic';

async function handler(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;

        if (request.method === 'GET') {
            const product = await prisma.product.findUnique({
                where: { id },
                include: {
                    createdBy: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    business: {
                        select: {
                            id: true,
                            ownerName: true,
                            isVerified: true,
                            subscriptionStatus: true,
                        },
                    },
                    reviews: {
                        include: {
                            user: {
                                select: {
                                    id: true,
                                    userName: true,
                                    firstName: true,
                                    lastName: true,
                                },
                            },
                        },
                        orderBy: {
                            createdDate: 'desc',
                        },
                    },
                    _count: {
                        select: {
                            reviews: true,
                        },
                    },
                },
            });

            if (!product) {
                return NextResponse.json(
                    { error: 'Product not found' },
                    { status: 404 }
                );
            }

            return NextResponse.json(product);
        }

        if (request.method === 'PATCH') {
            const data = await request.json();
            const {
                name,
                description,
                tags,
                openingHrs,
                closingHrs,
                telephone,
                website,
                isDeleted,
                isPublic,
            } = data;

            const product = await prisma.product.update({
                where: { id },
                data: {
                    name,
                    description,
                    tags,
                    openingHrs,
                    closingHrs,
                    telephone,
                    website,
                    isDeleted,
                    isPublic,
                },
                include: {
                    createdBy: {
                        select: {
                            id: true,
                            userName: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    business: {
                        select: {
                            id: true,
                            ownerName: true,
                            isVerified: true,
                            subscriptionStatus: true,
                        },
                    },
                },
            });

            // Invalidate caches so listings reflect changes
            await invalidateProductCache(id);
            await invalidateAllProductsCache();
            // Also clear product search caches so visibility updates propagate
            await invalidateSearchCache();

            return NextResponse.json(product);
        }

        if (request.method === 'DELETE') {
            await prisma.product.update({
                where: { id },
                data: { isDeleted: true },
            });

            // Invalidate caches
            await invalidateProductCache(id);
            await invalidateAllProductsCache();
            // Clear search caches as product visibility/deletion changed
            await invalidateSearchCache();
            return NextResponse.json({ success: true });
        }

        return NextResponse.json(
            { error: 'Method not allowed' },
            { status: 405 }
        );
    } catch (error) {
        console.error('Error in product route:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

export const GET = withAdminAuth(handler);
export const PATCH = withAdminAuth(handler);
export const DELETE = withAdminAuth(handler); 