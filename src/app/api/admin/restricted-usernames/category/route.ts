import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { reloadUsernameRestrictions } from "@/app/config/usernameRestrictions";

// Path to the restricted usernames JSON file
const restrictedUsernamesPath = path.join(process.cwd(), "src", "app", "config", "restrictedUsernames.json");

// Types for the restricted usernames configuration
interface RestrictedUsernamesConfig {
  version: string;
  lastUpdated: string;
  categories: {
    [categoryName: string]: {
      description: string;
      usernames: string[];
    };
  };
}

// Helper function to read the restricted usernames file
const readRestrictedUsernamesFile = (): RestrictedUsernamesConfig => {
  try {
    const fileContent = fs.readFileSync(restrictedUsernamesPath, "utf-8");
    return JSON.parse(fileContent) as RestrictedUsernamesConfig;
  } catch (error) {
    console.error("Error reading restricted usernames file:", error);
    throw new Error("Failed to read restricted usernames configuration");
  }
};

// Helper function to write to the restricted usernames file
const writeRestrictedUsernamesFile = (data: RestrictedUsernamesConfig): void => {
  try {
    const jsonString = JSON.stringify(data, null, 2);
    fs.writeFileSync(restrictedUsernamesPath, jsonString, "utf-8");
    
    // Reload the username restrictions service to apply changes
    reloadUsernameRestrictions();
  } catch (error) {
    console.error("Error writing restricted usernames file:", error);
    throw new Error("Failed to write restricted usernames configuration");
  }
};

// Check if the user is an admin
const checkAdminPermission = async () => {
  const { userId } = await auth();
  
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  try {
    // Check if user has admin role in database
    const user = await prisma.user.findFirst({
      where: {
        clerkUserId: userId
      },
      select: {
        role: true,
        status: true
      }
    });

    if (!user || user.role !== "ADMIN" || user.status !== "ACTIVE") {
      return NextResponse.json({ error: "Forbidden: Admin access required" }, { status: 403 });
    }
    
    return null; // No error, user is admin
  } catch (error) {
    console.error("Error checking admin permission:", error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
};

// POST: Add a new category
export async function POST(req: NextRequest) {
  // Check admin permission
  const permissionError = await checkAdminPermission();
  if (permissionError) return permissionError;
  
  try {
    const { name, description } = await req.json();
    
    // Validate input
    if (!name || typeof name !== "string") {
      return NextResponse.json(
        { error: "Category name is required and must be a string" },
        { status: 400 }
      );
    }
    
    if (!description || typeof description !== "string") {
      return NextResponse.json(
        { error: "Category description is required and must be a string" },
        { status: 400 }
      );
    }
    
    // Read current data
    const restrictedUsernames = readRestrictedUsernamesFile();
    
    // Check if category already exists
    const normalizedName = name.trim();
    if (restrictedUsernames.categories[normalizedName]) {
      return NextResponse.json(
        { error: `Category '${normalizedName}' already exists` },
        { status: 400 }
      );
    }
    
    // Add new category
    restrictedUsernames.categories[normalizedName] = {
      description: description.trim(),
      usernames: []
    };
    
    // Update lastUpdated timestamp
    restrictedUsernames.lastUpdated = new Date().toISOString().split("T")[0];
    
    // Write updated data back to file
    writeRestrictedUsernamesFile(restrictedUsernames);
    
    return NextResponse.json({ 
      success: true, 
      message: "Category added successfully",
      category: {
        name: normalizedName,
        description: description.trim()
      }
    });
  } catch (error) {
    console.error("Error adding category:", error);
    return NextResponse.json(
      { error: "Failed to add category" },
      { status: 500 }
    );
  }
}

// DELETE: Remove a category
export async function DELETE(req: NextRequest) {
  // Check admin permission
  const permissionError = await checkAdminPermission();
  if (permissionError) return permissionError;
  
  try {
    const { name } = await req.json();
    
    // Validate input
    if (!name || typeof name !== "string") {
      return NextResponse.json(
        { error: "Category name is required and must be a string" },
        { status: 400 }
      );
    }
    
    // Read current data
    const restrictedUsernames = readRestrictedUsernamesFile();
    
    // Check if category exists
    if (!restrictedUsernames.categories[name]) {
      return NextResponse.json(
        { error: `Category '${name}' does not exist` },
        { status: 404 }
      );
    }
    
    // Check if category has usernames
    if (restrictedUsernames.categories[name].usernames.length > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete category '${name}' because it contains ${restrictedUsernames.categories[name].usernames.length} username(s)`,
          count: restrictedUsernames.categories[name].usernames.length
        },
        { status: 400 }
      );
    }
    
    // Delete category
    delete restrictedUsernames.categories[name];
    
    // Update lastUpdated timestamp
    restrictedUsernames.lastUpdated = new Date().toISOString().split("T")[0];
    
    // Write updated data back to file
    writeRestrictedUsernamesFile(restrictedUsernames);
    
    return NextResponse.json({ 
      success: true, 
      message: "Category deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      { error: "Failed to delete category" },
      { status: 500 }
    );
  }
}
