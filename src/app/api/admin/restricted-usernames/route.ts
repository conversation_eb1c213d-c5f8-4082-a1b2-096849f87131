import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { reloadUsernameRestrictions } from "@/app/config/usernameRestrictions";

// Path to the restricted usernames JSON file
const restrictedUsernamesPath = path.join(process.cwd(), "src", "app", "config", "restrictedUsernames.json");

// Types for the restricted usernames configuration
interface RestrictedUsernamesConfig {
  version: string;
  lastUpdated: string;
  categories: {
    [categoryName: string]: {
      description: string;
      usernames: string[];
    };
  };
}

// Helper function to read the restricted usernames file
const readRestrictedUsernamesFile = (): RestrictedUsernamesConfig => {
  try {
    const fileContent = fs.readFileSync(restrictedUsernamesPath, "utf-8");
    return JSON.parse(fileContent) as RestrictedUsernamesConfig;
  } catch (error) {
    console.error("Error reading restricted usernames file:", error);
    throw new Error("Failed to read restricted usernames configuration");
  }
};

// Helper function to write to the restricted usernames file
const writeRestrictedUsernamesFile = (data: RestrictedUsernamesConfig): void => {
  try {
    const jsonString = JSON.stringify(data, null, 2);
    fs.writeFileSync(restrictedUsernamesPath, jsonString, "utf-8");
    
    // Reload the username restrictions service to apply changes
    reloadUsernameRestrictions();
  } catch (error) {
    console.error("Error writing restricted usernames file:", error);
    throw new Error("Failed to write restricted usernames configuration");
  }
};

// Check if the user is an admin
const checkAdminPermission = async () => {
  const { userId } = await auth();
  
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  try {
    // Check if user has admin role in database
    const user = await prisma.user.findFirst({
      where: {
        clerkUserId: userId
      },
      select: {
        role: true,
        status: true
      }
    });

    if (!user || user.role !== "ADMIN" || user.status !== "ACTIVE") {
      return NextResponse.json({ error: "Forbidden: Admin access required" }, { status: 403 });
    }
    
    return null; // No error, user is admin
  } catch (error) {
    console.error("Error checking admin permission:", error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
};

// GET: Fetch all restricted usernames
export async function GET() {
  // Check admin permission
  const permissionError = await checkAdminPermission();
  if (permissionError) return permissionError;
  
  try {
    const restrictedUsernames = readRestrictedUsernamesFile();
    return NextResponse.json(restrictedUsernames);
  } catch (error) {
    console.error("Error fetching restricted usernames:", error);
    return NextResponse.json(
      { error: "Failed to fetch restricted usernames" },
      { status: 500 }
    );
  }
}

// POST: Add a new username to the restricted list
export async function POST(req: NextRequest) {
  // Check admin permission
  const permissionError = await checkAdminPermission();
  if (permissionError) return permissionError;
  
  try {
    const { username, category } = await req.json();
    
    // Validate input
    if (!username || typeof username !== "string") {
      return NextResponse.json(
        { error: "Username is required and must be a string" },
        { status: 400 }
      );
    }
    
    if (!category || typeof category !== "string") {
      return NextResponse.json(
        { error: "Category is required and must be a string" },
        { status: 400 }
      );
    }
    
    // Read current data
    const restrictedUsernames = readRestrictedUsernamesFile();
    
    // Check if category exists
    if (!restrictedUsernames.categories[category]) {
      return NextResponse.json(
        { error: `Category '${category}' does not exist` },
        { status: 400 }
      );
    }
    
    // Check if username already exists in this category
    const normalizedUsername = username.toLowerCase().trim();
    if (restrictedUsernames.categories[category].usernames.includes(normalizedUsername)) {
      return NextResponse.json(
        { error: `Username '${username}' already exists in category '${category}'` },
        { status: 400 }
      );
    }
    
    // Add username to category
    restrictedUsernames.categories[category].usernames.push(normalizedUsername);
    
    // Update lastUpdated timestamp
    restrictedUsernames.lastUpdated = new Date().toISOString().split("T")[0];
    
    // Write updated data back to file
    writeRestrictedUsernamesFile(restrictedUsernames);
    
    return NextResponse.json({ success: true, message: "Username added successfully" });
  } catch (error) {
    console.error("Error adding restricted username:", error);
    return NextResponse.json(
      { error: "Failed to add restricted username" },
      { status: 500 }
    );
  }
}

// DELETE: Remove a username from the restricted list
export async function DELETE(req: NextRequest) {
  // Check admin permission
  const permissionError = await checkAdminPermission();
  if (permissionError) return permissionError;
  
  try {
    const { username, category } = await req.json();
    
    // Validate input
    if (!username || typeof username !== "string") {
      return NextResponse.json(
        { error: "Username is required and must be a string" },
        { status: 400 }
      );
    }
    
    if (!category || typeof category !== "string") {
      return NextResponse.json(
        { error: "Category is required and must be a string" },
        { status: 400 }
      );
    }
    
    // Read current data
    const restrictedUsernames = readRestrictedUsernamesFile();
    
    // Check if category exists
    if (!restrictedUsernames.categories[category]) {
      return NextResponse.json(
        { error: `Category '${category}' does not exist` },
        { status: 400 }
      );
    }
    
    // Check if username exists in this category
    const normalizedUsername = username.toLowerCase().trim();
    const index = restrictedUsernames.categories[category].usernames.indexOf(normalizedUsername);
    
    if (index === -1) {
      return NextResponse.json(
        { error: `Username '${username}' not found in category '${category}'` },
        { status: 404 }
      );
    }
    
    // Remove username from category
    restrictedUsernames.categories[category].usernames.splice(index, 1);
    
    // Update lastUpdated timestamp
    restrictedUsernames.lastUpdated = new Date().toISOString().split("T")[0];
    
    // Write updated data back to file
    writeRestrictedUsernamesFile(restrictedUsernames);
    
    return NextResponse.json({ success: true, message: "Username removed successfully" });
  } catch (error) {
    console.error("Error removing restricted username:", error);
    return NextResponse.json(
      { error: "Failed to remove restricted username" },
      { status: 500 }
    );
  }
}
