import { NextRequest, NextResponse } from 'next/server';
import { redisService } from '@/app/lib/redis';
import { auth } from '@clerk/nextjs/server';
import { getCacheStats } from '@/app/lib/cache';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin privileges
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'stats';
    const pattern = searchParams.get('pattern') || '*';
    const key = searchParams.get('key') || '';

    switch (action) {
      case 'stats':
        // Return cache statistics
        const stats = getCacheStats();
        const health = await redisService.isHealthy();
        
        // Get Redis info
        let redisInfo = {};
        try {
          const client = redisService.getClient();
          redisInfo = await client.info();
        } catch (error) {
          console.error('Error getting Redis info:', error);
        }
        
        return NextResponse.json({
          success: true,
          data: {
            stats,
            health,
            redisInfo
          }
        });

      case 'keys':
        // Return keys matching pattern
        const keys = await redisService.keys(pattern);
        return NextResponse.json({
          success: true,
          data: {
            keys,
            count: keys.length
          }
        });

      case 'key-value':
        // Return value for specific key
        if (!key) {
          return NextResponse.json(
            { success: false, message: 'Key parameter is required' },
            { status: 400 }
          );
        }
        
        const value = await redisService.get(key);
        let parsedValue;
        try {
          parsedValue = value ? JSON.parse(value) : null;
        } catch (e) {
          parsedValue = value;
        }
        
        return NextResponse.json({
          success: true,
          data: {
            key,
            value: parsedValue,
            rawValue: value
          }
        });

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error inspecting cache:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
