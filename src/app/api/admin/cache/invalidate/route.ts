import { NextRequest, NextResponse } from 'next/server';
import {
  invalidateAllProductsCache,
  invalidateSearchCache,
  invalidateAdminCache,
  invalidateReviewCaches,
  invalidateAllCaches,
  invalidateBusinessCaches,
  batchInvalidateCache,
} from '@/app/lib/cache';
import { auth } from '@clerk/nextjs/server';

/**
 * POST /api/admin/cache/invalidate
 *
 * Body: { action: string }
 *  Supported actions:
 *  - "invalidate-all-products"
 *  - "invalidate-search"
 *  - "invalidate-admin"
 *  - "invalidate-reviews"
 *  - "invalidate-all" (clears entire Redis cache)
 *
 * Admin-only. Each action delegates to existing cache-invalidation utilities.
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin privileges via Clerk
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }

    const { action } = await request.json();

    switch (action) {
      case 'invalidate-all-products':
        await invalidateAllProductsCache();
        return NextResponse.json({ success: true, message: 'All products cache invalidated' });

      case 'invalidate-search':
        await invalidateSearchCache();
        return NextResponse.json({ success: true, message: 'Search cache invalidated' });

      case 'invalidate-admin':
        await invalidateAdminCache();
        return NextResponse.json({ success: true, message: 'Admin cache invalidated' });

      case 'invalidate-reviews':
        await invalidateReviewCaches();
        return NextResponse.json({ success: true, message: 'Review caches invalidated' });

      case 'invalidate-all':
        await invalidateAllCaches();
        return NextResponse.json({ success: true, message: 'All caches invalidated' });

      default:
        return NextResponse.json({ success: false, message: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in cache invalidation route:', error);
    return NextResponse.json({ success: false, message: 'Internal server error' }, { status: 500 });
  }
}
