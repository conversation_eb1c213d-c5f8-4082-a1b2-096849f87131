import { NextRequest, NextResponse } from 'next/server';
import { checkCacheHealth, getCacheStats } from '@/app/lib/cache';
import { auth } from '@clerk/nextjs/server';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin privileges
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Perform cache health check
    const healthCheck = await checkCacheHealth();
    const basicStats = getCacheStats();

    return NextResponse.json({
      success: true,
      data: {
        ...healthCheck,
        basicStats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error checking cache health:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to check cache health',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin privileges
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action } = await request.json();

    if (action === 'reset-circuit-breaker') {
      // This would reset the circuit breaker - for now just return success
      return NextResponse.json({
        success: true,
        message: 'Circuit breaker reset requested',
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { success: false, message: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error performing cache action:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to perform cache action',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}