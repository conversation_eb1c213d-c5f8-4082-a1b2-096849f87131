import { NextRequest, NextResponse } from 'next/server';
import { 
  safeGetFromCache, 
  safeSetToCache, 
  invalidateAllProductsCache,
  checkCacheHealth,
  batchInvalidateCache
} from '@/app/lib/cache';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin privileges
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action } = await request.json();

    switch (action) {
      case 'test-cache-operations':
        // Test basic cache operations
        const testKey = `test_cache_${Date.now()}`;
        const testValue = { message: 'Cache test successful', timestamp: new Date().toISOString() };
        
        // Test set
        const setResult = await safeSetToCache(testKey, testValue, 60);
        if (!setResult) {
          return NextResponse.json({
            success: false,
            message: 'Failed to set cache value'
          });
        }
        
        // Test get
        const getValue = await safeGetFromCache<{ message: string; timestamp: string }>(testKey);
        if (!getValue || getValue.message !== testValue.message) {
          return NextResponse.json({
            success: false,
            message: 'Failed to retrieve cache value'
          });
        }
        
        return NextResponse.json({
          success: true,
          message: 'Cache operations test passed',
          data: { setValue: testValue, getValue }
        });

      case 'test-invalidation':
        // Test cache invalidation
        await invalidateAllProductsCache();
        return NextResponse.json({
          success: true,
          message: 'Cache invalidation test completed'
        });

      case 'test-batch-invalidation':
        // Test batch invalidation
        const testKeys = [
          `test_batch_1_${Date.now()}`,
          `test_batch_2_${Date.now()}`,
          `test_batch_3_${Date.now()}`
        ];
        
        // Set some test values
        for (const key of testKeys) {
          await safeSetToCache(key, { test: true }, 60);
        }
        
        // Batch invalidate
        await batchInvalidateCache(testKeys);
        
        return NextResponse.json({
          success: true,
          message: 'Batch invalidation test completed',
          data: { keysInvalidated: testKeys.length }
        });

      case 'health-check':
        // Test cache health
        const healthResult = await checkCacheHealth();
        return NextResponse.json({
          success: true,
          message: 'Cache health check completed',
          data: healthResult
        });

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid test action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error running cache test:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Cache test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}