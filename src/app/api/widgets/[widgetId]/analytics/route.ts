import { NextRequest, NextResponse } from 'next/server';
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // Default to 30 days
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            id: true,
            ownerId: true,
            ownerName: true,
            isVerified: true
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            display_image: true
          }
        },
        analytics: true
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    // Verify ownership
    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Calculate date range
    let periodStartDate: Date;
    let periodEndDate: Date = new Date();

    if (startDate && endDate) {
      periodStartDate = new Date(startDate);
      periodEndDate = new Date(endDate);
    } else {
      const days = parseInt(period);
      periodStartDate = new Date();
      periodStartDate.setDate(periodStartDate.getDate() - days);
    }

    // Process analytics data
    const analytics = widget.analytics;
    let processedAnalytics = null;

    if (analytics) {
      const dailyViews = analytics.dailyViews as Record<string, number>;
      const dailyClicks = analytics.dailyClicks as Record<string, number>;
      const topReferrers = analytics.topReferrers as Record<string, number>;

      // Filter data by date range
      const filteredDailyStats: Array<{ date: string; views: number; clicks: number }> = [];
      let totalViews = 0;
      let totalClicks = 0;

      const currentDate = new Date(periodStartDate);
      while (currentDate <= periodEndDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const views = dailyViews[dateStr] || 0;
        const clicks = dailyClicks[dateStr] || 0;
        
        filteredDailyStats.push({
          date: dateStr,
          views,
          clicks
        });
        
        totalViews += views;
        totalClicks += clicks;
        
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Process top referrers
      const processedReferrers = Object.entries(topReferrers)
        .map(([domain, views]) => ({
          domain,
          views: views as number,
          percentage: totalViews > 0 ? Math.round((views as number / totalViews) * 100) : 0
        }))
        .sort((a, b) => b.views - a.views)
        .slice(0, 10);

      processedAnalytics = {
        totalViews,
        totalClicks,
        clickThroughRate: totalViews > 0 ? Math.round((totalClicks / totalViews) * 100 * 100) / 100 : 0,
        topReferrers: processedReferrers,
        dailyStats: filteredDailyStats,
        averageLoadTime: analytics.averageLoadTime,
        errorCount: analytics.errorCount
      };
    }

    const response = {
      widget: {
        id: widget.id,
        name: widget.name,
        type: widget.type,
        isActive: widget.isActive,
        createdAt: widget.createdAt,
        lastUsed: widget.lastUsed,
        viewCount: widget.viewCount,
        clickCount: widget.clickCount,
        business: widget.business,
        product: widget.product
      },
      analytics: processedAnalytics,
      period: {
        startDate: periodStartDate,
        endDate: periodEndDate
      },
      metrics: processedAnalytics
    };

    return NextResponse.json({
      success: true,
      status: 200,
      data: response
    });

  } catch (error) {
    console.error('Widget analytics error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}