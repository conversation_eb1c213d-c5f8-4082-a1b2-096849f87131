import { NextRequest, NextResponse } from 'next/server';
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { DomainVerificationRequest, DomainVerificationResponse, VerificationMethod } from "@/app/util/Interfaces";
import { $Enums } from '@prisma/client';
import crypto from 'crypto';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// POST /api/widgets/[widgetId]/verify-domain - Initiate domain verification
export async function POST(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    const body: DomainVerificationRequest = await request.json();
    const { domain, method } = body;

    // Validate input
    if (!domain || !method) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Domain and method are required'
      });
    }

    // Validate domain format
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
    if (!domainRegex.test(domain)) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Invalid domain format'
      });
    }

    // Validate method
    if (!['HTML_FILE', 'DNS_TXT', 'META_TAG'].includes(method)) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Invalid verification method'
      });
    }

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Check if widget is secure
    if (widget.securityLevel !== 'SECURE') {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Domain verification is only available for secure widgets'
      });
    }

    // Generate verification code
    const verificationCode = crypto.randomBytes(16).toString('hex');
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Create or update domain verification
    const domainVerification = await prisma.domainVerification.upsert({
      where: {
        widgetId_domain: {
          widgetId: params.widgetId,
          domain: domain
        }
      },
      create: {
        widgetId: params.widgetId,
        domain: domain,
        verificationCode: verificationCode,
        method: method as $Enums.VerificationMethod,
        expiresAt: expiresAt
      },
      update: {
        verificationCode: verificationCode,
        method: method as $Enums.VerificationMethod,
        expiresAt: expiresAt,
        isVerified: false,
        verifiedAt: null
      }
    });

    // Generate instructions based on method
    const instructions: any = {};

    switch (method) {
      case 'HTML_FILE':
        const fileName = `reviewit-verification-${verificationCode}.html`;
        const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <title>ReviewIt Domain Verification</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>ReviewIt Domain Verification</h1>
    <p><strong>Verification Code:</strong> ${verificationCode}</p>
    <p><strong>Widget ID:</strong> ${params.widgetId}</p>
    <p><strong>Domain:</strong> ${domain}</p>
    <p><strong>Generated:</strong> ${new Date().toISOString()}</p>
    <p>This file verifies ownership of ${domain} for ReviewIt widget embedding.</p>
</body>
</html>`;

        instructions.htmlFile = {
          fileName: fileName,
          content: htmlContent,
          uploadPath: `https://${domain}/${fileName}`
        };
        break;

      case 'DNS_TXT':
        instructions.dnsRecord = {
          type: 'TXT',
          name: 'reviewit-verification',
          value: verificationCode
        };
        break;

      case 'META_TAG':
        instructions.metaTag = {
          tag: `<meta name="reviewit-verification" content="${verificationCode}" />`,
          placement: 'Add this meta tag to the <head> section of your website\'s homepage'
        };
        break;
    }

    const response: DomainVerificationResponse = {
      success: true,
      data: {
        verificationCode: verificationCode,
        method: method,
        domain: domain,
        expiresAt: expiresAt,
        instructions: instructions
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Domain verification initiation error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}

// GET /api/widgets/[widgetId]/verify-domain - Get verification status
export async function GET(
  request: NextRequest,
  { params }: { params: { widgetId: string } }
) {
  try {
    const { sessionClaims } = getAuth(request as any);
    const clerkClaimsData = sessionClaims as any;

    if (!clerkClaimsData?.userId) {
      return NextResponse.json({
        success: false,
        status: 401,
        error: 'Unauthorized'
      });
    }

    // Ensure user exists in database
    if (!(await userInDb(clerkClaimsData.userId))) {
      await addUserToDb(clerkClaimsData);
    }

    // Get user's database ID from Clerk metadata
    const clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
    const userId = clerkUserData.publicMetadata.id as string;

    // Get domain from query params
    const url = new URL(request.url);
    const domain = url.searchParams.get('domain');

    if (!domain) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Domain parameter is required'
      });
    }

    // Get widget and verify ownership
    const widget = await prisma.widget.findUnique({
      where: { id: params.widgetId },
      include: {
        business: {
          select: {
            ownerId: true
          }
        }
      }
    });

    if (!widget) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Widget not found'
      });
    }

    if (widget.business.ownerId !== userId) {
      return NextResponse.json({
        success: false,
        status: 403,
        error: 'Access denied'
      });
    }

    // Get domain verification status
    const domainVerification = await prisma.domainVerification.findUnique({
      where: {
        widgetId_domain: {
          widgetId: params.widgetId,
          domain: domain
        }
      }
    });

    if (!domainVerification) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Domain verification not found'
      });
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: {
        domain: domainVerification.domain,
        isVerified: domainVerification.isVerified,
        verifiedAt: domainVerification.verifiedAt,
        method: domainVerification.method,
        expiresAt: domainVerification.expiresAt,
        isExpired: domainVerification.expiresAt < new Date()
      }
    });

  } catch (error) {
    console.error('Get domain verification status error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: 'Internal server error'
    });
  }
}