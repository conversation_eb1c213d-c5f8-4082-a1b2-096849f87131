import { NextRequest, NextResponse } from 'next/server';
import { getAdminDashboardMetricsFromCache } from "@/app/util/databaseAnalytics";
import { checkRateLimit } from '@/app/util/rateLimiting';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    // Rate limiting - 50 requests per minute
    if (!checkRateLimit(request, 50, 60000)) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      );
    }
    
    // Get metrics data from cache (no authentication required for public metrics)
    const result = await getAdminDashboardMetricsFromCache();
    
    // Return only the data portion without requiring admin access
    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          totalUsers: result.data.totalUsers,
          totalReviews: result.data.totalReviews,
          totalProducts: result.data.totalProducts
        }
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to fetch metrics' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error fetching site metrics:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
