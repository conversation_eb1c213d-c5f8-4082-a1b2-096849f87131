import { prisma } from "@/app/util/prismaClient";
import { isValidUsername } from "@/app/util/userHelpers";
import { validateUsername } from "@/app/config/usernameRestrictions";
import { differenceInDays } from "date-fns";
import { redisService } from "@/app/lib/redis";
import { CacheKeys } from "@/app/lib/cache";
import { NextRequest, NextResponse } from "next/server";
import { iUser } from "@/app/util/Interfaces";
import { Prisma } from "@prisma/client";
import { clerkClient } from "@clerk/nextjs/server";
import { auth, getAuth } from "@clerk/nextjs/server";
import { invalidateSearchCache } from "@/app/lib/cache";

// Helper function to convert iUser partial to Prisma UserUpdateInput
function convertToPrismaUserUpdateInput(
  partialUser: Partial<iUser>,
): Prisma.UserUpdateInput {
  const allowedFields: (keyof iUser)[] = [
    "bio",
    "userName",
    "avatar",

    "email",
    "firstName",
    "lastName",
  ];

  const updateInput: Prisma.UserUpdateInput = {};

  for (const field of allowedFields) {
    if (field in partialUser) {
      switch (field) {
        case "bio":
        case "userName":
        case "avatar":
        // case "email":
        case "firstName":
        case "lastName":
          updateInput[field] = partialUser[field] as string;
          break;
      }
    }
  }

  return updateInput;
}

// Removed unused helper function

export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } },
) {
  const userId = params.userId;
  const updatedFields = (await request.json()) as Partial<iUser>;
  const MIN_DAYS_BETWEEN_CHANGES = 30;

  try {
    // Try multiple auth methods to ensure we get the user ID
    console.log("Attempting authentication for request to update user", userId);
    
    // Method 1: Standard auth()
    let clerkUserId = (await auth()).userId;
    console.log("Auth method 1 result:", clerkUserId);
    
    // Method 2: Try getAuth with request
    if (!clerkUserId) {
      try {
        const authData = getAuth(request as any);
        clerkUserId = authData.userId;
        console.log("Auth method 2 result:", clerkUserId);
      } catch (e) {
        console.error("Error in getAuth:", e);
      }
    }
    
    // Method 3: Try parsing Authorization header manually
    if (!clerkUserId) {
      const authHeader = request.headers.get("Authorization");
      console.log("Authorization header:", authHeader);
      // We'd need to validate the token here, but just logging for now
    }

    if (!clerkUserId) {
      console.log("All auth methods failed, returning 401");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Special case for username changes - check if user has usernameNeedsChange flag
    let targetUser;
    // Initialize with userId as fallback to ensure it's always defined
    let effectiveUserId: string = userId;
    let bypassNormalAuth = false;
    
    if (updatedFields.userName) {
      // First try to find the user by ID
      const userById = await prisma.user.findUnique({
        where: { id: userId },
        select: { usernameNeedsChange: true, id: true, clerkUserId: true },
      });
      
      // If found and has usernameNeedsChange flag, allow the update
      if (userById?.usernameNeedsChange === true) {
        console.log("User has usernameNeedsChange flag set, allowing update regardless of Clerk ID");
        
        // Allow this user to update their username regardless of Clerk ID mismatch
        targetUser = userById;
        effectiveUserId = targetUser.id;
        bypassNormalAuth = true;
      } else {
        // Also check if any user with usernameNeedsChange has the current clerkUserId
        // This handles the case where Clerk assigned a new ID during signup
        const userByClerkId = await prisma.user.findFirst({
          where: { 
            clerkUserId: clerkUserId,
            usernameNeedsChange: true 
          },
          select: { id: true, clerkUserId: true, usernameNeedsChange: true },
        });
        
        if (userByClerkId) {
          console.log("Found user with current clerkUserId that needs username change");
          targetUser = userByClerkId;
          effectiveUserId = targetUser.id;
          bypassNormalAuth = true;
        }
      }
    }
    
    // If not bypassing normal auth, do standard auth flow
    if (!bypassNormalAuth) {
      console.log("Using standard auth flow");
      console.log("Looking up user with userId:", userId);
      console.log("Or with clerkUserId:", clerkUserId);
      
      targetUser = await prisma.user.findFirst({
        where: {
          OR: [
            { id: userId },
            { clerkUserId: userId },
          ],
        },
        select: { id: true, clerkUserId: true },
      });
      
      console.log("Database lookup result:", targetUser ? 
        { id: targetUser.id, clerkUserId: targetUser.clerkUserId } : 
        "User not found");

      if (!targetUser) {
        console.log("User not found in database");
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }
      
      if (targetUser.clerkUserId !== clerkUserId) {
        console.log("User found but clerkUserId mismatch", { 
          dbClerkUserId: targetUser.clerkUserId, 
          requestClerkUserId: clerkUserId 
        });
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }
      
      effectiveUserId = targetUser.id;
    }
    
    console.log("User authorized successfully");

    // When userName change requested, validate and rate-limit
    if (updatedFields.userName) {
      // Validate format & reserved words
      if (!isValidUsername(updatedFields.userName)) {
        return NextResponse.json(
          { error: "Invalid username" },
          { status: 400 },
        );
      }

      // Check comprehensive username restrictions
      const usernameValidation = validateUsername(updatedFields.userName);
      if (!usernameValidation.isValid) {
        return NextResponse.json(
          { error: usernameValidation.reason || "This username is not available. Please choose another." },
          { status: 400 },
        );
      }

      // Check if username is already taken by another user
      const existingUserWithUsername = await prisma.user.findFirst({
        where: {
          userName: {
            equals: updatedFields.userName,
            mode: 'insensitive'
          },
          id: { not: userId }, // Exclude current user
          isDeleted: false
        },
        select: { id: true }
      });

      if (existingUserWithUsername) {
        return NextResponse.json(
          { error: "This username is already taken" },
          { status: 400 },
        );
      }

      // Fetch current user to check last change timestamp
      // @ts-ignore - the field usernameChangedAt will exist after migration
      const existing = await prisma.user.findUnique({
        where: { id: effectiveUserId },
        select: { userName: true, usernameChangedAt: true, usernameNeedsChange: true },
      });

      if (!existing) {
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Skip rate limit check if username change is required
      const needsChange = Boolean((existing as any).usernameNeedsChange);
      console.log("User needs username change:", needsChange);
      
      // Only enforce rate limit if this is NOT a required username change
      if (
        !needsChange &&
        (existing as any).usernameChangedAt &&
        differenceInDays(new Date(), (existing as any).usernameChangedAt || new Date()) < MIN_DAYS_BETWEEN_CHANGES
      ) {
        return NextResponse.json(
          {
            error: `Username can only be changed every ${MIN_DAYS_BETWEEN_CHANGES} days.`,
          },
          { status: 429 },
        );
      }
    }

    // Update user in your database
    const prismaUpdateInput = convertToPrismaUserUpdateInput(updatedFields);
    if (updatedFields.userName) {
      // also stamp the change time
      (prismaUpdateInput as any).usernameChangedAt = new Date();
      (prismaUpdateInput as any).usernameNeedsChange = false;
    }
    const updatedUser = await prisma.user.update({
      where: {
        id: effectiveUserId,
      },
      data: prismaUpdateInput,
    });

    // Prepare data for Clerk update
    const clerkUpdateData: any = {};
    if (updatedFields.firstName)
      clerkUpdateData.firstName = updatedFields.firstName;
    if (updatedFields.lastName)
      clerkUpdateData.lastName = updatedFields.lastName;
    if (updatedFields.userName)
      clerkUpdateData.username = updatedFields.userName;
    // if (updatedFields.avatar) clerkUpdateData.imageUrl = updatedFields.avatar;

    // Update Clerk user data if there are fields to update
    // Ensure we clear the usernameNeedsChange flag in Clerk metadata if username was updated
    if (updatedFields.userName) {
      // Get current user to preserve existing metadata
      const currentClerkUser = await (await clerkClient()).users.getUser(clerkUserId);
      clerkUpdateData.publicMetadata = {
        ...currentClerkUser.publicMetadata,
        usernameNeedsChange: false
      };
    }

      if (Object.keys(clerkUpdateData).length > 0) {
      const res = await (await clerkClient()).users.updateUser(
        clerkUserId,
        clerkUpdateData,
      );
    }

    // Invalidate relevant caches after successful user update
    // This ensures all user-dependent cached data is refreshed
    // Each cache invalidation is wrapped in its own try/catch to ensure failures don't block other invalidations
    
    // Function to safely execute Redis operations without crashing
    const safeRedisOp = async (operation: () => Promise<any>, description: string) => {
      try {
        await operation();
        return true;
      } catch (err) {
        console.error(`[CACHE] Error during ${description}:`, err);
        return false;
      }
    };
    
    // Safely invalidate search cache
    await safeRedisOp(
      async () => await invalidateSearchCache(),
      'search cache invalidation'
    );
    
    // Safely invalidate top reviewers cache
    await safeRedisOp(
      async () => await redisService.del(CacheKeys.topReviewers(6)),
      'top reviewers cache invalidation'
    );
    
    // Safely invalidate user-specific caches
    await safeRedisOp(async () => {
      try {
        const keys = await redisService.keys(`reviewit:*:user:${effectiveUserId}*`);
        if (keys && keys.length > 0) {
          await Promise.all(keys.map(key => 
            safeRedisOp(
              async () => await redisService.del(key),
              `deletion of key ${key}`
            )
          ));
        }
      } catch {}
    }, 'user-specific cache invalidation');
    
    // Safely invalidate user profile caches
    await safeRedisOp(async () => {
      try {
        const keys = await redisService.keys(`reviewit:*:user_profile:*`);
        if (keys && keys.length > 0) {
          await Promise.all(keys.map(key => 
            safeRedisOp(
              async () => await redisService.del(key),
              `deletion of key ${key}`
            )
          ));
        }
      } catch {}
    }, 'user profile cache invalidation');
    
    // Safely invalidate review caches
    await safeRedisOp(async () => {
      try {
        const keys = await redisService.keys(`reviewit:*:reviews:*`);
        if (keys && keys.length > 0) {
          await Promise.all(keys.map(key => 
            safeRedisOp(
              async () => await redisService.del(key),
              `deletion of key ${key}`
            )
          ));
        }
      } catch {}
    }, 'review cache invalidation');
    
    // Safely invalidate popular/trending/latest reviews
    await safeRedisOp(
      async () => {
        await redisService.del(CacheKeys.popularReviews()).catch(() => {});
        await redisService.del(CacheKeys.trendingReviews()).catch(() => {});
        await redisService.del(CacheKeys.latestReviews()).catch(() => {});
      },
      'popular/trending/latest reviews cache invalidation'
    );
    
    console.log(`[CACHE] Completed Redis cache invalidation for user ${effectiveUserId} after username change`);

    return NextResponse.json({
      success: true,
      status: 200,
      data: updatedUser,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 },
    );
  } finally {
    await prisma.$disconnect();
  }
}
