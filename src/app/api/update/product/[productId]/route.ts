import { prisma } from "@/app/util/prismaClient";
import type { Prisma } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { iProduct } from "@/app/util/Interfaces";
import { auth } from "@clerk/nextjs/server";
import { invalidateProductCache, invalidateSearchCache, invalidateAllProductsCache } from "@/app/lib/cache";

// Helper function to convert iProduct partial to Prisma ProductUpdateInput
function convertToPrismaProductUpdateInput(
  partialProduct: Partial<iProduct>,
): Prisma.ProductUpdateInput {
  const allowedFields = [
    "businessHours",
    "name",
    "description",
    "display_image",
    "images",
    "videos",
    "links",
    "tags",
    "openingHrs",
    "closingHrs",
    "openingDays",
    "address",
    "streetAddress",
    "city",
    "latitude",
    "longitude",
    "telephone",
    "website",
    "email",
  ];

  const updateInput: Prisma.ProductUpdateInput = {};

  for (const field of allowedFields) {
    if (field in partialProduct) {
      switch (field) {
        case "address":
        case "streetAddress":
        case "city":
        case "description":
        case "display_image":
        case "name":
        case "openingHrs":
        case "closingHrs":
        case "telephone":
        case "email":
          updateInput[field] = partialProduct[field] as string;
          break;
        case "images":
        case "videos":
        case "links":
        case "tags":
        case "website":
        case "openingDays":
          updateInput[field] = partialProduct[field] as string[];
          break;
        case "businessHours":
          updateInput[field] = partialProduct[field] as unknown as Prisma.InputJsonValue;
          break;
        case "rating":
        case "latitude":
        case "longitude":
          updateInput[field] = partialProduct[field] as number;
          break;
      }
    }
  }

  return updateInput;
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { productId: string } },
) {
  const productId = params.productId;
  const updatedFields = (await request.json()) as Partial<iProduct>;

  // Normalize URLs in arrays if they exist
  if (updatedFields.images) {
    updatedFields.images = updatedFields.images.map((url) => url.trim());
  }
  if (updatedFields.videos) {
    updatedFields.videos = updatedFields.videos.map((url) => url.trim());
  }
  if (updatedFields.links) {
    updatedFields.links = updatedFields.links.map((url) => url.trim());
  }
  if (updatedFields.website) {
    updatedFields.website = updatedFields.website.map((url) => url.trim());
  }
  if (updatedFields.tags) {
    updatedFields.tags = updatedFields.tags.map((tag) => tag.trim().toLowerCase());
  }

  try {
    // Authenticate the request
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Update product in your database
    const prismaUpdateInput = convertToPrismaProductUpdateInput(updatedFields);

    if (updatedFields.description) {
      prismaUpdateInput.descriptionLastUpdatedAt = new Date();
    }

    if (updatedFields.images) {
      prismaUpdateInput.imagesLastUpdatedAt = new Date();
    }

    const updatedProduct = await prisma.product.update({
      where: {
        id: productId,
      },
      data: prismaUpdateInput,
    });

    // Invalidate relevant caches after successful product update
    try {
      await invalidateProductCache(productId);
      await invalidateSearchCache();
      await invalidateAllProductsCache();
    } catch (cacheError) {
      // Don't fail the request if cache invalidation fails
    }

    return NextResponse.json({
      success: true,
      status: 200,
      data: updatedProduct,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to update product" },
      { status: 500 },
    );
  } finally {
    await prisma.$disconnect();
  }
}
