import { prisma } from "@/app/util/prismaClient";
import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { invalidateAggregatedCachesOnReviewChange } from "@/app/lib/cache";

export async function POST(request: NextRequest) {
  // Removed console.log statement
  
  interface Body {
    reviewId: string;
    isApproved: boolean;
  }

  const body: Body = await request.json();
  
  try {
    // Get the current user (admin)
    const { userId } = getAuth(request);
    
    if (!userId) {
      return NextResponse.json({ 
        success: false, 
        status: 401, 
        message: "Unauthorized" 
      });
    }

    const existingReview = await prisma.review.findUnique({
      where: { id: body.reviewId },
    });

    if (!existingReview) {
      console.error(`Review with ID ${body.reviewId} not found.`);
      return NextResponse.json({ 
        success: false, 
        status: 404, 
        message: "Review not found" 
      });
    }

    const updatedReview = await prisma.review.update({
      where: { id: body.reviewId },
      data: {
        isPublic: body.isApproved,
        isVerified: body.isApproved,
        verifiedBy: userId,
      },
    });

    // Invalidate admin caches after review approval
    await invalidateAggregatedCachesOnReviewChange();

    return NextResponse.json({
      success: true,
      status: 200,
      data: updatedReview,
    });
  } catch (error) {
    console.error(`Error updating review: ${error}`);
    return NextResponse.json({ 
      success: false, 
      status: 500, 
      message: "Internal server error" 
    });
  } finally {
    await prisma.$disconnect();
  }
}