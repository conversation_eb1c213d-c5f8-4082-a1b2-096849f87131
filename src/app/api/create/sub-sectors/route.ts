import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';

export async function POST(request: NextRequest) {
  const client = new Client({
    connectionString: process.env.OVERFLOW_DATA_DATABASE_URL,
  });

  try {
    const body = await request.json();
    const { sectorId, subSector } = body;

    // Validate required fields
    if (!sectorId || !subSector) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Missing required fields: sectorId and subSector are required'
      });
    }

    await client.connect();
    
    // First, check if the sector exists
    const sectorCheckQuery = `
      SELECT id, sector, sub_sectors 
      FROM sector 
      WHERE id = $1;
    `;
    
    const sectorResult = await client.query(sectorCheckQuery, [sectorId]);
    
    if (sectorResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        status: 404,
        error: 'Sector not found'
      });
    }

    const existingSector = sectorResult.rows[0];
    const currentSubSectors = existingSector.sub_sectors || [];
    
    // Check if sub-sector already exists
    if (currentSubSectors.includes(subSector)) {
      return NextResponse.json({
        success: false,
        status: 409,
        error: 'Sub-sector already exists in this sector'
      });
    }

    // Add the new sub-sector to the array
    const updatedSubSectors = [...currentSubSectors, subSector];
    
    // Update the sector with the new sub-sector
    const updateQuery = `
      UPDATE sector 
      SET sub_sectors = $1 
      WHERE id = $2
      RETURNING id, sector, sub_sectors;
    `;
    
    const updateResult = await client.query(updateQuery, [updatedSubSectors, sectorId]);
    
    return NextResponse.json({
      success: true,
      status: 200,
      data: {
        id: updateResult.rows[0].id,
        sector: updateResult.rows[0].sector,
        sub_sectors: updateResult.rows[0].sub_sectors,
        message: `Sub-sector "${subSector}" added successfully`
      }
    });
    
  } catch (error) {
    console.error('Database connection error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: `Database operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  } finally {
    await client.end();
  }
}