import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';
import { SuggestionBox } from '@/app/util/Interfaces';

export async function POST(request: NextRequest) {
  const client = new Client({
    connectionString: process.env.OVERFLOW_DATA_DATABASE_URL,
  });

  try {
    const body = await request.json();
    const { description, user_email, user_name, ip_address } = body;

    // Validate required fields
    if (!description || !ip_address) {
      return NextResponse.json({
        success: false,
        status: 400,
        error: 'Missing required fields: description and ip_address are required'
      });
    }

    await client.connect();
    
    // Insert the new suggestion into the suggestion_box table
    const insertQuery = `
      INSERT INTO suggestion_box (description, user_email, user_name, ip_address)
      VALUES ($1, $2, $3, $4)
      RETURNING id, description, user_email, user_name, ip_address;
    `;
    
    const result = await client.query(insertQuery, [
      description,
      user_email || null,
      user_name || null,
      ip_address
    ]);
    
    const newSuggestion: SuggestionBox = {
      id: result.rows[0].id,
      description: result.rows[0].description,
      user_email: result.rows[0].user_email,
      user_name: result.rows[0].user_name,
      ip_address: result.rows[0].ip_address
    };
    
    return NextResponse.json({
      success: true,
      status: 201,
      data: {
        suggestion: newSuggestion,
        message: 'Suggestion created successfully'
      }
    });
    
  } catch (error) {
    console.error('Database connection error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: `Database operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  } finally {
    await client.end();
  }
}