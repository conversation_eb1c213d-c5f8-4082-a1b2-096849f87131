import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/app/util/prismaClient';
import { databaseAnalytics, calculateConversionRate } from '@/app/util/databaseAnalytics';
import { iProductPerformance } from '@/app/util/Interfaces';
import { convertPrismaProductToIProduct } from '@/app/util/businessHoursUtils';

export const dynamic = 'force-dynamic';

/**
 * GET /api/analytics/products
 * Fetches product performance data for a specific business
 */
export async function GET(req: NextRequest) {
    try {
        const { userId } = getAuth(req);
        if (!userId) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get query parameters
        const searchParams = req.nextUrl.searchParams;
        const businessId = searchParams.get('id');
        const startDate = searchParams.get('start');
        const endDate = searchParams.get('end');

        if (!businessId) {
            return NextResponse.json({ error: 'Business ID is required' }, { status: 400 });
        }

        // Verify user has access to this business
        const business = await prisma.business.findUnique({
            where: {
                id: businessId,
                ownerId: userId,
            },
        });

        if (!business) {
            return NextResponse.json({ error: 'Business not found or access denied' }, { status: 403 });
        }

        // Get products for this business
        const products = await prisma.product.findMany({
            where: {
                businessId: businessId,
                isDeleted: false,
            },
            include: {
                _count: {
                    select: {
                        reviews: {
                            where: {
                                isDeleted: false,
                                isPublic: true
                            }
                        }
                    }
                }
            },
        });

        if (products.length === 0) {
            return NextResponse.json([]);
        }

        // Use real database analytics instead of mock data
        const period = {
            startDate: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            endDate: endDate ? new Date(endDate) : new Date()
        };

        const productPerformance: iProductPerformance[] = [];

        // Get analytics for each product
        for (const product of products) {
            try {
                const analytics = await databaseAnalytics.getProductAnalytics(product.id);

                // Calculate real metrics
                const reviewCount = product._count.reviews || 0;

                // Get average rating from separate query
                const reviewStats = await prisma.review.aggregate({
                    where: {
                        productId: product.id,
                        isDeleted: false,
                        isPublic: true
                    },
                    _avg: { rating: true }
                });
                const averageRating = reviewStats._avg.rating || 0;

                const conversionRate = calculateConversionRate(analytics.totalViews, reviewCount);

                // Calculate bounce rate from view duration (simplified calculation)
                const bounceRate = analytics.averageViewDuration < 30 ? 70 :
                    analytics.averageViewDuration < 60 ? 50 :
                        analytics.averageViewDuration < 120 ? 30 : 20;

                // Calculate click-through rate based on unique visitors vs total views
                const clickThroughRate = analytics.totalViews > 0
                    ? (analytics.uniqueVisitors / analytics.totalViews) * 100
                    : 0;

                productPerformance.push({
                    id: analytics.id,
                    productId: product.id,
                    businessId: businessId,
                    viewsTrend: [{
                        date: new Date().toISOString().split('T')[0],
                        views: analytics.totalViews
                    }],
                    reviewsTrend: [{
                        date: new Date().toISOString().split('T')[0],
                        count: reviewCount,
                        rating: averageRating
                    }],
                    conversionTrend: [{
                        date: new Date().toISOString().split('T')[0],
                        rate: conversionRate
                    }],
                    engagementMetrics: {
                        averageTimeOnPage: analytics.averageViewDuration || 0,
                        bounceRate: bounceRate,
                        clickThroughRate: clickThroughRate
                    },
                    lastUpdated: analytics.lastUpdated,
                    product: convertPrismaProductToIProduct(product)
                });
            } catch (error) {
                console.error(`Error getting analytics for product ${product.id}:`, error);
                // Continue with other products
            }
        }

        return NextResponse.json(productPerformance);

    } catch (error) {
        console.error('Error fetching product analytics:', error);

        // Return error response instead of mock data
        return NextResponse.json(
            { 
                error: 'Failed to fetch product analytics', 
                message: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            }, 
            { status: 500 }
        );
    }
}
