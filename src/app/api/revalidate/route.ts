import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');

    if (!path) {
      return NextResponse.json(
        { success: false, message: 'Path parameter is required' },
        { status: 400 }
      );
    }

    // Revalidate the specified path
    revalidatePath(path);

    return NextResponse.json({
      success: true,
      message: `Revalidated path: ${path}`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error revalidating path:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to revalidate path' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');

    if (!path) {
      return NextResponse.json(
        { success: false, message: 'Path parameter is required' },
        { status: 400 }
      );
    }

    // Revalidate the specified path
    revalidatePath(path);

    return NextResponse.json({
      success: true,
      message: `Revalidated path: ${path}`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error revalidating path:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to revalidate path' },
      { status: 500 }
    );
  }
}