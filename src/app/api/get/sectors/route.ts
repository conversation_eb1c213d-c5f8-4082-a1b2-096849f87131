import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';
import { Sector } from '@/app/util/Interfaces';

export async function GET(request: NextRequest) {
  const client = new Client({
    connectionString: process.env.OVERFLOW_DATA_DATABASE_URL,
  });

  try {
    await client.connect();
    
    // Query to get all unique sectors and their sub_sectors from the sector table
    const sectorQuery = `
      SELECT
        MIN(id) as id,
        sector,
        ARRAY(
          SELECT DISTINCT unnest_val
          FROM (
            SELECT unnest(sub_sectors) as unnest_val
            FROM sector s2
            WHERE s2.sector = s1.sector AND sub_sectors IS NOT NULL
          ) t
          WHERE unnest_val IS NOT NULL
        ) as sub_sectors
      FROM sector s1
      WHERE sector IS NOT NULL
      GROUP BY sector
      ORDER BY sector;
    `;
    
    const result = await client.query(sectorQuery);
    
    // Map the database results to the Sector interface format
    const sectors: Sector[] = result.rows.map(row => ({
      id: row.id,
      sector: row.sector || undefined,
      sub_sectors: row.sub_sectors && row.sub_sectors.length > 0 ? row.sub_sectors : undefined
    }));
    
    return NextResponse.json({
      success: true,
      status: 200,
      data: sectors
    });
    
  } catch (error) {
    console.error('Database connection error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  } finally {
    await client.end();
  }
}