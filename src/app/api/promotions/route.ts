import { NextRequest, NextResponse } from "next/server";
import { auth, getAuth } from "@clerk/nextjs/server";
import { getActivePromotionsForProduct, getPromotionsForBusiness, createPromotion, CreatePromotionData } from "@/app/util/promotionService";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { clerkClient } from "@clerk/nextjs/server";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// Interface representing user data from Clerk
interface UserDATA {
  avatar?: string;
  azp: string;
  email: string;
  exp: number;
  firstName: string;
  lastName: string;
  fullName: string;
  iat: number;
  iss: string;
  jti: string;
  nbf: number;
  sub: string;
  userId: string;
  userName: string;
  metadata: {
    id: string;
    userInDb: boolean;
  };
}

// GET /api/promotions - Get promotions with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const businessId = searchParams.get('businessId');
    const activeOnly = searchParams.get('activeOnly') === 'true';

    if (productId) {
      // Get active promotions for a specific product
      const promotions = await getActivePromotionsForProduct(productId);
      return NextResponse.json({ success: true, data: promotions }, { status: 200 });
    }

    if (businessId) {
      // Get promotions for a specific business
      const promotions = await getPromotionsForBusiness(businessId, !activeOnly);
      return NextResponse.json({ success: true, data: promotions }, { status: 200 });
    }

    return NextResponse.json(
      { success: false, error: "productId or businessId parameter is required" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error fetching promotions:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch promotions" },
      { status: 500 }
    );
  }
}

// POST /api/promotions - Create a new promotion
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get promotion data from request body
    const promotionData = await request.json();

    // Validate required fields
    const requiredFields = ['title', 'description', 'startDate', 'endDate', 'productId', 'businessId'];
    for (const field of requiredFields) {
      if (!promotionData[field]) {
        return NextResponse.json(
          { success: false, error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Get user data from Clerk
    let clerkUserData = null;
    let userIdFromClerk = null;

    try {
      const { sessionClaims } = getAuth(request as any);
      const clerkClaimsData = sessionClaims as unknown as UserDATA;

      // Check if user exists in database
      if (!(await userInDb(clerkClaimsData.userId))) {
        clerkUserData = await addUserToDb(clerkClaimsData);
      } else {
        clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
        if (clerkUserData.publicMetadata.id !== undefined) {
          userIdFromClerk = clerkUserData.publicMetadata.id as string;
        } else {
          return NextResponse.json(
            { success: false, error: "User metadata not found" },
            { status: 401 }
          );
        }
      }

      const dbUserId = clerkUserData?.publicMetadata.id as string;

      // Prepare promotion data
      const createData: CreatePromotionData = {
        title: promotionData.title,
        description: promotionData.description,
        startDate: new Date(promotionData.startDate),
        endDate: new Date(promotionData.endDate),
        discountPercentage: promotionData.discountPercentage,
        discountAmount: promotionData.discountAmount,
        promotionCode: promotionData.promotionCode,
        isActive: promotionData.isActive ?? true,
        image: promotionData.image,
        productId: promotionData.productId,
        businessId: promotionData.businessId,
        createdById: dbUserId,
      };

      // Create promotion
      const promotion = await createPromotion(createData);

      return NextResponse.json({ success: true, data: promotion }, { status: 201 });
    } catch (error) {
      console.error("Error with user authentication:", error);
      return NextResponse.json(
        { success: false, error: "Authentication failed" },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Error creating promotion:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create promotion" },
      { status: 500 }
    );
  }
}