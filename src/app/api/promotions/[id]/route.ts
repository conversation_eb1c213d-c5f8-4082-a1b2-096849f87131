import { NextRequest, NextResponse } from "next/server";
import { auth, getAuth } from "@clerk/nextjs/server";
import { getPromotionById, updatePromotion, deletePromotion, trackPromotionView, UpdatePromotionData } from "@/app/util/promotionService";
import { userInDb } from "@/app/util/userInDb";
import { addUserToDb } from "@/app/util/addUserToDb";
import { clerkClient } from "@clerk/nextjs/server";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// Interface representing user data from Clerk
interface UserDATA {
  avatar?: string;
  azp: string;
  email: string;
  exp: number;
  firstName: string;
  lastName: string;
  fullName: string;
  iat: number;
  iss: string;
  jti: string;
  nbf: number;
  sub: string;
  userId: string;
  userName: string;
  metadata: {
    id: string;
    userInDb: boolean;
  };
}

// GET /api/promotions/[id] - Get a specific promotion
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const promotion = await getPromotionById(params.id);

    if (!promotion) {
      return NextResponse.json(
        { success: false, error: "Promotion not found" },
        { status: 404 }
      );
    }

    // Track view if this is a public view (not from owner/admin)
    const { searchParams } = new URL(request.url);
    const trackView = searchParams.get('trackView') === 'true';
    
    if (trackView) {
      // Track promotion view asynchronously
      trackPromotionView(params.id).catch(error => {
        console.warn("Failed to track promotion view:", error);
      });
    }

    return NextResponse.json({ success: true, data: promotion }, { status: 200 });
  } catch (error) {
    console.error("Error fetching promotion:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch promotion" },
      { status: 500 }
    );
  }
}

// PUT /api/promotions/[id] - Update a promotion
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get promotion data from request body
    const promotionData = await request.json();

    // Get user data from Clerk
    let clerkUserData = null;
    let userIdFromClerk = null;

    try {
      const { sessionClaims } = getAuth(request as any);
      const clerkClaimsData = sessionClaims as unknown as UserDATA;

      // Check if user exists in database
      if (!(await userInDb(clerkClaimsData.userId))) {
        clerkUserData = await addUserToDb(clerkClaimsData);
      } else {
        clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
        if (clerkUserData.publicMetadata.id !== undefined) {
          userIdFromClerk = clerkUserData.publicMetadata.id as string;
        } else {
          return NextResponse.json(
            { success: false, error: "User metadata not found" },
            { status: 401 }
          );
        }
      }

      const dbUserId = clerkUserData?.publicMetadata.id as string;

      // Check if user owns this promotion or is admin
      const existingPromotion = await getPromotionById(params.id);
      if (!existingPromotion) {
        return NextResponse.json(
          { success: false, error: "Promotion not found" },
          { status: 404 }
        );
      }

      // TODO: Add proper authorization check
      // For now, allow any authenticated user to update
      // In production, check if user owns the business or is admin

      // Prepare update data
      const updateData: UpdatePromotionData = {};
      
      if (promotionData.title !== undefined) updateData.title = promotionData.title;
      if (promotionData.description !== undefined) updateData.description = promotionData.description;
      if (promotionData.startDate !== undefined) updateData.startDate = new Date(promotionData.startDate);
      if (promotionData.endDate !== undefined) updateData.endDate = new Date(promotionData.endDate);
      if (promotionData.discountPercentage !== undefined) updateData.discountPercentage = promotionData.discountPercentage;
      if (promotionData.discountAmount !== undefined) updateData.discountAmount = promotionData.discountAmount;
      if (promotionData.promotionCode !== undefined) updateData.promotionCode = promotionData.promotionCode;
      if (promotionData.isActive !== undefined) updateData.isActive = promotionData.isActive;
      if (promotionData.image !== undefined) updateData.image = promotionData.image;

      // Update promotion
      const promotion = await updatePromotion(params.id, updateData);

      return NextResponse.json({ success: true, data: promotion }, { status: 200 });
    } catch (error) {
      console.error("Error with user authentication:", error);
      return NextResponse.json(
        { success: false, error: "Authentication failed" },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Error updating promotion:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update promotion" },
      { status: 500 }
    );
  }
}

// DELETE /api/promotions/[id] - Delete a promotion
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user data from Clerk
    let clerkUserData = null;
    let userIdFromClerk = null;

    try {
      const { sessionClaims } = getAuth(request as any);
      const clerkClaimsData = sessionClaims as unknown as UserDATA;

      // Check if user exists in database
      if (!(await userInDb(clerkClaimsData.userId))) {
        clerkUserData = await addUserToDb(clerkClaimsData);
      } else {
        clerkUserData = await (await clerkClient()).users.getUser(clerkClaimsData.userId);
        if (clerkUserData.publicMetadata.id !== undefined) {
          userIdFromClerk = clerkUserData.publicMetadata.id as string;
        } else {
          return NextResponse.json(
            { success: false, error: "User metadata not found" },
            { status: 401 }
          );
        }
      }

      const dbUserId = clerkUserData?.publicMetadata.id as string;

      // Check if user owns this promotion or is admin
      const existingPromotion = await getPromotionById(params.id);
      if (!existingPromotion) {
        return NextResponse.json(
          { success: false, error: "Promotion not found" },
          { status: 404 }
        );
      }

      // TODO: Add proper authorization check
      // For now, allow any authenticated user to delete
      // In production, check if user owns the business or is admin

      // Delete promotion
      await deletePromotion(params.id);

      return NextResponse.json({ success: true, message: "Promotion deleted successfully" }, { status: 200 });
    } catch (error) {
      console.error("Error with user authentication:", error);
      return NextResponse.json(
        { success: false, error: "Authentication failed" },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Error deleting promotion:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete promotion" },
      { status: 500 }
    );
  }
}