/**
 * Username availability validation API endpoint
 * Provides real-time username validation for forms
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateUsername } from '@/app/config/usernameRestrictions';
import { trackUsernameValidation, trackUsernameValidationError } from '@/app/config/usernameRestrictionsAnalytics';
import { prisma } from '@/app/util/prismaClient';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const { username } = await request.json();
    const userAgent = request.headers.get('user-agent') || undefined;
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined;

    // Basic input validation
    if (!username || typeof username !== 'string') {
      return NextResponse.json({
        success: false,
        status: 400,
        data: {
          isValid: false,
          reason: 'Username is required',
          available: false
        }
      });
    }

    // Trim and validate length
    const trimmedUsername = username.trim();
    if (trimmedUsername.length < 2) {
      return NextResponse.json({
        success: true,
        status: 200,
        data: {
          isValid: false,
          reason: 'Username must be at least 2 characters long',
          available: false
        }
      });
    }

    if (trimmedUsername.length > 30) {
      return NextResponse.json({
        success: true,
        status: 200,
        data: {
          isValid: false,
          reason: 'Username must be 30 characters or less',
          available: false
        }
      });
    }

    // Check username restrictions
    const restrictionValidation = validateUsername(trimmedUsername);
    if (!restrictionValidation.isValid) {
      const responseTime = Date.now() - startTime;
      
      // Track restriction hit
      trackUsernameValidation({
        username: trimmedUsername,
        isRestricted: true,
        category: restrictionValidation.category,
        responseTime,
        userAgent,
        ipAddress
      }).catch(err => console.error('Analytics tracking error:', err));

      return NextResponse.json({
        success: true,
        status: 200,
        data: {
          isValid: false,
          reason: restrictionValidation.reason,
          available: false,
          category: restrictionValidation.category
        }
      });
    }

    // Check if username is already taken in database
    const existingUser = await prisma.user.findFirst({
      where: {
        userName: {
          equals: trimmedUsername,
          mode: 'insensitive'
        },
        isDeleted: false
      },
      select: { id: true }
    });

    if (existingUser) {
      const responseTime = Date.now() - startTime;
      
      // Track username taken
      trackUsernameValidation({
        username: trimmedUsername,
        isRestricted: false, // Not restricted, just taken
        responseTime,
        userAgent,
        ipAddress
      }).catch(err => console.error('Analytics tracking error:', err));

      return NextResponse.json({
        success: true,
        status: 200,
        data: {
          isValid: false,
          reason: 'This username is already taken',
          available: false
        }
      });
    }

    // Username is valid and available
    const responseTime = Date.now() - startTime;
    
    // Track successful validation
    trackUsernameValidation({
      username: trimmedUsername,
      isRestricted: false,
      responseTime,
      userAgent,
      ipAddress
    }).catch(err => console.error('Analytics tracking error:', err));

    return NextResponse.json({
      success: true,
      status: 200,
      data: {
        isValid: true,
        available: true,
        message: 'Username is available'
      }
    });

  } catch (error) {
    console.error('[USERNAME_VALIDATION] API error:', error);
    
    // Track error
    trackUsernameValidationError({
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date()
    }).catch(err => console.error('Error tracking failed:', err));
    
    // Fail-safe: return generic error but don't block user
    return NextResponse.json({
      success: false,
      status: 500,
      data: {
        isValid: true, // Fail-safe: allow username on system error
        available: true,
        reason: 'Unable to validate username. Please try again.'
      }
    });
  }
}

// GET method for simple availability checks
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');

    if (!username) {
      return NextResponse.json({
        success: false,
        status: 400,
        data: { error: 'Username parameter is required' }
      });
    }

    // Use the same validation logic as POST
    const response = await POST(new NextRequest(request.url, {
      method: 'POST',
      body: JSON.stringify({ username }),
      headers: { 'Content-Type': 'application/json' }
    }));

    return response;
  } catch (error) {
    console.error('[USERNAME_VALIDATION] GET error:', error);
    return NextResponse.json({
      success: false,
      status: 500,
      data: { error: 'Validation failed' }
    });
  }
}