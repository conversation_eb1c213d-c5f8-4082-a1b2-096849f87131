import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
    const { searchParams } = new URL(request.url);
    const urlToResolve = searchParams.get('url');

    if (!urlToResolve) {
        return NextResponse.json({ error: 'URL parameter is missing' }, { status: 400 });
    }

    try {
        // Validate the URL (basic validation, can be enhanced)
        new URL(urlToResolve);
    } catch (_) {
        return NextResponse.json({ error: 'Invalid URL format' }, { status: 400 });
    }

    try {
        const response = await fetch(urlToResolve, {
            method: 'HEAD',
            redirect: 'follow',
        });

        if (!response.ok) {
            // Even if response.ok is false, response.url might still contain the redirected URL
            // if the final redirect resulted in an error (e.g., 404 on the final domain).
            // However, for simplicity here, we treat non-ok as a failure to resolve properly.
            return NextResponse.json({ error: `Failed to resolve URL: ${response.statusText} (Status: ${response.status})` }, { status: response.status });
        }

        // response.url will contain the final URL after all redirects
        return NextResponse.json({ resolvedUrl: response.url });

    } catch (error) {
        console.error('Error resolving URL:', error);
        let errorMessage = 'Failed to resolve URL due to a network or server error.';
        if (error instanceof Error) {
            errorMessage = error.message;
        }
        return NextResponse.json({ error: errorMessage }, { status: 500 });
    }
} 