import { NextResponse } from 'next/server';

const WORD_SERVER_URL = process.env.WORD_SERVER_URL;

if (!WORD_SERVER_URL) {
  throw new Error('WORD_SERVER_URL environment variable is not set');
}

export async function POST(request: Request) {
  try {
    const { word } = await request.json();
    if (!word) {
      return NextResponse.json({ error: 'Word is required' }, { status: 400 });
    }

    const response = await fetch(`${WORD_SERVER_URL}/word/${word}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to delete word' }));
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete word' },
      { status: 500 }
    );
  }
}
