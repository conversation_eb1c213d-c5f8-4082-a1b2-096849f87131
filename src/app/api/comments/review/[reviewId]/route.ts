import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/util/prismaClient";
import { sanitizeDeletedCommentsInReview } from "@/app/util/sanitizeDeletedComments";
import { cleanReview, createFilter } from "@/app/store/badWordsFilter";
import { redisService } from "@/app/lib/redis";

const filter = createFilter();

// Use the standardized cache key from CacheKeys
import { CacheKeys, trackCacheHit, trackCacheMiss } from "@/app/lib/cache";

export async function GET(
  request: NextRequest,
  { params }: { params: { reviewId: string } }
) {
  const { reviewId } = params;

  if (!reviewId) {
    return NextResponse.json({
      success: false,
      status: 400,
      error: "Review ID is required",
    });
  }

  try {
    const cacheKey = CacheKeys.reviewComments(reviewId);
    
    // Try to get from cache first
    const cached = await redisService.getFromCache<any>(cacheKey);
    if (cached) {
      trackCacheHit(cacheKey);
      return NextResponse.json({
        success: true,
        status: 200,
        data: cached,
      });
    }

    trackCacheMiss(cacheKey);

    // Fetch comments from database (include deleted ones for sanitization)
    const comments = await prisma.comment.findMany({
      where: {
        reviewId: reviewId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            userName: true,
            avatar: true,
          },
        },
        parent: {
          select: {
            id: true,
            userId: true,
            body: true,
          },
        },
        votes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                userName: true,
              },
            },
          },
        },
        replies: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                userName: true,
                avatar: true,
              },
            },
            votes: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    userName: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdDate: "asc",
          },
        },
      },
      orderBy: {
        createdDate: "desc",
      },
    });

    // Apply sanitization for deleted comments
    let processedComments = comments.map(comment => {
      if (comment.isDeleted) {
        return {
          ...comment,
          body: "This comment has been deleted.",
          user: comment.user ? {
            ...comment.user,
            userName: "Comment Deleted",
            avatar: "/deleted-user.svg"
          } : null,
          // Also sanitize replies
          replies: comment.replies?.map(reply => {
            if (reply.isDeleted) {
              return {
                ...reply,
                body: "This comment has been deleted.",
                user: reply.user ? {
                  ...reply.user,
                  userName: "Comment Deleted",
                  avatar: "/deleted-user.svg"
                } : null
              };
            }
            return reply;
          }) || []
        };
      }
      
      // For non-deleted comments, still check their replies
      return {
        ...comment,
        replies: comment.replies?.map(reply => {
          if (reply.isDeleted) {
            return {
              ...reply,
              body: "This comment has been deleted.",
              user: reply.user ? {
                ...reply.user,
                userName: "Deleted User",
                avatar: "/deleted-user.svg"
              } : null
            };
          }
          return reply;
        }) || []
      };
    });

    // Apply bad words filter
    let cleanedComments = processedComments;
    try {
      const badWordsFilter = await filter;
      cleanedComments = processedComments.map(comment => {
        const cleanedComment = { ...comment };
        cleanedComment.body = badWordsFilter.clean(comment.body);
        
        if (cleanedComment.replies) {
          cleanedComment.replies = cleanedComment.replies.map(reply => ({
            ...reply,
            body: badWordsFilter.clean(reply.body)
          }));
        }
        
        return cleanedComment;
      });
    } catch (error) {
      console.warn("Bad words filter failed for comments:", error);
    }

    // Cache the result for 3 minutes (180 seconds) - shorter than reviews
    await redisService.setInCache(cacheKey, cleanedComments, 180);
    // Removed console.log statement
    return NextResponse.json({
      success: true,
      status: 200,
      data: cleanedComments,
    });
  } catch (error) {
    console.error("Error fetching comments:", error);
    return NextResponse.json({
      success: false,
      status: 500,
      error: error instanceof Error ? error.message : "Failed to fetch comments",
    });
  }
}