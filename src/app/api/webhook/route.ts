import { Webhook } from "svix";
import { headers } from "next/headers";
import { WebhookEvent, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { iUser, UserCreatedEvent } from "@/app/util/Interfaces";
import { createUser } from "@/app/util/NotificationFunctions";
import { validateUsername } from "@/app/config/usernameRestrictions";
import { isAdmin, validateAdminConfig } from "@/app/config/admin";

export async function POST(req: Request) {
  // Validate admin configuration on startup
  validateAdminConfig();

  const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;

  if (!WEBHOOK_SECRET) {
    throw new Error(
      "Please add WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local",
    );
  }

  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occured -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  let payload: UserCreatedEvent = await req.json();
  const body = JSON.stringify(payload);

  // Create a new SVIX instance with your secret.
  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  // Verify the payload with the headers
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  // Get the ID and type
  const { id } = evt.data;
  const eventType = evt.type;

  // Get user email early for use in username logic
  const userEmail = payload.data.email_addresses[0].email_address;

  // Determine final username – validate and possibly generate fallback
  const requestedUsername = payload.data.username || userEmail;
  let finalUsername = requestedUsername;
  let usernameNeedsChange = false;

  // Validate username against restrictions
  const validation = validateUsername(requestedUsername);
  if (!validation.isValid) {
    // Replace with safe fallback (user_<8chars>)
    // Clerk user IDs are formatted like "user_<hash>". Avoid duplicating the prefix.
    const idSuffix = payload.data.id.replace(/^user_/, "").slice(-8);
    finalUsername = `user_${idSuffix}`;
    usernameNeedsChange = true;

    // will update Clerk metadata after DB upsert
  } else {
    // If requested username still follows fallback pattern keep flag true
    const autoGenerated = /^user_[A-Za-z0-9]{8}$/.test(requestedUsername);
    usernameNeedsChange = autoGenerated;
  }

  // Determine user role based on email
  const userRole = isAdmin(userEmail) ? 'ADMIN' : 'USER';
  
  const user = await prisma.user.upsert({
    where: { email: userEmail },
    update: {},
    create: {
      id: payload.data.id,
      userName: finalUsername,
      avatar: payload.data.profile_image_url,
      email: userEmail,
      firstName: payload.data.first_name,
      lastName: payload.data.last_name,
      createdDate: new Date(),
      clerkUserId: payload.data.id,
      isDeleted: false,
      role: userRole, // Set role based on admin email check
      
      usernameNeedsChange: usernameNeedsChange,
    },
  }) as iUser;

  createUser(user);
  // Single Clerk update – set username (if changed) and metadata in one call
  await (await clerkClient()).users.updateUser(payload.data.id, {
    username: finalUsername,
    publicMetadata: { userInDb: true, id: user.id, usernameNeedsChange },
  });

  return new Response("", { status: 201 });
}
