import { Webhook } from "svix";
import { headers } from "next/headers";
import { WebhookEvent, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "@/app/util/prismaClient";
import { validateUsername } from "@/app/config/usernameRestrictions";
import { UserCreatedEvent } from "@/app/util/Interfaces";
import { isAdmin } from "@/app/config/admin";
export async function POST(req: Request) {
  const WEBHOOK_SECRET = process.env.USER_UPDATE_WEBHOOK_SECRET;
  if (!WEBHOOK_SECRET) {
    throw new Error(
      "Please add WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local",
    );
  }

  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occured -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  let payload: UserCreatedEvent = await req.json();
  const body = JSON.stringify(payload);

  // Create a new SVIX instance with your secret.
  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  // Verify the payload with the headers
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  // Get the ID and type
  // const { id } = evt.data;
  // const eventType = evt.type;

  // Determine username logic
  const userEmail = payload.data.email_addresses[0].email_address;
  const rawUsername = (payload.data.username || "").trim();
  let finalUsername = rawUsername;
  // default assumption
  let usernameNeedsChange = false;

  // Determine user role based on email
  const userRole = isAdmin(userEmail) ? 'ADMIN' : 'USER';

  // Admins can bypass username restrictions entirely
  if (userRole !== 'ADMIN') {
    const validation = validateUsername(rawUsername);
    if (!validation.isValid || rawUsername === "") {
      const idSuffix = payload.data.id.replace(/^user_/, "").slice(-8);
      finalUsername = `user_${idSuffix}`;
      usernameNeedsChange = true;
    } else {
      // If the chosen username matches our auto-generated fallback pattern,
      // keep prompting the user to change it.
      const autoGenerated = /^user_[A-Za-z0-9]{8}$/.test(rawUsername);
      usernameNeedsChange = autoGenerated;
    }
  }
  // If admin, username is accepted as-is

  const user = await prisma.user.upsert({
    where: { email: userEmail },
    update: {
      avatar: payload.data.image_url,
      lastName: payload.data.last_name,
      firstName: payload.data.first_name,
      userName: finalUsername,
      role: userRole, // Update role in case email was added to admin list
      usernameNeedsChange: usernameNeedsChange,
    },
    create: {
      id: payload.data.id, // Set database ID to Clerk ID
      userName: finalUsername,
      avatar: payload.data.image_url,
      email: userEmail,
      firstName: payload.data.first_name,
      lastName: payload.data.last_name,
      createdDate: new Date(),
      clerkUserId: payload.data.id,
      role: userRole, // Set role based on admin email check
      usernameNeedsChange: usernameNeedsChange,
    },
  });

  // Single Clerk update – avoid loops: only set username if it actually changed
  const clerkUpdate: any = {
    publicMetadata: { userInDb: true, id: (user as any).id, usernameNeedsChange },
  };
  if (payload.data.username !== finalUsername) {
    clerkUpdate.username = finalUsername;
  }
  await (await clerkClient()).users.updateUser(payload.data.id, clerkUpdate);

  return new Response("", { status: 201 });
}
