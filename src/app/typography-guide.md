# ReviewIt Typography Style Guide

This document defines the typography standards for the ReviewIt platform, ensuring consistency across all pages and components.

## Font System

### Base Font
- **Font Family**: Default system fonts (Tailwind's default sans-serif stack)
- **Base Size**: 16px (1rem)

### Font Weights
- **Light**: 300
- **Regular**: 400
- **Medium**: 500
- **Semi-bold**: 600
- **Bold**: 700
- **Extra Bold**: 800
- **Black**: 900

## Heading Hierarchy

| Level | PC/Desktop Size | Mobile Size | Weight    | Usage |
|-------|-----------------|-------------|-----------|-------|
| H1    | 48px (3rem)     | 32px (2rem) | Black     | Main page titles, hero section primary headline |
| H2    | 36px (2.25rem)  | 24px (1.5rem)| Extra Bold | Section titles, secondary headlines |
| H3    | 24px (1.5rem)   | 20px (1.25rem)| Bold    | Card titles, feature titles |
| H4    | 20px (1.25rem)  | 18px (1.125rem)| Semi-bold | Subsection titles, small card headers |
| H5    | 18px (1.125rem) | 16px (1rem) | Medium    | Card subtitles, labels |
| H6    | 16px (1rem)     | 14px (0.875rem)| Medium  | Small labels, metadata |

## Body Text

| Type        | PC/Desktop Size | Mobile Size | Weight   | Usage |
|-------------|-----------------|-------------|----------|-------|
| Large Body  | 18px (1.125rem) | 16px (1rem) | Regular  | Lead paragraphs, highlighted text |
| Body        | 16px (1rem)     | 14px (0.875rem)| Regular | Standard body text |
| Small Body  | 14px (0.875rem) | 12px (0.75rem)| Regular | Captions, footnotes, metadata |

## Special Text Elements

| Element     | Size        | Weight   | Usage |
|-------------|-------------|----------|-------|
| Button Text | 16px (1rem) | Medium   | Primary buttons |
| Button Text | 14px (0.875rem)| Medium | Secondary/small buttons |
| Link Text   | Inherit     | Medium   | Navigation links, inline links |
| Badge Text  | 12px (0.75rem)| Medium  | Tags, status indicators |

## Color Palette for Text

| Element             | Color Class           | Hex Value | Usage |
|---------------------|-----------------------|-----------|-------|
| Primary Heading     | text-myTheme-lightTextHeading | #0F172A | Main headings |
| Secondary Heading   | text-myTheme-lightTextHeading | #0F172A | Secondary headings |
| Body Text           | text-myTheme-lightTextBody | #334155 | Standard body text |
| Secondary Text      | text-gray-600         | Tailwind gray-600 | Captions, metadata |
| Link Text           | text-myTheme-primary  | #0F172A | Links |
| Link Hover          | text-myTheme-secondary| #1E293B | Link hover state |
| Highlight Text      | text-myTheme-primary  | #0F172A | Emphasized text |

## Responsive Implementation

All typography should be responsive using the following Tailwind classes:

- **Mobile First**: Default classes apply to mobile
- **Tablet Up**: Use `sm:` prefix for 640px and above
- **Desktop Up**: Use `lg:` prefix for 1024px and above
- **Large Desktop**: Use `xl:` prefix for 1280px and above

## Implementation Examples

### Headings
```html
<!-- H1 - Main page title -->
<h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-black text-myTheme-lightTextHeading">Main Title</h1>

<!-- H2 - Section title -->
<h2 className="text-xl sm:text-2xl lg:text-3xl font-extrabold text-myTheme-lightTextHeading">Section Title</h2>

<!-- H3 - Card title -->
<h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-myTheme-lightTextHeading">Card Title</h3>
```

### Body Text
```html
<!-- Large body text -->
<p className="text-base sm:text-lg text-myTheme-lightTextBody">This is large body text for lead paragraphs.</p>

<!-- Standard body text -->
<p className="text-sm sm:text-base text-myTheme-lightTextBody">This is standard body text.</p>

<!-- Small body text -->
<p className="text-xs sm:text-sm text-gray-600">This is small body text for captions.</p>
```

### Special Elements
```html
<!-- Button text -->
<button className="text-sm sm:text-base font-medium">Click Me</button>

<!-- Badge text -->
<span className="text-xs font-medium">New</span>
```

## Component-Specific Guidelines

### Hero Section
- Main headline: H1
- Subheadline: Large Body
- Stats numbers: H3
- Stats labels: Small Body

### Cards (Review Cards, Category Cards, etc.)
- Card titles: H3
- Card subtitles: H5
- Card body text: Body
- Metadata: Small Body

### Navigation & UI Elements
- Navigation labels: H6
- Button text: Button Text
- Form labels: H6
- Form helper text: Small Body

## Best Practices

1. **Consistency**: Always use the defined classes for similar elements
2. **Semantic HTML**: Use appropriate heading levels (h1-h6) for accessibility
3. **Responsive Text**: Ensure text is readable on all device sizes
4. **Contrast**: Maintain sufficient color contrast for accessibility
5. **Line Height**: Use Tailwind's default line heights unless specific adjustments are needed
6. **Letter Spacing**: Use the existing letter spacing from Tailwind config (-0.01em to -0.02em for larger text)

## Future Expansion

This guide will be expanded as new components are added to the platform. Any new typography needs should be added to this document to maintain consistency across the application.
