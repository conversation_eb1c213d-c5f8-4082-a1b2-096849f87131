"use client";
import { useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useQueryClient } from "@tanstack/react-query";

export default function ChangeUsernameForm({ dbId }: { dbId?: string }) {
  const { userId, getToken } = useAuth();
  const [userName, setUserName] = useState("");
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();

  const submit = async () => {
    if (!userName.trim()) return;
    setLoading(true);
    try {
      const token = await getToken();
      const res = await fetch(`/api/update/user/${dbId ?? userId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        credentials: "include",
        body: JSON.stringify({ userName }),
      });
      if (!res.ok) throw new Error("Failed");
      
      // Invalidate React Query caches that might contain the old username
      queryClient.invalidateQueries({ queryKey: ["user"] });
      queryClient.invalidateQueries({ queryKey: ["user", dbId || userId] });
      queryClient.invalidateQueries({ queryKey: ["user", "current"] });
      queryClient.invalidateQueries({ queryKey: ["reviews"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      
      toast.success("Username updated");
      
      // Small delay to ensure Clerk metadata propagates, then force refresh
      setTimeout(() => {
        window.location.href = "/userprofile";
      }, 500);
    } catch (e) {
      toast.error("Could not update username");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Input
        value={userName}
        onChange={(e) => setUserName(e.target.value)}
        placeholder="Enter new username"
      />
      <Button onClick={submit} disabled={loading}>
        {loading ? "Saving…" : "Save"}
      </Button>
    </div>
  );
}
