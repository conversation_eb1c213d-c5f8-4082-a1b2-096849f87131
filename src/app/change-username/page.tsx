import { auth, clerkClient } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import ChangeUsernameForm from "./ChangeUsernameForm";
import { prisma } from "@/app/util/prismaClient";

/**
 * Server page that only renders if the signed-in user still has the
 * `usernameNeedsChange` flag set in the database (source of truth).
 * Otherwise we redirect them away.
 */
export default async function ChangeUsernamePage() {
  const { userId } = await auth();
  // Clerk middleware will handle redirect if not authenticated
  if (!userId) {
    return <div>Loading...</div>;
  }

  // Get Clerk user + metadata for initial check
  const clerk = await clerkClient();
  const clerkUser = await clerk.users.getUser(userId);
  const needsChangeFromClerk = Boolean(clerkUser.publicMetadata?.usernameNeedsChange);
  
  // Get database user (source of truth)
  const dbUser = await prisma.user.findUnique({
    where: { clerkUserId: userId },
    select: { id: true, usernameNeedsChange: true },
  });
  
  // If no DB user found, redirect to profile
  if (!dbUser) {
    console.log("[ChangeUsername] No database user found, redirecting");
    redirect("/userprofile");
  }
  
  // Database is source of truth
  const needsChange = Boolean(dbUser.usernameNeedsChange);
  
  // If Clerk metadata is out of sync with database, update it
  if (needsChangeFromClerk !== needsChange) {
    console.log(`[ChangeUsername] Metadata out of sync. DB: ${needsChange}, Clerk: ${needsChangeFromClerk}. Updating Clerk.`);
    
    await clerk.users.updateUser(userId, {
      publicMetadata: { 
        ...clerkUser.publicMetadata,
        userInDb: true,
        id: dbUser.id,
        usernameNeedsChange: needsChange 
      }
    });
  }

  if (!needsChange) {
    // Already resolved according to database → send to profile settings
    redirect("/userprofile");
  }

  return (
    <div className="max-w-md mx-auto mt-10 p-6 border rounded shadow bg-white">
      <h1 className="text-2xl font-semibold mb-4">Choose a new username</h1>
      <p className="text-sm text-gray-600 mb-6">
        Your previous choice was restricted, so we assigned a temporary one. Please
        select a unique username to continue.
      </p>

      {/* Client form component */}
      <ChangeUsernameForm dbId={dbUser.id} />
    </div>
  );
}
