import { useCallback, useEffect, useRef } from 'react';

/**
 * Lightweight debounce hook to replace lodash.debounce.
 * Returns a stable debounced version of the provided callback.
 *
 * @param callback Callback to debounce
 * @param delay Delay in milliseconds
 */
export default function useDebouncedCallback<T extends (...args: any[]) => void>(
  callback: T,
  delay: number
) {
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const callbackRef = useRef<T>(callback);

  // Keep latest callback reference
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Debounced function
  const debounced = useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callbackRef.current(...args);
    }, delay);
  }, [delay]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debounced as T;
}
