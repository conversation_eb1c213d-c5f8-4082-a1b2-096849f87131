import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

const useScrollOnLoad = (elementId: string) => {
  const searchParams = useSearchParams();

  useEffect(() => {
    // Function to attempt scrolling multiple times
    const attemptScroll = (attemptsLeft = 5) => {
      const element = document.getElementById(elementId);
      if (element) {
        // Force scroll with both methods for maximum compatibility
        window.scrollTo({
          top: element.getBoundingClientRect().top + window.pageYOffset - 100,
          behavior: 'smooth'
        });
        
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        console.log('Scrolled to element:', elementId);
      } else if (attemptsLeft > 0) {
        // If element not found yet, try again after a delay
        setTimeout(() => attemptScroll(attemptsLeft - 1), 200);
      }
    };

    // Check if we have any search params (like tags)
    const hasSearchParams = searchParams && Array.from(searchParams.keys()).length > 0;
    
    // If we have search params or a hash, attempt to scroll
    if (hasSearchParams || window.location.hash === `#${elementId}`) {
      // Initial delay to ensure DOM is ready
      setTimeout(() => attemptScroll(), 500);
    }
  }, [searchParams, elementId]);
};

export default useScrollOnLoad;
