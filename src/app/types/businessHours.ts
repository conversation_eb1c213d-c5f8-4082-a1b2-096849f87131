/**
 * Types for flexible business hours functionality
 */

export type DayOfWeek = 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';

export interface DaySchedule {
  day: DayOfWeek;
  isClosed: boolean;
  openTime: string | null; // Format: "HH:MM" (24-hour)
  closeTime: string | null; // Format: "HH:MM" (24-hour)
}

export interface BusinessHours {
  schedules: DaySchedule[];
  openOnHolidays: boolean;
}

export const DAYS_OF_WEEK: DayOfWeek[] = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

/**
 * Creates a default business hours object with all days closed by default
 */
export function createDefaultBusinessHours(): BusinessHours {
  return {
    schedules: DAYS_OF_WEEK.map(day => ({
      day,
      isClosed: true,
      openTime: null,
      closeTime: null,
    })),
    openOnHolidays: false
  };
}

/**
 * Formats business hours for display
 * @param businessHours The business hours object
 * @returns Formatted string representation of business hours
 */
export function formatBusinessHours(businessHours: BusinessHours | null | undefined): string {
  if (!businessHours?.schedules?.length) {
    return 'Hours not specified';
  }

  // Group consecutive days with the same hours
  const groupedSchedules: { days: DayOfWeek[], schedule: DaySchedule }[] = [];
  
  businessHours.schedules.forEach(schedule => {
    const lastGroup = groupedSchedules[groupedSchedules.length - 1];
    
    if (
      lastGroup && 
      lastGroup.schedule.isClosed === schedule.isClosed &&
      lastGroup.schedule.openTime === schedule.openTime &&
      lastGroup.schedule.closeTime === schedule.closeTime
    ) {
      lastGroup.days.push(schedule.day);
    } else {
      groupedSchedules.push({
        days: [schedule.day],
        schedule
      });
    }
  });

  // Format each group
  return groupedSchedules.map(group => {
    const { days, schedule } = group;
    
    // Format days (e.g., "Mon-Wed" or "Thu" or "Fri-Sat")
    let daysStr: string;
    if (days.length === 1) {
      daysStr = days[0].substring(0, 3);
    } else {
      daysStr = `${days[0].substring(0, 3)}-${days[days.length - 1].substring(0, 3)}`;
    }
    
    // Format hours
    if (schedule.isClosed) {
      return `${daysStr}: Closed`;
    }
    
    return `${daysStr}: ${schedule.openTime} - ${schedule.closeTime}`;
  }).join(', ');
}

/**
 * Determines if a business is currently open based on business hours and current time
 * @param businessHours The business hours object
 * @param now Optional Date object representing current time (defaults to now)
 * @param isHoliday Optional boolean indicating if today is a holiday
 * @returns Object with isOpen status and relevant message
 */
export function isBusinessOpen(
  businessHours: BusinessHours | null | undefined, 
  now: Date = new Date(),
  isHoliday: boolean = false
): { isOpen: boolean, message: string } {
  if (!businessHours?.schedules?.length) {
    return { isOpen: false, message: 'Hours not specified' };
  }

  // Check if it's a holiday
  if (isHoliday && !businessHours.openOnHolidays) {
    return { isOpen: false, message: 'Closed for holiday' };
  }

  // Get current day and time
  const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const currentDay = daysOfWeek[now.getDay()];
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTimeStr = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;

  // Find schedule for today
  const todaySchedule = businessHours.schedules.find(s => s.day === currentDay);
  
  if (!todaySchedule) {
    return { isOpen: false, message: 'Schedule not found for today' };
  }

  if (todaySchedule.isClosed) {
    return { isOpen: false, message: 'Closed today' };
  }

  if (!todaySchedule.openTime || !todaySchedule.closeTime) {
    return { isOpen: false, message: 'Hours not specified for today' };
  }

  // Check if current time is within business hours
  const isOpen = currentTimeStr >= todaySchedule.openTime && currentTimeStr < todaySchedule.closeTime;

  if (isOpen) {
    return { isOpen: true, message: `Open until ${todaySchedule.closeTime}` };
  } else if (currentTimeStr < todaySchedule.openTime) {
    return { isOpen: false, message: `Opens at ${todaySchedule.openTime}` };
  } else {
    // Find next open day
    const currentDayIndex = DAYS_OF_WEEK.indexOf(todaySchedule.day as DayOfWeek);
    let nextOpenDay: DaySchedule | undefined;
    let daysUntilOpen = 0;

    for (let i = 1; i <= 7; i++) {
      const nextIndex = (currentDayIndex + i) % 7;
      const nextDaySchedule = businessHours.schedules.find(s => s.day === DAYS_OF_WEEK[nextIndex]);
      
      if (nextDaySchedule && !nextDaySchedule.isClosed && nextDaySchedule.openTime) {
        nextOpenDay = nextDaySchedule;
        daysUntilOpen = i;
        break;
      }
    }

    if (nextOpenDay) {
      return { 
        isOpen: false, 
        message: daysUntilOpen === 1 
          ? `Opens tomorrow at ${nextOpenDay.openTime}` 
          : `Opens ${nextOpenDay.day} at ${nextOpenDay.openTime}` 
      };
    } else {
      return { isOpen: false, message: 'No upcoming open hours found' };
    }
  }
}
