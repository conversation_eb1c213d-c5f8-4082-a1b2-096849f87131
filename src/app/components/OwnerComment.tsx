"use client";
import React, { useState, useCallback, useRef, useEffect } from "react";
import { iComment, iUser, iReview, iCommentVote } from "../util/Interfaces";
import dayjs from "dayjs";
import Link from "next/link";
import {
  SaveIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MessageSquareIcon,
  CheckCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import OptionsMenu from "./CommentOptionsMenu";
import { useAuth } from "@clerk/nextjs";
import { profileUrl } from "@/app/util/userHelpers";
import { toast } from "sonner";
import { Tooltip } from "@mantine/core";
import { cn } from "@/lib/utils";

interface OwnerCommentProps {
  comment: iComment;
  onReply: (parentId: string, body: string) => Promise<void>;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  onVote?: (voteType: "UP" | "DOWN") => Promise<void>;
  depth: number;
  clerkUserId: string;
  currentUser: iUser;
  productName?: string;
  review?: iReview;
}

const DEPTH_COLORS = [
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
  "border-blue-100",
  "border-blue-50",
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
];

const OwnerComment: React.FC<OwnerCommentProps> = ({
  comment: initialComment,
  onReply,
  onEdit,
  onDelete,
  onVote,
  depth = 0,
  clerkUserId,
  currentUser,
  productName,
  review,
}) => {
  const { userId } = useAuth();
  const [comment, setComment] = useState(initialComment);
  const [isEditing, setIsEditing] = useState(false);
  const [editedBody, setEditedBody] = useState(comment.body);
  const [showFullComment, setShowFullComment] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [replyBody, setReplyBody] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVoting, setIsVoting] = useState(false);
  const [replyCharCount, setReplyCharCount] = useState(0);
  const replyTextareaRef = useRef<HTMLTextAreaElement>(null);
  const MAX_REPLY_LENGTH = 1000;

  const isCommentOwner = clerkUserId === comment.user?.clerkUserId;
  const canReply = clerkUserId && clerkUserId !== comment.user?.clerkUserId;
  const depthColor = DEPTH_COLORS[depth % DEPTH_COLORS.length];
  const canVote = clerkUserId && !isCommentOwner && onVote;

  // Auto-focus reply textarea when replying starts
  useEffect(() => {
    if (isReplying && replyTextareaRef.current) {
      replyTextareaRef.current.focus();
    }
  }, [isReplying]);

  // Update character count when reply body changes
  useEffect(() => {
    setReplyCharCount(replyBody.length);
  }, [replyBody]);

  const handleReply = useCallback(async () => {
    if (!comment.id || !canReply) return;

    if (replyBody.trim().length === 0) {
      toast.error("Reply cannot be empty");
      return;
    }

    if (replyBody.length > MAX_REPLY_LENGTH) {
      toast.error(`Reply is too long. Maximum ${MAX_REPLY_LENGTH} characters allowed.`);
      return;
    }

    setIsSubmitting(true);
    try {
      await onReply(comment.id, replyBody);
      setIsReplying(false);
      setReplyBody("");
      setReplyCharCount(0);
      toast.success("Reply added successfully!");
    } catch (error) {
      console.error('Reply error:', error);
      if (error instanceof Error) {
        if (error.message.includes('401')) {
          toast.error("Please sign in to reply");
        } else if (error.message.includes('403')) {
          toast.error("You don't have permission to reply to this comment");
        } else {
          toast.error(error.message || "Failed to add reply. Please try again.");
        }
      } else {
        toast.error("Network error. Please check your connection.");
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [comment.id, canReply, replyBody, onReply]);

  const handleEdit = () => {
    if (comment.id && isCommentOwner) {
      setIsEditing(true);
    }
  };

  const handleSave = useCallback(async () => {
    if (!comment.id || !isCommentOwner) return;

    if (editedBody.trim().length === 0) {
      toast.error("Comment cannot be empty");
      return;
    }

    setIsSubmitting(true);
    try {
      await onEdit(comment.id, editedBody);
      const updatedComment = { ...comment, body: editedBody };
      setComment(updatedComment);
      setIsEditing(false);
      toast.success("Comment updated successfully!");
    } catch (error) {
      console.error('Edit error:', error);
      if (error instanceof Error) {
        toast.error(error.message || "Failed to update comment");
      } else {
        toast.error("Failed to update comment. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [comment, isCommentOwner, editedBody, onEdit]);

  const handleDelete = useCallback(async () => {
    if (!comment.id || !isCommentOwner) return;

    try {
      await onDelete(comment.id);
      const updatedComment = { ...comment, isDeleted: true };
      setComment(updatedComment);
      toast.success("Comment deleted successfully!");
    } catch (error) {
      console.error('Delete error:', error);
      if (error instanceof Error) {
        toast.error(error.message || "Failed to delete comment");
      } else {
        toast.error("Failed to delete comment. Please try again.");
      }
    }
  }, [comment, isCommentOwner, onDelete]);

  const handleVote = useCallback(async (voteType: "UP" | "DOWN") => {
    if (!canVote || isVoting) return;

    setIsVoting(true);
    try {
      await onVote!(voteType);
    } catch (error) {
      console.error('Vote error:', error);
      if (error instanceof Error) {
        if (error.message.includes('401')) {
          toast.error("Please sign in to vote");
        } else {
          toast.error(error.message || "Failed to process vote");
        }
      } else {
        toast.error("Network error. Please check your connection.");
      }
    } finally {
      setIsVoting(false);
    }
  }, [canVote, isVoting, onVote]);

  const handleCancelReply = useCallback(() => {
    setIsReplying(false);
    setReplyBody("");
    setReplyCharCount(0);
  }, []);

  return (
    <div
      id={comment.id || `comment-${Date.now()}`}
      className="w-full"
    >
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 md:p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center text-sm mb-2">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Avatar className="w-10 h-10">
                    <AvatarImage
                      src={comment.review?.product?.display_image || review?.product?.display_image || "/default-product.png"}
                      alt={`${comment.review?.product?.name || productName || 'Product'} logo`}
                    />
                    <AvatarFallback className="bg-blue-500 text-white text-xs">
                      {(comment.review?.product?.name || productName || 'P')?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  {/* <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-blue-500 rounded-full border border-white flex items-center justify-center">
                    <CheckCircleIcon className="w-2 h-2 text-white" />
                  </div> */}
                </div>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Owner
                  </span>
                </div>
              </div>
              <span className="mx-2 text-gray-400">•</span>
              <span className="text-gray-500 text-sm">
                {dayjs(comment.createdDate).format("MMM D, YYYY")}
              </span>
              {isCommentOwner && (
                <div className="ml-auto">
                  <OptionsMenu
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    setIsEditing={setIsEditing}
                  />
                </div>
              )}
            </div>
            <div className="text-sm leading-snug break-words">
              {isEditing ? (
                <Textarea
                  value={editedBody}
                  onChange={(e) => setEditedBody(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px] bg-white text-gray-900 placeholder-gray-500 text-sm"
                  placeholder="Share your thoughts as a verified business owner..."
                />
              ) : comment.isDeleted ? (
                <span className="italic text-gray-400 text-sm">
                  {comment.body}
                </span>
              ) : showFullComment || comment.body.length <= 200 ? (
                <div className="whitespace-pre-line text-gray-700 leading-snug text-sm">{comment.body}</div>
              ) : (
                <>
                  <div className="whitespace-pre-line text-gray-700 leading-snug text-sm">
                    {comment.body.slice(0, 200)}...
                  </div>
                  <button
                    onClick={() => setShowFullComment(true)}
                    className="text-blue-600 hover:text-blue-700 text-xs font-medium mt-2 underline flex items-center"
                  >
                    <span>Read more</span>
                    <ChevronDownIcon className="w-3 h-3 ml-1" />
                  </button>
                </>
              )}
            </div>
            {isEditing && (
              <div className="flex mt-4 space-x-2">
                <Button
                  onClick={handleSave}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  <SaveIcon className="w-4 h-4 mr-1" />
                  Save
                </Button>
                <Button
                  onClick={() => setIsEditing(false)}
                  size="sm"
                  variant="outline"
                  className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-md text-sm font-medium"
                >
                  Cancel
                </Button>
              </div>
            )}
            {!isEditing && canReply && (
               <div className="mt-4">
                 <Button
                   onClick={() => setIsReplying(!isReplying)}
                   size="sm"
                   variant="outline"
                   className="text-blue-600 hover:underline text-sm font-medium px-1 py-0"
                 >
                   Reply
                 </Button>
               </div>
             )}
            {isReplying && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <Textarea
                  ref={replyTextareaRef}
                  value={replyBody}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    if (newValue.length <= MAX_REPLY_LENGTH) {
                      setReplyBody(newValue);
                    }
                  }}
                  placeholder="Write a thoughtful reply to this verified business owner..."
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px] bg-white text-gray-900 placeholder-gray-500 text-sm resize-none"
                  maxLength={MAX_REPLY_LENGTH}
                  aria-label="Reply to owner comment"
                />
                <div className="flex justify-between items-center mt-1">
                  <span className={`text-xs ${
                    replyCharCount > MAX_REPLY_LENGTH * 0.9 ? 'text-red-500' : 'text-gray-500'
                  }`}>
                    {replyCharCount}/{MAX_REPLY_LENGTH}
                  </span>
                </div>
                <div className="flex justify-end mt-3 space-x-2">
                  <Button
                    onClick={handleReply}
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!replyBody.trim() || isSubmitting || replyBody.length > MAX_REPLY_LENGTH}
                    aria-label="Submit reply"
                  >
                    
                    {isSubmitting ? "Posting..." : "Post Reply"}
                  </Button>
                  <Button
                    onClick={handleCancelReply}
                    size="sm"
                    variant="outline"
                    className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-md text-sm font-medium"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
            
            {/* Voting section for owner comments */}
            {canVote && (
              <div className="flex justify-end mt-3 pt-3 border-t border-blue-200">
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "p-0 h-8 w-8 transition-colors",
                      isVoting && "opacity-50 cursor-not-allowed"
                    )}
                    onClick={() => handleVote("UP")}
                    disabled={isVoting}
                    aria-label="Upvote owner comment"
                  >
                    <ArrowUpIcon className="h-5 w-5" />
                  </Button>
                  <span className="text-sm font-medium min-w-[2ch] text-center">
                    {(comment.upvotes || 0) - (comment.downvotes || 0)}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "p-0 h-8 w-8 transition-colors",
                      isVoting && "opacity-50 cursor-not-allowed"
                    )}
                    onClick={() => handleVote("DOWN")}
                    disabled={isVoting}
                    aria-label="Downvote owner comment"
                  >
                    <ArrowDownIcon className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
  );
};

export default OwnerComment;
