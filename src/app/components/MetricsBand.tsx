"use client";

import { use<PERSON><PERSON> } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useEffect, useState } from "react";
import CountUp from "react-countup";

const MetricsBand = () => {
  const [allProducts] = useAtom(allProductsStore);
  const [stats, setStats] = useState({
    reviews: 0,
    products: 0,
    categories: 0,
  });

  useEffect(() => {
    if (allProducts && allProducts.length > 0) {
      // Calculate total reviews
      const totalReviews = allProducts.reduce((acc, product) => {
        return acc + (product._count?.reviews || 0);
      }, 0);

      // Derive unique categories from tags
      const uniqueCategories = new Set<string>();
      allProducts.forEach((product) => {
        product.tags?.forEach((tag) => uniqueCategories.add(tag));
      });

      setStats({
        reviews: totalReviews,
        products: allProducts.length,
        categories: uniqueCategories.size,
      });
    }
  }, [allProducts]);

  const metricClass =
    "flex flex-col items-center px-4 sm:px-6 md:px-8 lg:px-12";
  const labelClass = "mt-1 text-sm sm:text-base text-gray-200";

  return (
    <section className="bg-[rgb(var(--myTheme-navy))] py-10 sm:py-14">
      <div className="max-w-6xl mx-auto flex flex-wrap justify-center gap-8 sm:gap-12 text-white text-center">
        {/* Reviews */}
        <div className={metricClass}>
          <span className="text-3xl sm:text-4xl font-semibold">
            <CountUp end={stats.reviews} duration={2.5} separator="," />+
          </span>
          <span className={labelClass}>Reviews</span>
        </div>

        {/* Products */}
        <div className={metricClass}>
          <span className="text-3xl sm:text-4xl font-semibold">
            <CountUp end={stats.products} duration={2.5} separator="," />+
          </span>
          <span className={labelClass}>Products</span>
        </div>

        {/* Categories */}
        <div className={metricClass}>
          <span className="text-3xl sm:text-4xl font-semibold">
            <CountUp end={stats.categories} duration={2.5} separator="," />+
          </span>
          <span className={labelClass}>Categories</span>
        </div>
      </div>
    </section>
  );
};

export default MetricsBand;