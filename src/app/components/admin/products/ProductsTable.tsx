"use client";

import { useState } from "react";
import { format } from "date-fns";
import { iProduct } from "@/app/util/Interfaces";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";

interface ProductsTableProps {
  products: iProduct[];
  onViewDetails: (product: iProduct) => void;
  onEditProduct: (product: iProduct) => void;
  onDeleteProduct: (product: iProduct) => void;
  onToggleVisibility: (productId: string, currentVisibility: boolean) => Promise<void>;
}

export default function ProductsTable({
  products,
  onViewDetails,
  onEditProduct,
  onDeleteProduct,
  onToggleVisibility,
}: ProductsTableProps) {
  return (
    <div className="rounded-md border shadow-sm overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Visibility</TableHead>
            <TableHead>Rating</TableHead>
            <TableHead>Reviews</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product.id}>
              <TableCell>{product.name}</TableCell>
              <TableCell>
                {product.createdBy?.firstName} {product.createdBy?.lastName}
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={product.isPublic ?? true}
                    onCheckedChange={() => onToggleVisibility(product.id!, product.isPublic ?? true)}
                    aria-label="Toggle product visibility"
                  />
                  <span className="text-muted-foreground">
                    {product.isPublic ?? true ? 'Public' : 'Hidden'}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 mr-1" />
                  <span>{product.rating?.toFixed(1) || "N/A"}</span>
                </div>
              </TableCell>
              <TableCell>{product._count?.reviews || 0}</TableCell>
              <TableCell>
                <Badge
                  variant={product.isDeleted ? "destructive" : "outline"}
                  className={
                    product.isDeleted ? "bg-red-100 text-red-800 hover:bg-red-100 hover:text-red-800" : ""
                  }
                >
                  {product.isDeleted ? "Deleted" : "Active"}
                </Badge>
              </TableCell>
              <TableCell>
                {product.createdDate && format(new Date(product.createdDate), 'MMM d, yyyy')}
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewDetails(product)}
                  >
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditProduct(product)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => onDeleteProduct(product)}
                  >
                    Delete
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
