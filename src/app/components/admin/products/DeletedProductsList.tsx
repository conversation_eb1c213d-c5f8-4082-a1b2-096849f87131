"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { iProduct } from "@/app/util/Interfaces";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { calculateWeightedRating } from "@/app/util/calculateWeightedRating";
import RatingDisplayWithThreshold from "@/app/components/RatingDisplayWithThreshold";

// API function to fetch deleted products
const fetchDeletedProducts = async (page = 1, searchTerm = "") => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: "20", // Show more items since it's a simple listing
    sortBy: "updatedAt", // Sort by when they were deleted
    sortOrder: "desc",
    isDeleted: "true"
  });

  if (searchTerm) {
    params.append("search", searchTerm);
  }

  const response = await fetch(`/api/admin/products?${params.toString()}`);
  if (!response.ok) {
    throw new Error("Failed to fetch deleted products");
  }
  return response.json();
};

export default function DeletedProductsList() {
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch deleted products
  const { data, isLoading, isError } = useQuery({
    queryKey: ["deleted-products", page, searchTerm],
    queryFn: () => fetchDeletedProducts(page, searchTerm),
  });

  // Loading and error states
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Deleted Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Loading deleted products...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Deleted Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            Error loading deleted products
          </div>
        </CardContent>
      </Card>
    );
  }

  // Extract products and pagination from the response
  const products = data?.data?.products || [];
  const pagination = data?.data?.pagination || { page: 1, total: 0, totalPages: 1, limit: 20 };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="h-5 w-5" />
          Deleted Products ({pagination.total})
        </CardTitle>
        
        {/* Search */}
        <div className="flex items-center gap-2 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search deleted products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {products.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {searchTerm ? "No deleted products found matching your search." : "No deleted products found."}
          </div>
        ) : (
          <div className="space-y-3">
            {products.map((product: iProduct) => (
              <div
                key={product.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium truncate">{product.name}</h4>
                    <Badge variant="destructive" className="text-xs">
                      Deleted
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>Business: {product.business?.ownerName || 'Unknown'}</span>
                    <span>•</span>
                    <span>Created by: {product.createdBy?.userName || 'Unknown'}</span>
                    {product.updatedAt && (
                      <>
                        <span>•</span>
                        <span>Deleted: {format(new Date(product.updatedAt), 'MMM dd, yyyy')}</span>
                      </>
                    )}
                  </div>
                  
                  {product.description && (
                    <p className="text-sm text-muted-foreground mt-1 truncate">
                      {product.description.length > 100 
                        ? `${product.description.substring(0, 100)}...` 
                        : product.description
                      }
                    </p>
                  )}
                </div>
                
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <RatingDisplayWithThreshold
                    ratingData={calculateWeightedRating(
                      [], // No reviews data available in deleted products list
                      {
                        actualReviewCount: product._count?.reviews || 0
                      }
                    )}
                    size="xs"
                    showReviewCount={true}
                    showConfidence={false}
                    minimumReviewsMessage="Not enough reviews"
                    className="text-xs"
                  />
                </div>
              </div>
            ))}
          </div>
        )}
        
        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} deleted products
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}