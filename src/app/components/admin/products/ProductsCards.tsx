"use client";

import { format } from "date-fns";
import { iProduct } from "@/app/util/Interfaces";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";
import Image from "next/image";

interface ProductsCardsProps {
  products: iProduct[];
  onViewDetails: (product: iProduct) => void;
  onEditProduct: (product: iProduct) => void;
  onDeleteProduct: (product: iProduct) => void;
  onToggleVisibility: (productId: string, currentVisibility: boolean) => Promise<void>;
}

export default function ProductsCards({
  products,
  onViewDetails,
  onEditProduct,
  onDeleteProduct,
  onToggleVisibility,
}: ProductsCardsProps) {
  return (
    <div className="grid grid-cols-1 gap-4">
      {products.map((product) => (
        <Card key={product.id} className="overflow-hidden">
          <div className="flex items-start">
            {product.display_image && (
              <div className="w-24 h-24 relative">
                <Image
                  src={product.display_image}
                  alt={product.name || "Product image"}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div className="flex-1">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{product.name}</CardTitle>
                    <CardDescription>
                      By {product.createdBy?.firstName} {product.createdBy?.lastName}
                    </CardDescription>
                  </div>
                  <Badge
                    variant={product.isDeleted ? "destructive" : "outline"}
                    className={
                      product.isDeleted ? "bg-red-100 text-red-800 hover:bg-red-100 hover:text-red-800" : ""
                    }
                  >
                    {product.isDeleted ? "Deleted" : "Active"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 mr-1" />
                    <span className="mr-2">{product.rating?.toFixed(1) || "N/A"}</span>
                    <span className="text-muted-foreground text-sm">
                      ({product._count?.reviews || 0} reviews)
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {product.createdDate && format(new Date(product.createdDate), "MMM d, yyyy")}
                  </div>
                </div>
                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-sm">Visibility:</span>
                  <Switch
                    checked={product.isPublic ?? true}
                    onCheckedChange={() => onToggleVisibility(product.id!, product.isPublic ?? true)}
                    aria-label="Toggle product visibility"
                  />
                  <span className="text-sm text-muted-foreground">
                    {product.isPublic ?? true ? "Public" : "Hidden"}
                  </span>
                </div>
              </CardContent>
              <CardFooter className="pt-0 flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewDetails(product)}
                >
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditProduct(product)}
                >
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-500 hover:text-red-700"
                  onClick={() => onDeleteProduct(product)}
                >
                  Delete
                </Button>
              </CardFooter>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
