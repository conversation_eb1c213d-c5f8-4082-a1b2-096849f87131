"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle, CheckCircle2, Store } from "lucide-react";
import { iProduct } from "../util/Interfaces";

interface ProductSubmissionConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  product: iProduct;
  isLoading?: boolean;
}

export default function ProductSubmissionConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  product,
  isLoading = false,
}: ProductSubmissionConfirmModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl font-semibold text-myTheme-primary">
            <Store className="h-6 w-6 mr-3 text-myTheme-primary" />
            Confirm Product Submission
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 mt-2">
            Please review and confirm the following before submitting your business listing.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Product Summary */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
            <h3 className="font-semibold text-myTheme-primary mb-2">You're about to submit:</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">Business:</span> {product.name}</p>
              <p><span className="font-medium">Location:</span> {product.streetAddress || product.address || "Not specified"}</p>
              <p><span className="font-medium">Categories:</span> {product.tags?.length || 0} tags</p>
            </div>
          </div>

          {/* Confirmation Checklist */}
          <div className="space-y-3">
            <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-yellow-800 mb-1">Quality Guidelines</p>
                <p className="text-yellow-700">
                  To the best of my knowledge, this is a legitimate business listing and not spam, duplicate content, or misleading information.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg border border-green-200">
              <CheckCircle2 className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-green-800 mb-1">Information Accuracy</p>
                <p className="text-green-700">
                  To the best of my knowledge, the information provided (contact details, hours, location) is accurate and current.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <CheckCircle2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 mb-1">Content Usage</p>
                <p className="text-blue-700">
                  I believe I have the right to use the uploaded images and content, or they are publicly available for this purpose.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg text-xs text-gray-600">
            <p>
              By submitting, you agree to our Terms of Service and acknowledge that your listing will be reviewed before going live.
            </p>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isLoading}
            className="w-full sm:w-auto bg-gradient-to-r from-myTheme-primary to-myTheme-secondary hover:from-myTheme-primary/90 hover:to-myTheme-secondary/90"
          >
            {isLoading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </div>
            ) : (
              "Yes, Submit Product"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}