import React from 'react';
import { ReadOnlyRating } from './RatingSystem';
import { WeightedRatingResult } from '../util/calculateWeightedRating';
import { iCalculatedRating } from '../util/Interfaces';
import { MINIMUM_REVIEWS } from '@/app/config/rating';
import { formatRatingDisplay } from '../util/ratingUtils';

type RatingSize = "xs" | "sm" | "md" | "lg" | "xl";

interface RatingDisplayWithThresholdProps {
  ratingData: WeightedRatingResult | iCalculatedRating;
  name?: string;
  size?: RatingSize | "rating-xs" | "rating-sm" | "rating-lg";
  showReviewCount?: boolean;
  showConfidence?: boolean;
  minimumReviewsMessage?: string;
  className?: string;
}

const RatingDisplayWithThreshold: React.FC<RatingDisplayWithThresholdProps> = ({
  ratingData,
  size = "sm",
  showReviewCount = true,
  showConfidence = true,
  minimumReviewsMessage = "Not enough reviews yet",
  className = "",
  name = "rating"
}) => {
  // Convert legacy size values to new format
  const normalizedSize: RatingSize = 
    size === "rating-sm" ? "sm" : 
    size === "rating-lg" ? "lg" : 
    size === "rating-xs" ? "xs" : 
    size as RatingSize;
  const {
    displayRating,
    roundedRating,
    roundedRatingOneDecimalPlace,
    numberOfReviews,
    hasMinimumReviews,
    confidence
  } = ratingData;

  const minimumRequired = MINIMUM_REVIEWS;

  // Use displayRating for star display (supports half stars) and format for text display
  const starRating = hasMinimumReviews ? (displayRating || 0) : 0;
  const textRating = hasMinimumReviews ? formatRatingDisplay(displayRating || 0) : '';

  return (
    <div className={`flex flex-col space-y-1 ${className}`}>
      <div className="flex items-center space-x-2">
        {hasMinimumReviews ? (
          <>
            <ReadOnlyRating
              name={name}
              rating={starRating}
              size={normalizedSize}
            />
            <span className="text-sm font-medium text-gray-700">
              {textRating}
            </span>
          </>
        ) : (
          <>
            <div className="flex items-center space-x-1">
              <ReadOnlyRating
                name={name}
                rating={0}
                size={normalizedSize}
              />
              <span className="text-sm text-gray-400">
                {minimumReviewsMessage}
              </span>
            </div>
          </>
        )}
      </div>
      
      {showReviewCount && (
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">
            {numberOfReviews} review{numberOfReviews !== 1 ? 's' : ''}
          </span>
          {!hasMinimumReviews && numberOfReviews > 0 && (
            <span className="text-xs text-amber-600">
              ({minimumRequired - numberOfReviews} more needed)
            </span>
          )}
          {hasMinimumReviews && showConfidence && (
            <span className={`text-xs px-2 py-1 rounded-full ${
              confidence === 'high' ? 'bg-green-100 text-green-800' :
              confidence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-gray-100 text-gray-600'
            }`}>
              {confidence} confidence
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default RatingDisplayWithThreshold;