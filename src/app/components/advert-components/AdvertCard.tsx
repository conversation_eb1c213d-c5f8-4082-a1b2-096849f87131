import React from 'react';
import Image from 'next/image';
import { ExternalLink, Sparkles } from 'lucide-react';

interface AdvertCardProps {
    title: string;
    description: string;
    imageUrl: string;
    link: string;
    isSponsored?: boolean;
}

const AdvertCard: React.FC<AdvertCardProps> = ({ title, description, imageUrl, link, isSponsored }) => {
    return (
        <a 
            href={link}
            target="_blank"
            rel="noopener noreferrer"
            className="group block bg-white border border-gray-200 rounded-xl overflow-hidden hover:border-myTheme-primary/30 transition-all duration-200 hover:shadow-md"
        >
            {/* Header with subtle ad indicator */}
            <div className="px-4 pt-4 flex justify-between items-start">
                <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                    <span className="text-xs font-medium text-gray-500">Promoted</span>
                </div>
                {isSponsored && (
                    <div className="flex items-center gap-1 bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                        <Sparkles className="w-3 h-3" />
                        <span>Ad</span>
                    </div>
                )}
            </div>
            
            {/* Image */}
            <div className="relative w-full h-40 mt-2 mx-4 rounded-lg overflow-hidden bg-gray-50">
                <Image
                    src={imageUrl}
                    alt={title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
            </div>
            
            {/* Content */}
            <div className="p-4 pt-3">
                <h3 className="font-semibold text-gray-900 group-hover:text-myTheme-primary line-clamp-2 mb-1">
                    {title}
                </h3>
                <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                    {description}
                </p>
                <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-myTheme-primary bg-myTheme-primary/10 px-2 py-1 rounded-full">
                        View Deal
                    </span>
                    <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-myTheme-primary" />
                </div>
            </div>
        </a>
    );
};

export default AdvertCard;
