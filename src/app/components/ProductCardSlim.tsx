"use client";
import { iProduct, iReview } from "@/app/util/Interfaces";
import Image from "next/legacy/image";
import { ReadOnlyRating } from "./RatingSystem";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import { getReviews } from "../util/serverFunctions";
import { calculateAverageReviewRatingSync } from "../util/calculateAverageReviewRating";
import RatingDisplayWithThreshold from './RatingDisplayWithThreshold';
import { WeightedRatingResult } from '../util/calculateWeightedRating';
import LoadingSpinner from "./LoadingSpinner";
import { motion } from "framer-motion";
import { MdLocationOn, MdPhone } from "react-icons/md";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle } from "lucide-react";
import { MINIMUM_REVIEWS } from "@/app/config/rating";

interface ProductCardProps {
  product: iProduct;
  options: {
    size: string;
  };
}

interface iCalculatedRating {
  roundedRating: number;
  roundedRatingOneDecimalPlace: string;
  numberOfReviews: number;
}

const ratingColors = {
  1: "bg-red-500",
  2: "bg-orange-500",
  3: "bg-yellow-500",
  4: "bg-lime-500",
  5: "bg-green-500",
};

const ProductCardSlim: React.FC<ProductCardProps> = ({ product, options }) => {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["reviews", product.id],
    queryFn: () => getReviews(product.id!),
    refetchOnWindowFocus: false,
  });

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <p className="text-sm text-red-500">{error.message}</p>;
  if (!data?.success || !data?.data)
    return <p className="text-sm text-red-500">Failed to load reviews</p>;

  const reviews = data.data.reviews;
  
  // Check if product already has weighted rating from API
  const hasWeightedRating = product.weightedRating && typeof product.weightedRating === 'object' && 'hasMinimumReviews' in product.weightedRating;
  
  const ratingData = hasWeightedRating 
    ? product.weightedRating 
    : calculateAverageReviewRatingSync(reviews, true);

  if (!ratingData) return null;
  const { roundedRating, roundedRatingOneDecimalPlace, numberOfReviews } = ratingData;

  const cardVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
    hover: {
      y: -2,
      transition: {
        duration: 0.15,
        ease: "easeInOut",
      },
    },
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className="w-full"
    >
      <Link href={`/reviews?id=${product.id}`}>
        <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200/60 p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
          <div className="flex gap-3">
            {/* Image */}
            {product.display_image && (
              <div className="flex-shrink-0">
                <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                  <Image
                    src={product.display_image}
                    alt={`${product.name} Image`}
                    layout="fill"
                    objectFit="cover"
                    className="transition-transform duration-200 group-hover:scale-105"
                  />
                </div>
              </div>
            )}

            {/* Content */}
            <div className="flex-grow min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-1">
                <div className="max-w-full overflow-hidden">
                  <h3 className="font-medium text-gray-900 line-clamp-1">
                    {product.name}
                  </h3>
                  <p className="text-sm text-gray-500 line-clamp-2">
                    {product.description}
                  </p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {(product.tags ?? []).slice(0, 3).map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs truncate max-w-[120px]"
                      >
                        {tag}
                      </Badge>
                    ))}
                    {(product.tags?.length ?? 0) > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{(product.tags?.length ?? 0) - 3}
                      </Badge>
                    )}
                    <Badge
                      variant={product.hasOwner ? "destructive" : "default"}
                      className="text-xs flex items-center gap-1"
                    >
                      {product.hasOwner ? (
                        <>
                          <XCircle className="h-3 w-3" />
                          <span>Has Owner</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-3 w-3" />
                          <span>Available</span>
                        </>
                      )}
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center gap-2 sm:flex-col sm:items-end flex-shrink-0">
                  {ratingData && 'hasMinimumReviews' in ratingData && ratingData.hasMinimumReviews ? (
                    <RatingDisplayWithThreshold
                      ratingData={ratingData as WeightedRatingResult}
                      size={options.size as any}
                      showReviewCount={true}
                      showConfidence={false}
                      className="text-right"
                    />
                  ) : (
                    <div className="flex flex-col items-end">
                      <div className="flex items-center gap-1">
                        <ReadOnlyRating
                          name={`rating-${product.id}`}
                          rating={0}
                          size="lg"
                        />
                        <span className="text-sm text-gray-500">
                          ({numberOfReviews})
                        </span>
                      </div>
                      <span className="text-xs text-gray-400">
                        ({MINIMUM_REVIEWS - numberOfReviews} more needed for rating)
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default ProductCardSlim;
