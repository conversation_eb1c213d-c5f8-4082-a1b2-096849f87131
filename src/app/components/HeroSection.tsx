"use client";

import Image from "next/image";
import SearchBoxAndListener from "./SearchBoxAndListener";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

import { useAtom } from "jotai";
import { allProductsStore } from "@/app/store/store";
import { useEffect, useState, useMemo } from "react";
import { ChevronRight, Star, TrendingUp, Users } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getPopularReviewsForHero } from "../util/serverFunctions";
import { iReview } from "../util/Interfaces";
import { useWindowSize } from "../hooks/useWindowSize";

// Pre-define static positions to avoid recreating arrays on each render
const MOBILE_POSITIONS = [
  { top: '10%', left: '10%', delay: 0 },
  { top: '20%', right: '10%', delay: 0.2 },
  { bottom: '20%', left: '10%', delay: 0.4 },
  { bottom: '10%', right: '10%', delay: 0.6 },
];

const DESKTOP_POSITIONS = [
  { top: '15%', left: '10%', delay: 0 },
  { top: '30%', right: '10%', delay: 0.2 },
  { bottom: '30%', left: '10%', delay: 0.4 },
  { bottom: '15%', right: '10%', delay: 0.6 },
];

// Pre-define stats configuration
const STATS_CONFIG = [
  { label: "Reviews", icon: Star, key: "reviews" },
  { label: "Products", icon: TrendingUp, key: "products" },
  { label: "Categories", icon: Users, key: "categories" }
];

// Memoized star rating component to avoid recreating on each render
const StarRating = ({ rating }: { rating: number }) => {
  // Create array once and reuse
  const stars = useMemo(() => [...Array(5)], []);
  
  return (
    <div className="flex">
      {stars.map((_, i) => (
        <Star
          key={i}
          className={`h-3 w-3 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
        />
      ))}
    </div>
  );
};

// Separate review card component to improve readability and optimization
const ReviewCard = ({
  review,
  position,
  isMobile,
}: {
  review: iReview;
  position: any;
  isMobile: boolean;
}) => {
  const cardWidth = isMobile ? "150px" : "200px";
  const { delay, ...styleProps } = position;

  const businessName = review.product?.business?.ownerName || review.product?.name;
  const productName = review.product?.name;

  return (
    <Link href={`/fr?id=${review.id}&productid=${review.productId}`} passHref>
      <div
        key={review.id}
        className="absolute z-10 bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
        style={{
          ...styleProps,
          width: cardWidth,
        }}
      >
        <div className="flex flex-col gap-1">
          <p className="font-bold text-sm text-white truncate">{businessName}</p>
          <div className="flex items-center gap-1">
            <StarRating rating={review.rating} />
            <span className="text-xs text-white/80">{review.rating.toFixed(1)}</span>
          </div>
          <p className="text-xs text-white font-medium line-clamp-2">{review.title}</p>
          {productName && businessName !== productName && (
            <p className="text-[10px] text-white/60 truncate">re: {productName}</p>
          )}
        </div>
      </div>
    </Link>
  );
};

const HeroSection = () => {
  const [allProducts] = useAtom(allProductsStore);
  const [stats, setStats] = useState({
    reviews: 0,
    products: 0,
    categories: 0
  });


  
  // Fetch popular reviews for hero section with 10-minute cache
  const { data: popularReviews } = useQuery({
    queryKey: ["heroPopularReviews"],
    queryFn: getPopularReviewsForHero,
    staleTime: 10 * 60 * 1000, // 10 minutes - data stays fresh
    gcTime: 15 * 60 * 1000, // 15 minutes - keep in cache longer than stale time
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Don't refetch on component mount if data is fresh
    refetchOnReconnect: false, // Don't refetch on reconnect if data is fresh
    select: (data) => data?.data?.slice(0, 4) || [], // Take only first 4 reviews
  });
  


  const { width } = useWindowSize();
  const isMobile = width < 768;



  // Memoize stats calculation to avoid recalculating on every render
  useEffect(() => {
    if (!allProducts?.length) return;
    
    // Debounce the calculation to avoid excessive processing
    const timer = setTimeout(() => {
      const totalReviews = allProducts.reduce((acc, product) => {
        return acc + (product._count?.reviews || 0);
      }, 0);

      const uniqueCategories = new Set<string>();
      allProducts.forEach(product => {
        product.tags?.forEach(tag => uniqueCategories.add(tag));
      });

      setStats({
        reviews: totalReviews,
        products: allProducts.length,
        categories: uniqueCategories.size
      });
    }, 100);
    
    return () => clearTimeout(timer);
  }, [allProducts]);





  return (
    <div className="relative w-full overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 min-h-screen">
      <Image
        src="/map_edited.avif"
        alt="A faint map of the guyana in the background"
        fill
        className="object-cover opacity-10"
        priority
        loading="eager"
      />

      {/* Simplified background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff0a_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0a_1px,transparent_1px)] bg-[size:100px_100px]" />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            {/* Left side - Content */}
            <div className="space-y-8 text-center lg:pr-8">
              <div className="space-y-4">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-xs sm:text-sm text-white/80 font-medium">
                  <span className="w-2 h-2 bg-green-400 rounded-full" />
                  Live reviews from real Guyanese
                  <div className="relative w-10 h-10 ml-2 rounded-full overflow-hidden border-2 border-white/30 shadow-md">
                    <Image 
                      src="/one-guyana-small.webp" 
                      alt="Guyana Tribute" 
                      fill
                      sizes="40px"
                      className="object-cover"
                    />
                  </div>
                </div>

                <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black text-white leading-tight">
                  Your Voice,{" "}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                    Your Choice
                  </span>
                </h1>

                <p className="text-base sm:text-lg text-gray-300 max-w-lg mx-auto">
                ReviewIt (ReviewIt.gy) - Empowering Guyanese consumers with reliable reviews and giving businesses a chance to exercise excellence in customer service. Ah place where all-a-wee could talk to businesses and they could talk to we. <br />
🚘🥨🥞🍕🍟🐕
                </p>
              </div>

              {/* Search Box */}
              <div className="w-full mx-auto lg:mx-0">
                <div className="flex justify-center w-full">
                  <div className="w-full max-w-2xl">
                    <SearchBoxAndListener />
                  </div>
                </div>
              </div>

              {/* CTA Buttons - Only render when needed */}
              {!isMobile && (
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-8 py-6 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                    asChild
                  >
                    <Link href="/write-review" prefetch={false}>
                      Write a Review
                      <ChevronRight className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white/20 text-white bg-white/5 hover:bg-white/10 hover:text-white px-8 py-6 text-lg rounded-xl transition-all duration-300"
                    asChild
                  >
                    <Link href="/reviews-explore" prefetch={false}>Browse Reviews</Link>
                  </Button>
                </div>
              )}

              {/* Stats */}
              <div className="flex flex-wrap justify-center gap-6 pt-2 md:pt-2">
                {STATS_CONFIG.map((stat, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="p-2 bg-white/10 rounded-lg">
                      <stat.icon className="h-5 w-5 text-white/80" />
                    </div>
                    <div>
                      <div className="text-xl sm:text-2xl lg:text-3xl font-extrabold text-white">
                        {stats[stat.key as keyof typeof stats].toLocaleString()}+
                      </div>
                      <div className="text-xs sm:text-sm text-gray-400 font-medium">{stat.label}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right side - Visual */}
            <div className="relative lg:pl-8 overflow-hidden">
              <div className={`relative ${isMobile ? 'w-full max-w-lg h-[380px] mx-auto overflow-visible' : 'w-full max-w-lg h-[600px] mx-auto overflow-hidden'}`}>
                {/* Floating cards - Real Reviews Only - Only render when data is available */}
                {popularReviews && popularReviews.length > 0 && popularReviews.map((review: iReview, index: number) => (
                  <ReviewCard
                    key={review.id}
                    review={review}
                    position={isMobile ? MOBILE_POSITIONS[index] : DESKTOP_POSITIONS[index]}
                    isMobile={isMobile}
                  />
                ))}

                {/* Central visual element */}
                <div className="absolute inset-0 flex items-center justify-center z-0">
                  <div className={`relative ${isMobile ? 'w-24 h-24' : 'w-48 h-48'}`}>
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/15 to-purple-500/15 rounded-full blur-xl" />
                    <div className="relative w-full h-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full flex items-center justify-center">
                      <div className="text-center">
                        <Image
                          src="/logo.avif"
                          alt="ReviewIt"
                          width={isMobile ? 70 : 140}
                          height={isMobile ? 70 : 140}
                          className="rounded-full"
                          priority
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;