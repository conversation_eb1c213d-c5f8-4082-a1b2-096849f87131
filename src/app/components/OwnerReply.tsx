"use client";
import React, { useState, useCallback } from "react";
import { iComment, iUser, iReview } from "../util/Interfaces";
import dayjs from "dayjs";
import Link from "next/link";
import {
  SaveIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CheckCircleIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import OptionsMenu from "./CommentOptionsMenu";
import { useAuth } from "@clerk/nextjs";
import { profileUrl } from "@/app/util/userHelpers";
import { toast } from "sonner";
import { Tooltip } from "@mantine/core";

interface OwnerReplyProps {
  comment: iComment;
  onReply: (parentId: string, body: string) => Promise<void>;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  depth: number;
  clerkUserId: string;
  currentUser: iUser;
  productName?: string;
  review?: iReview;
}

const DEPTH_COLORS = [
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
  "border-blue-100",
  "border-blue-50",
  "border-blue-400",
  "border-blue-300",
  "border-blue-200",
];

const OwnerReply: React.FC<OwnerReplyProps> = ({
  comment: initialComment,
  onReply,
  onEdit,
  onDelete,
  depth = 0,
  clerkUserId,
  currentUser,
  productName,
  review,
}) => {
  const { userId } = useAuth();
  const [comment, setComment] = useState(initialComment);
  const product = comment.review?.product || review?.product;
  const [isEditing, setIsEditing] = useState(false);
  const [editedBody, setEditedBody] = useState(comment.body);
  const [showFullComment, setShowFullComment] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isCommentOwner = clerkUserId === comment.user?.clerkUserId;
  const depthColor = DEPTH_COLORS[depth % DEPTH_COLORS.length];



  const handleEdit = () => {
    if (comment.id && isCommentOwner) {
      setIsEditing(true);
    }
  };

  const handleSave = useCallback(async () => {
    if (!comment.id || !isCommentOwner) return;

    if (editedBody.trim().length === 0) {
      toast.error("Reply cannot be empty");
      return;
    }

    setIsSubmitting(true);
    try {
      await onEdit(comment.id, editedBody);
      const updatedComment = { ...comment, body: editedBody };
      setComment(updatedComment);
      setIsEditing(false);
      toast.success("Reply updated successfully!");
    } catch (error) {
      console.error('Edit error:', error);
      if (error instanceof Error) {
        toast.error(error.message || "Failed to update reply");
      } else {
        toast.error("Failed to update reply. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [comment, isCommentOwner, editedBody, onEdit]);

  const handleDelete = useCallback(async () => {
    if (!comment.id || !isCommentOwner) return;

    try {
      await onDelete(comment.id);
      const updatedComment = { ...comment, isDeleted: true };
      setComment(updatedComment);
      toast.success("Reply deleted successfully!");
    } catch (error) {
      console.error('Delete error:', error);
      if (error instanceof Error) {
        toast.error(error.message || "Failed to delete reply");
      } else {
        toast.error("Failed to delete reply. Please try again.");
      }
    }
  }, [comment, isCommentOwner, onDelete]);

  return (
    <div
      id={comment.id || `reply-${Date.now()}`}
      className="w-full mt-3 ml-6"
    >
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center text-sm mb-3">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Avatar className="w-8 h-8">
                    <AvatarImage
                      src={product?.display_image || "/default-product.png"}
                      alt={`${product?.name || productName || 'Product'} logo`}
                    />
                    <AvatarFallback className="bg-blue-500 text-white text-xs">
                      {(product?.name || productName || 'Product')?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-blue-500 rounded-full border border-white flex items-center justify-center">
                    <CheckCircleIcon className="w-2 h-2 text-white" />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Owner
                  </span>
                </div>
              </div>
              <span className="mx-2 text-gray-400">•</span>
              <span className="text-gray-500 text-sm">
                {dayjs(comment.createdDate).format("MMM D, YYYY")}
              </span>
              {isCommentOwner && (
                <div className="ml-auto">
                  <OptionsMenu
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    setIsEditing={setIsEditing}
                  />
                </div>
              )}
            </div>
            <div className="text-sm leading-relaxed break-words">
              {isEditing ? (
                <Textarea
                  value={editedBody}
                  onChange={(e) => setEditedBody(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px] bg-white text-gray-900 placeholder-gray-500 text-sm"
                  placeholder="Share your thoughts as a verified business owner..."
                />
              ) : comment.isDeleted ? (
                <span className="italic text-gray-400 text-sm">
                  {comment.body}
                </span>
              ) : showFullComment || comment.body.length <= 200 ? (
                <div className="whitespace-pre-line text-gray-700 leading-relaxed text-sm">{comment.body}</div>
              ) : (
                <>
                  <div className="whitespace-pre-line text-gray-700 leading-relaxed text-sm">
                    {comment.body.slice(0, 200)}...
                  </div>
                  <button
                    onClick={() => setShowFullComment(true)}
                    className="text-blue-600 hover:text-blue-700 text-xs font-medium mt-2 underline flex items-center"
                  >
                    <span>Read more</span>
                    <ChevronDownIcon className="w-3 h-3 ml-1" />
                  </button>
                </>
              )}
            </div>
            {isEditing && (
              <div className="flex mt-4 space-x-2">
                <Button
                  onClick={handleSave}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!editedBody.trim() || isSubmitting}
                  aria-label="Save reply changes"
                >
                  <SaveIcon className="w-4 h-4 mr-1" />
                  {isSubmitting ? "Saving..." : "Save"}
                </Button>
                <Button
                  onClick={() => setIsEditing(false)}
                  size="sm"
                  variant="outline"
                  className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-md text-sm font-medium"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </div>
            )}
          </div>
        </div>
  );
};

export default OwnerReply;
