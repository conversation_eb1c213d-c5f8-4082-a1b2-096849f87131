"use client";

import React, { useState, useEffect } from "react";
import { BusinessHours, DaySchedule, DAYS_OF_WEEK, createDefaultBusinessHours } from "../types/businessHours";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { showToast } from "@/app/util/toastClient";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Copy, Trash2, Clock, Calendar } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface BusinessHoursInputProps {
  value: BusinessHours | null | undefined;
  onChange: (hours: BusinessHours) => void;
}

export default function BusinessHoursInput({ value, onChange }: BusinessHoursInputProps) {
  // Initialize with default hours if none provided
  const [businessHours, setBusinessHours] = useState<BusinessHours>(
    value || createDefaultBusinessHours()
  );

  // Using a ref to track if we're in the middle of an update
  const isUpdating = React.useRef(false);

  // Only update local state from props on initial mount or when props actually change
  useEffect(() => {
    if (value && !isUpdating.current) {
      // Deep compare to avoid unnecessary updates
      if (JSON.stringify(value) !== JSON.stringify(businessHours)) {
        setBusinessHours(value);
      }
    }
  }, [value, businessHours]);

  // Handle schedule changes for a specific day
  const handleScheduleChange = (index: number, field: keyof DaySchedule, newValue: any) => {
    const updatedSchedules = [...businessHours.schedules];
    updatedSchedules[index] = {
      ...updatedSchedules[index],
      [field]: newValue
    };
    
    const newBusinessHours = { ...businessHours, schedules: updatedSchedules };
    setBusinessHours(newBusinessHours);
    
    // Directly call onChange instead of relying on the effect
    isUpdating.current = true;
    onChange(newBusinessHours);
    // Reset the flag after a short delay to allow React to process the update
    setTimeout(() => {
      isUpdating.current = false;
    }, 0);
  };

  // Handle holiday setting toggle
  const handleHolidayToggle = (open: boolean) => {
    const newBusinessHours = { ...businessHours, openOnHolidays: open };
    setBusinessHours(newBusinessHours);
    
    // Directly call onChange instead of relying on the effect
    isUpdating.current = true;
    onChange(newBusinessHours);
    setTimeout(() => {
      isUpdating.current = false;
    }, 0);
  };

  // Copy hours from one day to others
  const copyHoursFromDay = (sourceIndex: number, targetIndices: number[]) => {
    const sourceSchedule = businessHours.schedules[sourceIndex];
    const updatedSchedules = [...businessHours.schedules];
    
    targetIndices.forEach(targetIndex => {
      updatedSchedules[targetIndex] = {
        ...updatedSchedules[targetIndex],
        isClosed: sourceSchedule.isClosed,
        openTime: sourceSchedule.openTime,
        closeTime: sourceSchedule.closeTime
      };
    });
    
    const newBusinessHours = { ...businessHours, schedules: updatedSchedules };
    setBusinessHours(newBusinessHours);
    
    // Directly call onChange instead of relying on the effect
    isUpdating.current = true;
    onChange(newBusinessHours);
    setTimeout(() => {
      isUpdating.current = false;
    }, 0);
  };

  // Quick actions for common patterns
  const applyWeekdayHours = (openTime: string, closeTime: string) => {
    const updatedSchedules = [...businessHours.schedules];
    
    // Update Monday-Friday
    [0, 1, 2, 3, 4].forEach(index => {
      updatedSchedules[index] = {
        ...updatedSchedules[index],
        isClosed: false,
        openTime,
        closeTime
      };
    });
    
    const newBusinessHours = { ...businessHours, schedules: updatedSchedules };
    setBusinessHours(newBusinessHours);
    
    // Directly call onChange instead of relying on the effect
    isUpdating.current = true;
    onChange(newBusinessHours);
    setTimeout(() => {
      isUpdating.current = false;
    }, 0);
  };

  const applyWeekendHours = (openTime: string, closeTime: string) => {
    const updatedSchedules = [...businessHours.schedules];
    
    // Update Saturday-Sunday
    [5, 6].forEach(index => {
      updatedSchedules[index] = {
        ...updatedSchedules[index],
        isClosed: false,
        openTime,
        closeTime
      };
    });
    
    const newBusinessHours = { ...businessHours, schedules: updatedSchedules };
    setBusinessHours(newBusinessHours);
    
    // Directly call onChange instead of relying on the effect
    isUpdating.current = true;
    onChange(newBusinessHours);
    setTimeout(() => {
      isUpdating.current = false;
    }, 0);
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="daily" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="daily" className="flex items-center justify-center">
            <Calendar className="h-4 w-4 mr-1 sm:mr-2" />
            <span className="text-xs sm:text-sm">Daily Schedule</span>
          </TabsTrigger>
          <TabsTrigger value="quick" className="flex items-center justify-center">
            <Clock className="h-4 w-4 mr-1 sm:mr-2" />
            <span className="text-xs sm:text-sm">Quick Setup</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="daily" className="space-y-4">
          {businessHours.schedules.map((schedule, index) => (
            <div 
              key={schedule.day}
              className={`p-3 sm:p-4 rounded-lg border ${
                schedule.isClosed 
                  ? 'bg-gray-50 border-gray-200' 
                  : 'bg-white border-orange-200'
              }`}
            >
              <div className="flex flex-wrap items-center justify-between mb-3">
                <div className="flex items-center mb-2 sm:mb-0">
                  <div 
                    className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full mr-2 ${
                      schedule.isClosed ? 'bg-gray-400' : 'bg-green-500'
                    }`}
                  ></div>
                  <Label className="text-base sm:text-lg font-medium">{schedule.day}</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Label htmlFor={`closed-${index}`} className="text-xs sm:text-sm text-gray-600">
                    {schedule.isClosed ? 'Closed' : 'Open'}
                  </Label>
                  <Switch
                    id={`closed-${index}`}
                    checked={!schedule.isClosed}
                    onCheckedChange={(checked) => handleScheduleChange(index, 'isClosed', !checked)}
                  />
                </div>
              </div>
              
              {!schedule.isClosed && (
                <div className="grid grid-cols-2 gap-2 sm:gap-4">
                  <div>
                    <Label htmlFor={`open-${index}`} className="text-xs sm:text-sm text-gray-600">
                      Opening
                    </Label>
                    <Input
                      id={`open-${index}`}
                      type="time"
                      value={schedule.openTime || ''}
                      onChange={(e) => handleScheduleChange(index, 'openTime', e.target.value)}
                      className="mt-1 border-gray-200 h-8 sm:h-10 text-xs sm:text-sm px-2 sm:px-3"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`close-${index}`} className="text-xs sm:text-sm text-gray-600">
                      Closing
                    </Label>
                    <Input
                      id={`close-${index}`}
                      type="time"
                      value={schedule.closeTime || ''}
                      onChange={(e) => handleScheduleChange(index, 'closeTime', e.target.value)}
                      className="mt-1 border-gray-200 h-8 sm:h-10 text-xs sm:text-sm px-2 sm:px-3"
                    />
                  </div>
                </div>
              )}
              
              {index > 0 && !schedule.isClosed && (
                <div className="mt-2 sm:mt-3 flex justify-end">
                  <Select 
                    onValueChange={(value) => {
                      if (value === "copy-previous") {
                        copyHoursFromDay(index - 1, [index]);
                      } else if (value === "copy-monday") {
                        copyHoursFromDay(0, [index]);
                      }
                    }}
                  >
                    <SelectTrigger className="w-[140px] sm:w-[180px] h-7 sm:h-8 text-xs">
                      <SelectValue placeholder="Copy hours..." />
                    </SelectTrigger>
                    <SelectContent>
                      {index > 0 && (
                        <SelectItem value="copy-previous">
                          Copy from {businessHours.schedules[index - 1].day}
                        </SelectItem>
                      )}
                      {index !== 0 && (
                        <SelectItem value="copy-monday">
                          Copy from Monday
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          ))}
        </TabsContent>
        
        <TabsContent value="quick" className="space-y-4 sm:space-y-6">
          <div className="bg-white p-3 sm:p-4 rounded-lg border border-orange-200">
            <h3 className="text-sm sm:text-base font-medium mb-2 sm:mb-3">Weekday Hours (Mon-Fri)</h3>
            <div className="grid grid-cols-2 gap-2 sm:gap-4">
              <div>
                <Label className="text-xs sm:text-sm text-gray-600">Opening</Label>
                <Input
                  type="time"
                  defaultValue="09:00"
                  className="mt-1 border-gray-200 h-8 sm:h-10 text-xs sm:text-sm px-2 sm:px-3"
                  id="weekday-open"
                />
              </div>
              <div>
                <Label className="text-xs sm:text-sm text-gray-600">Closing</Label>
                <Input
                  type="time"
                  defaultValue="17:00"
                  className="mt-1 border-gray-200 h-8 sm:h-10 text-xs sm:text-sm px-2 sm:px-3"
                  id="weekday-close"
                />
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="mt-2 sm:mt-3 border-orange-200 hover:bg-orange-50 text-xs sm:text-sm h-7 sm:h-8"
              onClick={() => {
                const openInput = document.getElementById('weekday-open') as HTMLInputElement;
                const closeInput = document.getElementById('weekday-close') as HTMLInputElement;
                applyWeekdayHours(openInput.value, closeInput.value);
                showToast({ title: "Weekday hours applied", type: "success" });
              }}
            >
              Apply to Weekdays
            </Button>
          </div>
          
          <div className="bg-white p-3 sm:p-4 rounded-lg border border-orange-200">
            <h3 className="text-sm sm:text-base font-medium mb-2 sm:mb-3">Weekend Hours (Sat-Sun)</h3>
            <div className="grid grid-cols-2 gap-2 sm:gap-4">
              <div>
                <Label className="text-xs sm:text-sm text-gray-600">Opening</Label>
                <Input
                  type="time"
                  defaultValue="10:00"
                  className="mt-1 border-gray-200 h-8 sm:h-10 text-xs sm:text-sm px-2 sm:px-3"
                  id="weekend-open"
                />
              </div>
              <div>
                <Label className="text-xs sm:text-sm text-gray-600">Closing</Label>
                <Input
                  type="time"
                  defaultValue="16:00"
                  className="mt-1 border-gray-200 h-8 sm:h-10 text-xs sm:text-sm px-2 sm:px-3"
                  id="weekend-close"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row sm:space-x-2 space-y-2 sm:space-y-0 mt-2 sm:mt-3">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="border-orange-200 hover:bg-orange-50 text-xs sm:text-sm h-7 sm:h-8"
                onClick={() => {
                  const openInput = document.getElementById('weekend-open') as HTMLInputElement;
                  const closeInput = document.getElementById('weekend-close') as HTMLInputElement;
                  applyWeekendHours(openInput.value, closeInput.value);
                   showToast({ title: "Weekend hours applied", type: "success" });
                }}
              >
                Apply to Weekends
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="border-red-200 hover:bg-red-50 text-red-600 text-xs sm:text-sm h-7 sm:h-8"
                onClick={() => {
                  const updatedSchedules = [...businessHours.schedules];
                  [5, 6].forEach(index => {
                    updatedSchedules[index] = {
                      ...updatedSchedules[index],
                      isClosed: true,
                      openTime: null,
                      closeTime: null
                    };
                  });
                  
                  const newBusinessHours = { ...businessHours, schedules: updatedSchedules };
                  setBusinessHours(newBusinessHours);
                  
                  isUpdating.current = true;
                  onChange(newBusinessHours);
                  showToast({ title: "Store closed on weekends", type: "info" });
                  setTimeout(() => {
                    isUpdating.current = false;
                  }, 0);
                }}
              >
                Close on Weekends
              </Button>
            </div>
          </div>
          
          <div className="bg-white p-3 sm:p-4 rounded-lg border border-orange-200">
            <h3 className="text-sm sm:text-base font-medium mb-2 sm:mb-3">Holiday Settings</h3>
            <div className="flex items-center justify-between">
              <Label htmlFor="holiday-toggle" className="text-xs sm:text-sm text-gray-700">
                Open on holidays
              </Label>
              <Switch
                id="holiday-toggle"
                checked={businessHours.openOnHolidays}
                onCheckedChange={handleHolidayToggle}
              />
            </div>
            <p className="text-xs sm:text-sm text-gray-500 mt-2">
              This is a general setting. You can update specific holiday hours later if needed.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
