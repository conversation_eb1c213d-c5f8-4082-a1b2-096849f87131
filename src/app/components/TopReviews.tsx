"use client";
import { iReview } from "../util/Interfaces";
import ReviewBox from "./ReviewBox";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "../util/serverFunctions";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Clock, Flame, Star, ThumbsUp, ExternalLink } from "lucide-react";
import { TopReviewsSkeleton } from "./skeletons";
import Link from "next/link";

type FilterType = "latest" | "popular" | "trending";

const TopReviews = () => {
  const [filter, setFilter] = useState<FilterType>("latest");
  const [visibleReviews, setVisibleReviews] = useState(6);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["latestReviews", filter],
    queryFn: async () => {
      const response = await getLatestReviews(filter);
      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch latest reviews");
      }
      console.log(response.data.length, "reviews");
      return response.data;
    },
    refetchOnWindowFocus: false,
  });

  if (isError)
    return (
      <div className="text-center p-8">
        <p className="text-lg font-medium text-red-600">Error loading reviews</p>
        <p className="text-gray-500 mt-2">{error instanceof Error ? error.message : "Unknown error"}</p>
      </div>
    );
  if (isLoading) return <TopReviewsSkeleton />;

  // Ensure reviews is always an array with proper typing
  const reviews: iReview[] = Array.isArray(data) ? data : [];

  // No need for client-side filtering since endpoints now handle proper sorting
  const sortedReviews = reviews;

  if (sortedReviews.length === 0)
    return (
      <div className="text-center p-12">
        <div className="max-w-md mx-auto">
          <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Star className="w-10 h-10 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">No reviews yet</h3>
          <p className="text-gray-500 mb-6">Be the first to share your experience!</p>
          <Link href="/write-review">
            <Button className="bg-myTheme-primary hover:bg-myTheme-secondary">
              Write Your First Review
            </Button>
          </Link>
        </div>
      </div>
    );

  const loadMore = () => {
    setVisibleReviews((prev) => prev + 6);
  };

  const FilterButton = ({
    type,
    icon,
    label,
  }: {
    type: FilterType;
    icon: React.ReactNode;
    label: string;
  }) => (
    <button
      className={`flex items-center gap-2 relative px-4 py-2 font-medium text-base transition-all duration-200 ease-in-out rounded-lg ${
        filter === type
          ? "bg-myTheme-primary text-white shadow-md"
          : "text-gray-600 hover:text-myTheme-primary hover:bg-gray-50 border border-gray-200"
      }`}
      onClick={() => setFilter(type)}
    >
      {icon}
      {label}
    </button>
  );

  return (
    <div className="flex flex-col w-full h-full justify-center items-center space-y-8 relative z-0">
      {/* Header Section */}
      <div className="w-full flex flex-col items-center space-y-6">
        <div className="text-center space-y-3">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 leading-tight">
            Latest Reviews from Guyana
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            See what fellow Guyanese are saying about local businesses and services
          </p>
        </div>

        <div className="flex gap-3 flex-wrap justify-center">
          <FilterButton
            type="latest"
            icon={<Clock size={18} />}
            label="Latest"
          />
          <FilterButton
            type="popular"
            icon={<ThumbsUp size={18} />}
            label="Most Helpful"
          />
          <FilterButton
            type="trending"
            icon={<Flame size={18} />}
            label="Trending"
          />
        </div>
      </div>

      {/* Reviews Grid - vertical stack on mobile, grid on larger screens */}
      <div className="w-full grid mx-auto items-start justify-center grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
        {sortedReviews
          .slice(0, visibleReviews)
          .map((review: iReview, index: number) => (
            <div key={index} className="animate-fadeIn w-full" style={{animationDelay: `${index * 100}ms`}}>
              <ReviewBox review={review} />
            </div>
          ))}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mt-8">
        {visibleReviews < sortedReviews.length && (
          <Button 
            variant="outline" 
            onClick={loadMore}
            className="border-2 border-myTheme-primary text-myTheme-primary hover:bg-myTheme-primary hover:text-white transition-all duration-200"
          >
            Load More Reviews
          </Button>
        )}

        <Link href="/reviews-explore">
          <Button
            variant="default"
            className="bg-myTheme-primary hover:bg-myTheme-secondary text-white font-medium px-6 py-3 rounded-lg inline-flex items-center gap-2 transition-all duration-200 hover:shadow-lg hover:scale-105"
          >
            <ExternalLink size={18} />
            Explore All Reviews
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default TopReviews;
