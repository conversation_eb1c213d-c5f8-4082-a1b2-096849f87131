"use client";
import React, { useState, useRef, useCallback, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Upload, X, Trash2, Plus } from "lucide-react";
import Image from "next/image";
import { uploadProductImageToCloudinary } from "../util/productCloudinary";
import { useProductImageResizer } from "../util/useProductImageResizer";

interface ProductImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImagesUploaded: (imageUrls: string[]) => void;
  existingImages?: string[];
  maxImages?: number;
}

interface UploadingImage {
  id: string;
  file: File;
  preview: string;
  progress: number;
  status: 'processing' | 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

export default function ProductImageUploadModal({
  isOpen,
  onClose,
  onImagesUploaded,
  existingImages = [],
  maxImages = 10,
}: ProductImageUploadModalProps) {
  const [uploadingImages, setUploadingImages] = useState<UploadingImage[]>([]);
  const [completedImages, setCompletedImages] = useState<string[]>(existingImages);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { processImage, isResizing } = useProductImageResizer();

  // Sync completedImages with existingImages prop when it changes
  useEffect(() => {
    setCompletedImages(existingImages);
  }, [existingImages]);

  const handleFileSelect = useCallback(async (files: FileList) => {
    const remainingSlots = maxImages - completedImages.length - uploadingImages.length;
    const filesToProcess = Array.from(files).slice(0, remainingSlots);

    if (filesToProcess.length === 0) return;

    // Create initial upload states
    const newUploads: UploadingImage[] = filesToProcess.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      preview: URL.createObjectURL(file),
      progress: 0,
      status: 'processing',
    }));

    setUploadingImages(prev => [...prev, ...newUploads]);

    // Process each image
    for (const upload of newUploads) {
      try {
        // Update status to processing
        setUploadingImages(prev => 
          prev.map(img => 
            img.id === upload.id 
              ? { ...img, status: 'processing' as const, progress: 10 }
              : img
          )
        );

        // Process the image
        const result = await processImage(upload.file, {
          maxWidth: 1000,
          maxHeight: 1000,
          quality: 0.85,
          maxFileSize: 800 * 1024, // 800KB
        });

        // Update status to uploading
        setUploadingImages(prev => 
          prev.map(img => 
            img.id === upload.id 
              ? { ...img, status: 'uploading' as const, progress: 50 }
              : img
          )
        );

        // Upload to Cloudinary
        const uploadResult = await uploadProductImageToCloudinary(result.dataUrl);

        // Update status to completed
        setUploadingImages(prev => 
          prev.map(img => 
            img.id === upload.id 
              ? { 
                  ...img, 
                  status: 'completed' as const, 
                  progress: 100,
                  url: uploadResult.secure_url 
                }
              : img
          )
        );

        // Add to completed images
        setCompletedImages(prev => [...prev, uploadResult.secure_url]);

      } catch (error) {
        console.error('Error processing/uploading image:', error);
        setUploadingImages(prev => 
          prev.map(img => 
            img.id === upload.id 
              ? { 
                  ...img, 
                  status: 'error' as const, 
                  error: error instanceof Error ? error.message : 'Upload failed' 
                }
              : img
          )
        );
      }
    }
  }, [completedImages.length, uploadingImages.length, maxImages, processImage]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const removeUploadingImage = useCallback((id: string) => {
    setUploadingImages(prev => prev.filter(img => img.id !== id));
  }, []);

  const removeCompletedImage = useCallback((url: string) => {
    setCompletedImages(prev => prev.filter(img => img !== url));
  }, []);

  const handleSave = useCallback(() => {
    onImagesUploaded(completedImages);
    onClose();
  }, [completedImages, onImagesUploaded, onClose]);

  const handleCancel = useCallback(() => {
    // Reset to original state
    setCompletedImages(existingImages);
    setUploadingImages([]);
    onClose();
  }, [existingImages, onClose]);

  const canAddMore = completedImages.length + uploadingImages.length < maxImages;
  const hasChanges = JSON.stringify(completedImages.sort()) !== JSON.stringify(existingImages.sort());

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload Product Images</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Area */}
          {canAddMore && (
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                Drop images here or click to browse
              </p>
              <p className="text-sm text-gray-500">
                PNG, JPG, GIF, WebP up to 10MB each. Max {maxImages} images total.
              </p>
              <p className="text-xs text-gray-400 mt-2">
                Images will be optimized to 1000x1000px max while maintaining quality
              </p>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={(e) => {
              // Prevent any form submission
              e.preventDefault();
              e.stopPropagation();
              
              if (e.target.files) {
                handleFileSelect(e.target.files);
              }
            }}
          />

          {/* Uploading Images */}
          {uploadingImages.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4">Processing & Uploading</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {uploadingImages.map((upload) => (
                  <div key={upload.id} className="relative">
                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                      <Image
                        src={upload.preview}
                        alt="Uploading"
                        className="w-full h-full object-cover"
                        width={200}
                        height={200}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center">
                        <div className="text-white text-sm mb-2">
                          {upload.status === 'processing' && 'Processing...'}
                          {upload.status === 'uploading' && 'Uploading...'}
                          {upload.status === 'completed' && 'Complete!'}
                          {upload.status === 'error' && 'Error'}
                        </div>
                        {upload.status !== 'error' && (
                          <Progress value={upload.progress} className="w-20" />
                        )}
                        {upload.error && (
                          <div className="text-red-300 text-xs text-center px-2">
                            {upload.error}
                          </div>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute -top-2 -right-2 h-6 w-6"
                      onClick={() => removeUploadingImage(upload.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Completed Images */}
          {completedImages.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4">
                Selected Images ({completedImages.length}/{maxImages})
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {completedImages.map((url, index) => (
                  <div key={url} className="relative group">
                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                      <Image
                        src={url}
                        alt={`Product image ${index + 1}`}
                        className="w-full h-full object-cover"
                        width={200}
                        height={200}
                      />
                    </div>
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute -top-2 -right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => removeCompletedImage(url)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <div className="space-x-2">
              <Button
                variant="outline"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  fileInputRef.current?.click();
                }}
                disabled={!canAddMore}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add More
              </Button>
              <Button 
                onClick={handleSave}
                disabled={!hasChanges}
              >
                Save Images ({completedImages.length})
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
