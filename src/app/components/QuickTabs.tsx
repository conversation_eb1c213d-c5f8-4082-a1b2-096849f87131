"use client";

import {
  MdFastfood,
  MdDeliveryDining,
  MdOutlineElectricalServices,
  MdBusinessCenter,
  MdOtherHouses,
  MdOutlineLocalTaxi,
  MdOutlineHomeRepairService,
} from "react-icons/md";
import { IoMdCar } from "react-icons/io";
import { GiClothes } from "react-icons/gi";
import Link from "next/link";
import { LuExternalLink } from "react-icons/lu";
import CarouselWrapper from "./CarouselWrapper";

const QuickTabs = () => {
  const categories = [
    {
      name: "Fast Food",
      icon: <MdFastfood />,
      link: `/browse?tags=${encodeURIComponent("Fast Food".toLowerCase())}#results`,
      color: "from-orange-400 to-red-500",
    },
    {
      name: "Car Rental",
      icon: <IoMdCar />,
      link: `/browse?tags=${encodeURIComponent("Car Rental".toLowerCase())}#results`,
      color: "from-blue-400 to-blue-600",
    },
    {
      name: "Delivery",
      icon: <MdDeliveryDining />,
      link: `/browse?tags=${encodeURIComponent("Delivery Service".toLowerCase())}#results`,
      color: "from-green-400 to-emerald-600",
    },
    {
      name: "Electronics",
      icon: <MdOutlineElectricalServices />,
      link: `/browse?tags=${encodeURIComponent("Electronics".toLowerCase())}#results`,
      color: "from-purple-400 to-indigo-600",
    },
    {
      name: "Insurance",
      icon: <MdBusinessCenter />,
      link: `/browse?tags=${encodeURIComponent("Insurance Agency".toLowerCase())}#results`,
      color: "from-teal-400 to-cyan-600",
    },
    {
      name: "Real Estate",
      icon: <MdOtherHouses />,
      link: `/browse?tags=${encodeURIComponent("Real Estate".toLowerCase())}#results`,
      color: "from-amber-400 to-orange-600",
    },
    {
      name: "Clothing",
      icon: <GiClothes />,
      link: `/browse?tags=${encodeURIComponent("Clothing Store".toLowerCase())}#results`,
      color: "from-pink-400 to-rose-600",
    },
    {
      name: "Taxi",
      icon: <MdOutlineLocalTaxi />,
      link: `/browse?tags=${encodeURIComponent("Taxi Service".toLowerCase())}#results`,
      color: "from-yellow-400 to-amber-600",
    },
    {
      name: "Home Services",
      icon: <MdOutlineHomeRepairService />,
      link: `/browse?tags=${encodeURIComponent("Home Services".toLowerCase())}#results`,
      color: "from-lime-400 to-green-600",
    },
    {
      name: "See All",
      icon: <LuExternalLink />,
      link: "/browse#results",
      color: "from-gray-400 to-gray-600",
    },
  ];

  return (
    <div className="flex flex-col justify-center items-center w-full bg-gradient-to-b from-gray-50 to-white pt-4 pb-4 relative z-[1]">
      <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4 mb-16 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-black text-gray-800 leading-tight">
          Quick Categories
        </h2>
        <span className="text-base sm:text-lg text-gray-600 font-medium">Find what you need</span>
      </div>
      
      <div className="max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8">
        {/* Mobile carousel with scroll indicators */}
        <div className="relative">
          {/* Left fade indicator - only on mobile */}
          <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-50 to-transparent z-10 pointer-events-none sm:hidden"></div>
          
          {/* Right fade indicator - only on mobile */}
          <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-50 to-transparent z-10 pointer-events-none sm:hidden"></div>
          
          {/* Scroll hint arrows - only on mobile */}
          <div className="absolute right-2 top-1/2 -translate-y-1/2 z-20 sm:hidden">
            <div className="w-6 h-6 rounded-full bg-white/80 backdrop-blur-sm shadow-md flex items-center justify-center">
              <svg className="w-4 h-4 text-gray-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
          
          <CarouselWrapper
            slidesPerView={3.2}
            spacing={12}
            className="sm:grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6 lg:gap-8"
          >
            {categories.map((category, index) => (
              <Link
                key={index}
                href={category.link}
                className="group relative overflow-hidden rounded-2xl bg-white shadow-sm hover:shadow-2xl transition-all duration-300 hover:scale-105"
              >
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                ></div>
                <div className="relative p-4 flex flex-col items-center justify-center text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center mb-2 group-hover:scale-110 transition-all duration-300 group-hover:from-white/20 group-hover:to-white/10 backdrop-blur-sm">
                    <span
                      className={`text-2xl text-gray-700 group-hover:text-white transition-all duration-300 relative z-10 drop-shadow-md`}
                    >
                      {category.icon}
                    </span>
                  </div>
                  <p className="text-sm sm:text-base font-semibold text-gray-800 group-hover:text-white transition-colors duration-300">
                    {category.name}
                  </p>
                </div>
              </Link>
            ))}
          </CarouselWrapper>
        </div>
      </div>
    </div>
  );
};

export default QuickTabs;
