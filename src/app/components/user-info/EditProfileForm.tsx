import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { CardContent } from "@/components/ui/card";
import { AlertCircle, Save } from "lucide-react";
import { iUser } from "../../util/Interfaces";
import { toast } from "sonner";

interface EditProfileFormProps {
  editedFirstName: string;
  editedLastName: string;
  editedUserName: string;
  editedBio: string;
  setEditedFirstName: React.Dispatch<React.SetStateAction<string>>;
  setEditedLastName: React.Dispatch<React.SetStateAction<string>>;
  setEditedUserName: React.Dispatch<React.SetStateAction<string>>;
  setEditedBio: React.Dispatch<React.SetStateAction<string>>;
  onUpdateUser: (updatedUser: Partial<iUser>) => void;
  setIsEditingProfile: React.Dispatch<React.SetStateAction<boolean>>;
  handleSaveProfile: () => void;
  sanitizeInput: (input: string) => string;
  validateForm: () => boolean;
}

export default function EditProfileForm({
  editedFirstName,
  editedLastName,
  editedUserName,
  editedBio,
  setEditedFirstName,
  setEditedLastName,
  setEditedUserName,
  setEditedBio,
  onUpdateUser,
  setIsEditingProfile,
  handleSaveProfile,
  sanitizeInput,
  validateForm,
}: EditProfileFormProps) {
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  return (
    <CardContent className="max-w-2xl mx-auto p-6">
      <h3 className="text-xl font-semibold text-center mb-6 text-gray-800">
        Edit Profile
      </h3>
      <div className="space-y-4">
        <div className="space-y-1">
          <label
            htmlFor="firstName"
            className="text-sm font-medium text-gray-700"
          >
            First Name
          </label>
          <Input
            id="firstName"
            value={editedFirstName}
            onChange={(e) => setEditedFirstName(e.target.value)}
            placeholder="First Name"
            className={`bg-white ${formErrors.firstName ? "border-red-500" : ""}`}
            aria-invalid={!!formErrors.firstName}
            aria-describedby={formErrors.firstName ? "firstName-error" : undefined}
          />
          {formErrors.firstName && (
            <p
              id="firstName-error"
              className="text-xs text-red-500 flex items-center"
            >
              <AlertCircle className="h-3 w-3 mr-1" />
              {formErrors.firstName}
            </p>
          )}
        </div>

        <div className="space-y-1">
          <label
            htmlFor="lastName"
            className="text-sm font-medium text-gray-700"
          >
            Last Name
          </label>
          <Input
            id="lastName"
            value={editedLastName}
            onChange={(e) => setEditedLastName(e.target.value)}
            placeholder="Last Name"
            className={`bg-white ${formErrors.lastName ? "border-red-500" : ""}`}
            aria-invalid={!!formErrors.lastName}
            aria-describedby={formErrors.lastName ? "lastName-error" : undefined}
          />
          {formErrors.lastName && (
            <p
              id="lastName-error"
              className="text-xs text-red-500 flex items-center"
            >
              <AlertCircle className="h-3 w-3 mr-1" />
              {formErrors.lastName}
            </p>
          )}
        </div>

        <div className="space-y-1">
          <label
            htmlFor="userName"
            className="text-sm font-medium text-gray-700"
          >
            Username
          </label>
          <Input
            id="userName"
            value={editedUserName}
            onChange={(e) => setEditedUserName(e.target.value)}
            placeholder="Username"
            className={`bg-white ${formErrors.userName ? "border-red-500" : ""}`}
            aria-invalid={!!formErrors.userName}
            aria-describedby={formErrors.userName ? "userName-error" : undefined}
          />
          {formErrors.userName && (
            <p
              id="userName-error"
              className="text-xs text-red-500 flex items-center"
            >
              <AlertCircle className="h-3 w-3 mr-1" />
              {formErrors.userName}
            </p>
          )}
        </div>

        <div className="space-y-1">
          <label
            htmlFor="bio"
            className="text-sm font-medium text-gray-700"
          >
            Bio
          </label>
          <Textarea
            id="bio"
            value={editedBio}
            onChange={(e) => setEditedBio(e.target.value)}
            placeholder="Write your bio here..."
            className="bg-white min-h-[100px] resize-none"
            aria-label="Bio"
          />
        </div>
      </div>

      <div className="flex gap-3 justify-end mt-4">
        <Button
          variant="outline"
          onClick={() => setIsEditingProfile(false)}
          className="min-w-[100px] border-gray-300 hover:bg-gray-100"
          aria-label="Cancel profile editing"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSaveProfile}
          className="min-w-[100px] bg-blue-600 hover:bg-blue-700 text-white"
          aria-label="Save profile changes"
        >
          <Save className="h-4 w-4 mr-2" aria-hidden="true" />
          Save
        </Button>
      </div>
    </CardContent>
  );
}