import React, { useState, useMemo, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { FileText, MessageCircle, ThumbsUp, Settings, Building, Plus, Store, Search, FileX } from "lucide-react";
import { iUser, iReview, iComment, iBusiness } from "@/app/util/Interfaces";
import ReviewCard from "../ReviewCard";
import Comment from "../Comment";
import UserSettings from "../UserSettings";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useAuth } from "@clerk/nextjs";

dayjs.extend(relativeTime);

interface UserContentTabsProps {
  user: iUser;
  onUpdateUser: (updatedUser: Partial<iUser>) => void;
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  selectedTag: string | null;
  setSelectedTag: React.Dispatch<React.SetStateAction<string | null>>;
  sortBy: "date" | "rating";
  setSortBy: React.Dispatch<React.SetStateAction<"date" | "rating">>;
}

export default function UserContentTabs({
  user,
  onUpdateUser,
  searchTerm,
  setSearchTerm,
  selectedTag,
  setSelectedTag,
  sortBy,
  setSortBy,
}: UserContentTabsProps) {
  const { userId: clerkUserId } = useAuth();
  const isEditable = user.clerkUserId === clerkUserId;
  const [activeTab, setActiveTab] = useState("reviews");
  const [isLoadingReviews, setIsLoadingReviews] = useState(false);

  const allTags = Array.from(
    new Set(user.reviews?.flatMap((review) => review.product?.tags || []) || []),
  );

  const filteredReviews = useMemo(() => {
    if (!user.reviews) return [];

    return user.reviews
      .filter((review) => {
        const matchesSearch =
          searchTerm === "" ||
          (review.title?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
          (review.body?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
          (review.product?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);

        const matchesTag =
          !selectedTag ||
          (review.product?.tags?.includes(selectedTag) ?? false);

        return matchesSearch && matchesTag;
      })
      .sort((a, b) => {
        if (sortBy === "date") {
          return new Date(b.createdDate || 0).getTime() - new Date(a.createdDate || 0).getTime();
        } else {
          return (b.rating || 0) - (a.rating || 0);
        }
      });
  }, [user.reviews, searchTerm, selectedTag, sortBy]);

  useEffect(() => {
    setIsLoadingReviews(true);
    const timer = setTimeout(() => {
      setIsLoadingReviews(false);
    }, 100);
    return () => clearTimeout(timer);
  }, [searchTerm, selectedTag, sortBy]);

  const filteredComments = user.comments?.filter((comment) =>
    comment.body.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const filteredLikes = user.likedReviews?.filter((liked) => {
    return (
      liked.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
      liked.userId !== user.id
    );
  });

  return (
    <CardContent className="p-0">
      <Tabs
        defaultValue="reviews"
        className="w-full"
        onValueChange={setActiveTab}
        aria-label="User content tabs"
        activationMode="manual"
      >
      <div className="sticky top-0 z-10 px-4 sm:px-0 -mx-4 sm:mx-0 pt-2 pb-4 bg-white/95 backdrop-blur-sm shadow-sm border-b">
        <div className="overflow-x-auto overflow-y-hidden scrollbar-hide">
          <div className="max-w-3xl mx-auto">
            <TabsList className="flex min-w-max gap-x-1 sm:gap-x-2 bg-gray-50/80 border rounded-xl p-2 shadow-inner h-auto min-h-[60px]">
              <TabsTrigger value="reviews" className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col items-center justify-center gap-1 py-3 px-3 transition-all duration-300 hover:bg-white/50 min-w-[70px]">
                <FileText className="h-4 w-4" />
                <span className="text-xs">Reviews</span>
                <span className="bg-gray-200 data-[state=active]:bg-blue-100 px-1.5 py-0.5 rounded-full text-xs font-semibold transition-colors duration-300 absolute -top-1 -right-1">
                  {user.reviews?.length || 0}
                </span>
              </TabsTrigger>
              <TabsTrigger value="comments" className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col items-center justify-center gap-1 py-3 px-3 transition-all duration-300 hover:bg-white/50 min-w-[70px]">
                <MessageCircle className="h-4 w-4" />
                <span className="text-xs">Comments</span>
                <span className="bg-gray-200 data-[state=active]:bg-blue-100 px-1.5 py-0.5 rounded-full text-xs font-semibold transition-colors duration-300 absolute -top-1 -right-1">
                  {user.comments?.length || 0}
                </span>
              </TabsTrigger>
              <TabsTrigger value="likes" className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col items-center justify-center gap-1 py-3 px-3 transition-all duration-300 hover:bg-white/50 min-w-[70px]">
                <ThumbsUp className="h-4 w-4" />
                <span className="text-xs">Likes</span>
                <span className="bg-gray-200 data-[state=active]:bg-blue-100 px-1.5 py-0.5 rounded-full text-xs font-semibold transition-colors duration-300 absolute -top-1 -right-1">
                  {user.likedReviews?.length || 0}
                </span>
              </TabsTrigger>
              {/* Businesses tab - always visible for editable profiles */}
              {isEditable && (
                <TabsTrigger value="businesses" className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col items-center justify-center gap-1 py-3 px-3 transition-all duration-300 hover:bg-white/50 min-w-[70px]">
                  <Building className="h-4 w-4" />
                  <span className="text-xs">Businesses</span>
                  <span className="bg-gray-200 data-[state=active]:bg-blue-100 px-1.5 py-0.5 rounded-full text-xs font-semibold transition-colors duration-300 absolute -top-1 -right-1">
                    {user.businesses?.length || 0}
                  </span>
                </TabsTrigger>
              )}
              {isEditable && (
                <TabsTrigger value="settings" className="relative data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg text-sm font-medium flex flex-col items-center justify-center gap-1 py-3 px-3 transition-all duration-300 hover:bg-white/50 min-w-[70px]">
                  <Settings className="h-4 w-4" />
                  <span className="text-xs">Settings</span>
                </TabsTrigger>
              )}
            </TabsList>
          </div>
        </div>
      </div>

      <TabsContent value="reviews" className="animate-fadeIn">
        <div className="space-y-6 pt-4">
          <div className="bg-gray-50/80 p-5 rounded-xl space-y-4 shadow-sm border border-gray-100">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search reviews..."
                className="pl-10 bg-white border-gray-200 rounded-lg h-12 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm transition-all duration-300 hover:shadow-md"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Select
                value={selectedTag || "all"}
                onValueChange={(value) => setSelectedTag(value === "all" ? null : value)}
              >
                <SelectTrigger className="bg-white border-gray-200 rounded-lg h-12 text-sm shadow-sm transition-all duration-300 hover:shadow-md">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent className="max-h-[300px] overflow-y-auto">
                  <SelectItem value="all">All Categories</SelectItem>
                  {allTags.map((tag) => (
                    <SelectItem key={tag} value={tag}>
                      {tag}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={sortBy}
                onValueChange={(value) => setSortBy(value as "date" | "rating")}
              >
                <SelectTrigger className="bg-white border-gray-200 rounded-lg h-12 text-sm shadow-sm transition-all duration-300 hover:shadow-md">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Latest First</SelectItem>
                  <SelectItem value="rating">Highest Rated</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="min-h-[500px]">
            {isLoadingReviews ? (
              <div className="flex flex-col justify-center items-center h-64 space-y-4">
                <div className="relative">
                  <div className="animate-spin rounded-full h-12 w-12 border-2 border-t-blue-600 border-r-blue-600 border-b-transparent border-l-transparent"></div>
                  <div className="animate-ping absolute inset-0 rounded-full h-12 w-12 border border-blue-400 opacity-20"></div>
                </div>
                <p className="text-gray-500 font-medium">Loading reviews...</p>
              </div>
            ) : filteredReviews.length === 0 ? (
              <div className="flex flex-col justify-center items-center h-64 space-y-4 text-center">
                <div className="bg-gray-100 rounded-full p-4">
                  <FileX className="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <p className="text-gray-700 font-medium text-lg">No reviews found</p>
                  <p className="text-gray-500">Try changing your search or filter criteria</p>
                </div>
              </div>
            ) : (
              <div className="grid gap-5 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                {filteredReviews.map((review, index) => (
                  <Link
                    key={review.id}
                    href={`/fr?id=${review.id}&productid=${review.productId}`}
                    className="block group transform transition-all duration-300 hover:translate-y-[-4px]"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <Card className="h-full hover:shadow-xl transition-all duration-300 border-gray-200 group-hover:border-blue-200 overflow-hidden">
                      <CardContent className="p-5">
                        <div className="flex items-start gap-3 mb-3">
                          <div className="relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                            <Image
                              src={review.product?.display_image || "/images/default-product.png"}
                              alt={review.title || "Product image"}
                              fill
                              className="object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = "/images/default-product.png";
                              }}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold line-clamp-2 text-gray-900 leading-tight mb-1">
                              {review.title}
                            </h4>
                            <p className="text-sm text-gray-600 line-clamp-1">
                              {review.product?.name}
                            </p>
                          </div>
                        </div>
                        
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              <div className="flex text-yellow-400">
                                {[...Array(5)].map((_, i) => (
                                  <span key={i} className="text-sm">
                                    {i < review.rating ? "★" : "☆"}
                                  </span>
                                ))}
                              </div>
                              <span className="text-sm font-medium text-gray-700 ml-1">
                                {review.rating}/5
                              </span>
                            </div>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                              {review.comments?.length || 0} comments
                            </span>
                          </div>
                          
                          {review.product?.tags && review.product.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {review.product.tags.slice(0, 2).map((tag) => (
                                <span
                                  key={tag}
                                  className="px-2 py-1 bg-blue-50 text-blue-700 rounded-md text-xs font-medium"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}
                          
                          <p className="text-xs text-gray-500">
                            {dayjs(review.createdDate).fromNow()}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>
      </TabsContent>
      <TabsContent value="comments" className="animate-fadeIn">
        <div className="space-y-6 pt-4">
          <div className="min-h-[500px]">
            {filteredComments?.length === 0 ? (
              <div className="flex flex-col justify-center items-center h-64 space-y-4 text-center">
                <div className="bg-gray-100 rounded-full p-4">
                  <MessageCircle className="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <p className="text-gray-700 font-medium text-lg">No comments yet</p>
                  <p className="text-gray-500">Comments will appear here when available</p>
                </div>
              </div>
            ) : (
              <div className="grid gap-5 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                {filteredComments?.map((comment, index) => (
                  <Link
                    key={comment.id}
                    href={`/fr?id=${comment.review?.id}&productid=${comment.review?.productId}`}
                    className="block group transform transition-all duration-300 hover:translate-y-[-4px]"
                    style={{ animationDelay: `${index * 100}ms` }}
                    aria-label={`View comment on ${comment.review?.title || 'review'}`}
                  >
                    <Card className="h-full hover:shadow-xl transition-all duration-300 border-gray-200 group-hover:border-blue-200 overflow-hidden">
                      <CardContent className="p-5">
                        <p className="line-clamp-4 text-gray-700 leading-relaxed mb-4 text-sm sm:text-base">
                          {comment.body}
                        </p>
                        <div className="flex justify-between items-center text-xs">
                          <span className="text-gray-500 font-medium">{dayjs(comment.createdDate).fromNow()}</span>
                          <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full font-medium hover:bg-gray-200 transition-colors">
                            {comment.replies?.length || 0} replies
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>
      </TabsContent>
      <TabsContent value="likes" className="animate-fadeIn">
        <div className="space-y-6 pt-4">
          <div className="min-h-[500px]">
            {filteredLikes?.length === 0 ? (
              <div className="flex flex-col justify-center items-center h-64 space-y-4 text-center">
                <div className="bg-gray-100 rounded-full p-4">
                  <ThumbsUp className="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <p className="text-gray-700 font-medium text-lg">No liked reviews</p>
                  <p className="text-gray-500">Liked reviews will appear here</p>
                </div>
              </div>
            ) : (
              <div className="grid gap-5 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                {filteredLikes?.map((liked, index) => (
                  <Link
                    key={liked.id}
                    href={`/fr?id=${liked.id}&productid=${liked.productId}`}
                    className="block group transform transition-all duration-300 hover:translate-y-[-4px]"
                    style={{ animationDelay: `${index * 100}ms` }}
                    aria-label={`View liked review: ${liked.title || 'Untitled review'}`}
                  >
                    <Card className="h-full hover:shadow-xl transition-all duration-300 border-gray-200 group-hover:border-blue-200 overflow-hidden">
                      <CardContent className="p-5">
                        <div className="flex items-start gap-3 mb-3">
                          <div className="relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100 shadow-sm">
                            <Image
                              src={liked.product?.display_image || "/images/default-product.png"}
                              alt={liked.title || "Product image"}
                              fill
                              className="object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = "/images/default-product.png";
                              }}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold line-clamp-2 text-gray-900 leading-tight mb-1">
                              {liked.title}
                            </h4>
                            <p className="text-sm text-gray-600 line-clamp-1">
                              {liked.product?.name}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center justify-between mt-4">
                          <div className="flex items-center gap-1">
                            <div className="flex text-yellow-400">
                              {[...Array(5)].map((_, i) => (
                                <span key={i} className="text-sm">
                                  {i < liked.rating ? "★" : "☆"}
                                </span>
                              ))}
                            </div>
                            <span className="text-sm font-medium text-gray-700 ml-1">
                              {liked.rating}/5
                            </span>
                          </div>
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full font-medium">
                            {dayjs(liked.createdDate).fromNow()}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>
      </TabsContent>
      {/* Businesses tab content - displays user's businesses or a prompt to create one */}
      {isEditable && (
        <TabsContent value="businesses" className="animate-fadeIn">
          <div className="space-y-6 pt-4">
            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4 mb-6">
              <h2 className="text-2xl font-bold text-gray-900">My Businesses</h2>
              <Link href="/createbusiness">
                <button className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-300 shadow-md hover:shadow-lg w-full sm:w-auto justify-center">
                  <Plus className="h-4 w-4" />
                  <span>Add Business</span>
                </button>
              </Link>
            </div>
            
            {user.businesses && user.businesses.length > 0 ? (
              <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                {user.businesses.map((business: iBusiness) => (
                  <Link 
                    key={business.id} 
                    href={`/mybusinesses?businessId=${business.id}`}
                    className="block group"
                  >
                    <Card className="h-full hover:shadow-xl transition-all duration-300 border-gray-200 group-hover:border-blue-200 overflow-hidden">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100 shadow-sm">
                            {business.products && business.products.length > 0 && business.products[0].display_image ? (
                              <Image
                                src={business.products[0].display_image}
                                alt={business.products[0].name || "Business image"}
                                fill
                                className="object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = "/images/default-business.png";
                                }}
                              />
                            ) : (
                              <div className="bg-gray-200 border-2 border-dashed rounded-xl w-full h-full" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 line-clamp-1">{business.products && business.products[0] ? business.products[0].name : 'Unnamed Business'}</h3>
                            <p className="text-sm text-gray-500">
                              {business.products && business.products.length > 0 
                                ? `${business.products.length} product${business.products.length !== 1 ? 's' : ''}`
                                : 'No products'}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-1 text-gray-500">
                            <Store className="h-4 w-4" />
                            <span>Business</span>
                          </div>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                            {business.subscriptionStatus || 'Free'}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Store className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No businesses yet</h3>
                <p className="mt-1 text-sm text-gray-500">Get started by creating your first business.</p>
                <div className="mt-6">
                  <Link href="/createbusiness">
                    <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-300 w-full sm:w-auto justify-center">
                      <Plus className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                      Create Business
                    </button>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      )}
      {isEditable && (
        <TabsContent value="settings" className="animate-fadeIn">
          <div className="space-y-6 pt-4">
            <UserSettings user={user} onUpdateUser={onUpdateUser} />
          </div>
        </TabsContent>
      )}
      </Tabs>
    </CardContent>
  );
}