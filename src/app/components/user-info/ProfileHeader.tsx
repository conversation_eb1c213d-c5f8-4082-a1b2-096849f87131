import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { CardHeader } from "@/components/ui/card";
import { Camera } from "lucide-react";
import { iUser } from "../../util/Interfaces";
import { useImageResizer } from "../../util/useImageResizer";
import { uploadProfilePicToCloudinary } from "../../util/uploadImageToCloudinary";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import { avatarTriggerAtom } from "../../store/store";
import { useAtom } from "jotai";

interface ProfileHeaderProps {
  user: iUser;
  onUpdateUser: (updatedUser: Partial<iUser>) => void;
  isEditingProfile: boolean;
  setIsEditingProfile: React.Dispatch<React.SetStateAction<boolean>>;
  editedFirstName: string;
  editedLastName: string;
  editedUserName: string;
  editedBio: string;
  setEditedFirstName: React.Dispatch<React.SetStateAction<string>>;
  setEditedLastName: React.Dispatch<React.SetStateAction<string>>;
  setEditedUserName: React.Dispatch<React.SetStateAction<string>>;
  setEditedBio: React.Dispatch<React.SetStateAction<string>>;
  currentAvatar: string;
  setCurrentAvatar: React.Dispatch<React.SetStateAction<string>>;
  selectedGradient: string;
  handleAvatarClick: () => void;
  handleFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  isAvatarUploading: boolean;
  isResizing: boolean;
}

export default function ProfileHeader({
  user,
  onUpdateUser,
  isEditingProfile,
  setIsEditingProfile,
  editedFirstName,
  editedLastName,
  editedUserName,
  editedBio,
  setEditedFirstName,
  setEditedLastName,
  setEditedUserName,
  setEditedBio,
  currentAvatar,
  setCurrentAvatar,
  selectedGradient,
  handleAvatarClick,
  handleFileChange,
  fileInputRef,
  isAvatarUploading,
  isResizing,
}: ProfileHeaderProps) {
  const { userId: clerkUserId } = useAuth();
  const isEditable = user.clerkUserId === clerkUserId;
  const [, setAvatarTrigger] = useAtom(avatarTriggerAtom);



  return (
    <CardHeader className="relative p-0">
      <div className={`relative h-36 sm:h-52 md:h-64 overflow-hidden bg-gradient-to-br ${selectedGradient} transition-all duration-500`}>
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-20 -right-20 w-64 h-64 sm:w-80 sm:h-80 bg-gradient-to-br from-white/10 to-white/30 rounded-full blur-3xl animate-pulse opacity-50"></div>
          <div className="absolute -bottom-40 -left-20 w-64 h-64 sm:w-80 sm:h-80 bg-gradient-to-tl from-white/10 to-white/30 rounded-full blur-3xl animate-pulse opacity-50 animation-delay-1000"></div>
        </div>
        
        {/* Subtle background branding */}
        <div className="absolute inset-0 pointer-events-none select-none">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-5 text-white text-4xl sm:text-6xl md:text-8xl font-light tracking-wider">
            ReviewIt
          </div>
        </div>
        
        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/5 to-black/10"></div>
        
        {/* Avatar container */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative transition-transform duration-300 hover:scale-105">
            <div
              className={`relative h-28 w-28 sm:h-36 sm:w-36 md:h-44 md:w-44 ${isEditable ? "cursor-pointer" : ""} transition-all duration-300`}
              onClick={handleAvatarClick}
              role={isEditable ? "button" : undefined}
              aria-label={isEditable ? "Change profile picture" : undefined}
              tabIndex={isEditable ? 0 : undefined}
              onKeyDown={(e) => {
                if (isEditable && (e.key === "Enter" || e.key === " ")) {
                  e.preventDefault();
                  handleAvatarClick();
                }
              }}
            >
              <Avatar className="h-full w-full border-4 border-white/90 shadow-xl backdrop-blur-sm transition-all duration-300 hover:shadow-2xl">
                <AvatarImage
                  src={currentAvatar || ""}
                  alt={`${editedFirstName} ${editedLastName}`}
                  className="object-cover"
                />
                <AvatarFallback className="bg-white/80 text-gray-700 text-2xl sm:text-3xl md:text-4xl font-medium">
                  {`${editedFirstName?.charAt(0) || ""}${editedLastName?.charAt(0) || ""}`}
                </AvatarFallback>
              </Avatar>
              
              {/* Loading state */}
              {(isAvatarUploading || isResizing) && (
                <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 rounded-full backdrop-blur-sm">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-t-2 border-white"></div>
                  {isResizing && (
                    <span className="absolute text-white text-sm font-medium mt-16 px-2 py-1 bg-black/40 rounded-full">
                      Processing...
                    </span>
                  )}
                </div>
              )}
            </div>
            
            {/* Edit button */}
            {isEditable && (
              <div
                className="absolute -bottom-1 -right-1 bg-primary rounded-full p-2.5 cursor-pointer shadow-lg hover:shadow-xl hover:bg-primary/90 transition-all duration-300 border-2 border-white/90"
                onClick={handleAvatarClick}
                role="button"
                aria-label="Change profile picture"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    handleAvatarClick();
                  }
                }}
              >
                <Camera className="h-5 w-5 text-white" aria-hidden="true" />
              </div>
            )}
          </div>
          
          {isEditable && (
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
              aria-label="Upload profile picture"
            />
          )}
        </div>
      </div>
    </CardHeader>
  );
}