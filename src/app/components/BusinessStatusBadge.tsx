import React from 'react';
import { iProduct } from '../util/Interfaces';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);
import { isBusinessOpen, createDefaultBusinessHours } from '../types/businessHours';

interface BusinessStatusBadgeProps {
  product: iProduct;
  className?: string;
  showMessage?: boolean;
}

const BusinessStatusBadge: React.FC<BusinessStatusBadgeProps> = ({
  product,
  className = '',
  showMessage = false,
}) => {
  // Current time in Guyana timezone
  const guyanaTime = dayjs().tz('America/Guyana').toDate();
  
  // Try to use businessHours, or fall back to legacy format
  let businessHours = product.businessHours;
  
  // If no businessHours but legacy data exists, create a temporary businessHours object
  if (!businessHours && product.openingDays && product.openingHrs && product.closingHrs) {
    const tempBusinessHours = createDefaultBusinessHours();
    tempBusinessHours.schedules = tempBusinessHours.schedules.map(schedule => {
      if (product.openingDays?.includes(schedule.day)) {
        return {
          ...schedule,
          isClosed: false,
          openTime: product.openingHrs || null,
          closeTime: product.closingHrs || null
        };
      }
      return schedule;
    });
    businessHours = tempBusinessHours;
  }
  
  // If still no business hours data, don't show the badge
  if (!businessHours) {
    return null;
  }
  
  const { isOpen, message } = isBusinessOpen(businessHours, guyanaTime);

  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        isOpen 
          ? 'bg-green-100 text-green-800 border border-green-200' 
          : 'bg-red-100 text-red-800 border border-red-200'
      }`}>
        <div className={`w-2 h-2 rounded-full mr-1 ${
          isOpen ? 'bg-green-500' : 'bg-red-500'
        }`} />
        {isOpen ? 'Open' : 'Closed'}
      </div>
      
      {showMessage && message && (
        <span className="text-xs text-gray-600">
          {message}
        </span>
      )}
    </div>
  );
};

export default BusinessStatusBadge;
