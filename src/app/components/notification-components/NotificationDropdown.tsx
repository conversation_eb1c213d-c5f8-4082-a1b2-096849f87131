"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Wifi, WifiOff, MessageSquare } from "lucide-react";
import { Tooltip } from "@mantine/core";
import Link from "next/link";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import BellButton from "./BellButton";
import NotificationHeader from "./NotificationHeader";
import NotificationList from "./NotificationList";
import { useMarkAllRead } from "./hooks/useMarkAllRead";

import {
  iUserNotification,
  iProductOwnerNotification,
  LikeNotification,
  SystemNotification,
} from "@/app/util/Interfaces";

interface NotificationDropdownProps {
  // Notification data
  userNotifications: iUserNotification[];
  ownerNotifications: iProductOwnerNotification[];
  likeNotifications: LikeNotification[];
  systemNotifications: SystemNotification[];
  
  // Derived data
  unreadCount: number;
  totalCount: number;
  latestUserNotifications: iUserNotification[];
  latestOwnerNotifications: iProductOwnerNotification[];
  latestLikeNotifications: LikeNotification[];
  latestSystemNotifications: SystemNotification[];
  unreadUserNotifications: iUserNotification[];
  unreadOwnerNotifications: iProductOwnerNotification[];
  unreadSystemNotifications: SystemNotification[];
  
  // Connection status
  isSSEConnected: boolean;
  
  // Loading states
  isLoading: boolean;
  
  // Event handlers
  onNotificationClick: (
    notification: iUserNotification | iProductOwnerNotification | LikeNotification | SystemNotification,
    type: "user" | "owner" | "like" | "system"
  ) => void;
  onMarkAsReadLocal: (id: string | undefined, type: "user" | "owner" | "like" | "system") => void;
  onBellClick: () => void;
}

export default function NotificationDropdown({
  userNotifications,
  ownerNotifications,
  likeNotifications,
  systemNotifications,
  unreadCount,
  totalCount,
  latestUserNotifications,
  latestOwnerNotifications,
  latestLikeNotifications,
  latestSystemNotifications,
  unreadUserNotifications,
  unreadOwnerNotifications,
  unreadSystemNotifications,
  isSSEConnected,
  isLoading,
  onNotificationClick,
  onMarkAsReadLocal,
  onBellClick,
}: NotificationDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const router = useRouter();

  // Use the mark-all-read hook
  const { handleMarkAllAsRead: markAllAsReadHook } = useMarkAllRead();
  
  const handleMarkAllAsRead = () => {
    markAllAsReadHook(userNotifications, ownerNotifications, likeNotifications, systemNotifications);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <div className="relative">
          <BellButton
            unreadCount={unreadCount}
            isHovered={isHovered}
            isLoading={isLoading}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={(e) => {
              if (unreadCount === 0) {
                e.preventDefault();
                onBellClick();
              }
            }}
          />

          {/* Connection status indicator */}
          <Tooltip
            label={
              isSSEConnected ? "Real-time connected" : "Real-time disconnected"
            }
            position="bottom"
            withArrow
            className="absolute bottom-0 right-0"
          >
            <div className="absolute bottom-1 right-1 w-2 h-2 rounded-full">
              {isSSEConnected ? (
                <Wifi className="w-3 h-3 text-green-500" />
              ) : (
                <WifiOff className="w-3 h-3 text-amber-500" />
              )}
            </div>
          </Tooltip>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-80 max-h-[80vh] overflow-y-auto p-0 rounded-xl shadow-lg border border-gray-200 bg-white"
        sideOffset={8}
      >
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <NotificationHeader
            isSSEConnected={isSSEConnected}
            totalCount={totalCount}
            onMarkAllAsRead={handleMarkAllAsRead}
          />
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : totalCount === 0 ? (
          <>
            <div className="flex flex-col items-center justify-center py-8 text-gray-500">
              <MessageSquare className="h-12 w-12 mb-3 text-gray-300" />
              <p className="text-sm font-medium">No notifications</p>
              <p className="text-xs text-gray-400 mt-1">
                You're all caught up!
              </p>
            </div>
            <DropdownMenuItem
              asChild
              className="px-4 py-3 cursor-pointer hover:bg-gray-100 focus:bg-gray-100 transition-colors"
            >
              <Link
                href="/notifications"
                className="flex items-center justify-between w-full text-blue-600 hover:text-blue-700"
              >
                <span>See All Notifications</span>
                <MessageSquare className="h-4 w-4" />
              </Link>
            </DropdownMenuItem>
          </>
        ) : (
          <NotificationList
            latestUserNotifications={latestUserNotifications}
            latestOwnerNotifications={latestOwnerNotifications}
            latestLikeNotifications={latestLikeNotifications}
            latestSystemNotifications={latestSystemNotifications}
            unreadUserNotifications={unreadUserNotifications}
            unreadOwnerNotifications={unreadOwnerNotifications}
            unreadSystemNotifications={unreadSystemNotifications}
            onNotificationClick={onNotificationClick}
            onMarkAsReadLocal={onMarkAsReadLocal}
          />
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
