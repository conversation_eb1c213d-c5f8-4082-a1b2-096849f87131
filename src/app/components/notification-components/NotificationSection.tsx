import React from "react";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tooltip } from "@mantine/core";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {
  Clock,
  CheckCircleIcon,
  Users,
  User,
  Check,
} from "lucide-react";

import {
  getNotificationIcon,
  getNotificationTypeLabel,
} from "@/app/util/notificationIcons";
import { cn } from "@/lib/utils";
import {
  isOwnerNotification,
  shouldHavePremiumStyling,
} from "@/app/util/notificationHelpers";
import {
  iProductOwnerNotification,
  iUserNotification,
  LikeNotification,
  SystemNotification,
} from "@/app/util/Interfaces";

dayjs.extend(relativeTime);

type NotificationType =
  | iUserNotification
  | iProductOwnerNotification
  | LikeNotification
  | SystemNotification;

interface NotificationSectionProps {
  title: string;
  notifications: NotificationType[];
  onClick: (notification: NotificationType) => void;
  onMarkAsRead?: (notification: NotificationType) => void;
  icon: React.ReactNode;
}

const NotificationSection: React.FC<NotificationSectionProps> = ({
  title,
  notifications,
  onClick,
  onMarkAsRead,
  icon,
}) => {
  if (notifications.length === 0) return null;

  return (
    <>
      <div className="px-4 py-2 bg-gray-50 border-b border-t border-gray-100">
        <div className="flex items-center">
          {icon}
          <span className="ml-2 font-semibold text-sm text-gray-800">
            {title}
          </span>
        </div>
      </div>
      {notifications.map((notification, index) => {
        const isPremium = shouldHavePremiumStyling(notification);
        const isRead = notification.read === true;

        return (
          <DropdownMenuItem
            key={`${title.toLowerCase()}-${index}`}
            className={cn(
              "flex flex-col items-start p-3 cursor-pointer focus:bg-gray-100 relative overflow-hidden border-l-4 transition-colors duration-150",
              isPremium
                ? "bg-gradient-to-r from-slate-50 to-blue-50/30 hover:bg-blue-100 border-l-blue-500"
                : "hover:bg-gray-100 border-l-transparent",
              !isRead && "bg-blue-50/80 font-semibold",
            )}
            onClick={() => onClick(notification)}
          >
            {!isRead && (
              <Tooltip label="Mark as read" withArrow position="left">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onMarkAsRead?.(notification);
                  }}
                  className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200/70 transition-colors"
                  aria-label="Mark as read"
                >
                  <Check className="h-4 w-4 text-gray-500" />
                </button>
              </Tooltip>
            )}
            <div className="flex items-start w-full">
              <div className="flex-shrink-0 mr-3 mt-1">
                {getNotificationIcon(notification)}
              </div>
              <div className="flex-1 min-w-0">
                <p
                  className={cn(
                    "font-medium text-sm",
                    isPremium ? "owner-text" : "text-gray-800",
                    !isRead ? "font-bold" : "font-normal",
                  )}
                >
                  {"target_type" in notification
                    ? `${notification.from_name || 'Someone'} liked your ${notification.target_type || 'item'}`
                    : isOwnerNotification(notification as iUserNotification)
                    ? (notification as iProductOwnerNotification).review_title
                    : (notification as any).content}
                </p>
                <div className="flex items-center justify-between mt-1.5">
                  <div className="flex items-center text-xs text-gray-500">
                    {isPremium ? (
                      <>
                        <Tooltip label="Business Owner" withArrow>
                          <div className="flex items-center mr-1">
                            <CheckCircleIcon className="h-3 w-3 mr-1 text-blue-500" />
                            <span className="truncate font-medium text-blue-700">
                              {(notification as any).from_name}
                            </span>
                          </div>
                        </Tooltip>
                        <span className="notification-owner-badge">
                          Verified Owner
                        </span>
                      </>
                    ) : (
                      <>
                        {(notification as any).additional_users ? (
                          <>
                            <Users className="h-3 w-3 mr-1 text-gray-600" />
                            <span className="truncate">
                              {(notification as any).from_name} and{" "}
                              {(notification as any).additional_users.length}{" "}
                              others
                            </span>
                          </>
                        ) : (
                          <>
                            <User className="h-3 w-3 mr-1" />
                            <span className="truncate">
                              {(notification as any).from_name}
                            </span>
                          </>
                        )}
                      </>
                    )}
                  </div>
                  <div className="flex items-center ml-2 text-xs text-gray-400">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{dayjs(notification.created_at).fromNow()}</span>
                  </div>
                </div>
                <div className="mt-2 flex items-center justify-between w-full">
                  <Badge
                    variant="secondary"
                    className="text-xxs bg-gray-100 text-gray-600 border-gray-200 border"
                  >
                    {getNotificationTypeLabel(notification)}
                  </Badge>
                </div>
              </div>
            </div>
          </DropdownMenuItem>
        );
      })}
    </>
  );
};

export default NotificationSection;
