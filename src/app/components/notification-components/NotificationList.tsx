"use client";
import React from "react";
import { DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { MessageSquare, Star, ThumbsUp, Bell } from "lucide-react";
import NotificationSection from "./NotificationSection";
import { iUserNotification, iProductOwnerNotification, LikeNotification, SystemNotification } from "@/app/util/Interfaces";

interface NotificationListProps {
  latestUserNotifications: iUserNotification[];
  latestOwnerNotifications: iProductOwnerNotification[];
  latestLikeNotifications: LikeNotification[];
  latestSystemNotifications: SystemNotification[];
  unreadUserNotifications: iUserNotification[];
  unreadOwnerNotifications: iProductOwnerNotification[];
  unreadSystemNotifications: SystemNotification[];
  onNotificationClick: (
    notification: iUserNotification | iProductOwnerNotification | LikeNotification | SystemNotification,
    type: "user" | "owner" | "like" | "system",
  ) => void;
  onMarkAsReadLocal: (id: string | undefined, type: "user" | "owner" | "like" | "system") => void;
}

export default function NotificationList({
  latestUserNotifications,
  latestOwnerNotifications,
  latestLikeNotifications,
  latestSystemNotifications,
  unreadUserNotifications,
  unreadOwnerNotifications,
  unreadSystemNotifications,
  onNotificationClick,
  onMarkAsReadLocal,
}: NotificationListProps) {
  return (
    <>
      <NotificationSection
        title="User Notifications"
        notifications={latestUserNotifications}
        onClick={(n) => onNotificationClick(n, "user")}
        onMarkAsRead={(n) => onMarkAsReadLocal(n.id, "user")}
        icon={<MessageSquare className="h-4 w-4 text-green-500" />}
      />
      {unreadUserNotifications.length > 0 && unreadOwnerNotifications.length > 0 && (
        <DropdownMenuSeparator className="my-1 border-gray-200" />
      )}
      <NotificationSection
        title="Product Reviews"
        notifications={latestOwnerNotifications}
        onClick={(n) => onNotificationClick(n, "owner")}
        onMarkAsRead={(n) => onMarkAsReadLocal(n.id, "owner")}
        icon={<Star className="h-4 w-4 text-amber-500" />}
      />
      {latestLikeNotifications.length > 0 && (
        <>
          <DropdownMenuSeparator className="my-1 border-gray-200" />
          <NotificationSection
            title="Likes"
            notifications={latestLikeNotifications}
            onClick={(n) => onNotificationClick(n, "like")}
            onMarkAsRead={(n) => onMarkAsReadLocal(n.id, "like")}
            icon={<ThumbsUp className="h-4 w-4 text-red-500" />}
          />
        </>
      )}
      {latestSystemNotifications.length > 0 && (
        <>
          <DropdownMenuSeparator className="my-1 border-gray-200" />
          <NotificationSection
            title="System Announcements"
            notifications={latestSystemNotifications}
            onClick={(n) => onNotificationClick(n, "system")}
            onMarkAsRead={(n) => onMarkAsReadLocal(n.id, "system")}
            icon={<Bell className="h-4 w-4 text-blue-500" />}
          />
        </>
      )}
      <DropdownMenuSeparator className="my-1 border-gray-200" />
      <DropdownMenuItem
        asChild
        className="px-4 py-3 cursor-pointer hover:bg-gray-100 focus:bg-gray-100 transition-colors"
      >
        <Link
          href="/notifications"
          className="flex items-center justify-between w-full text-blue-600 hover:text-blue-700"
        >
          <span>See All Notifications</span>
          <MessageSquare className="h-4 w-4" />
        </Link>
      </DropdownMenuItem>
    </>
  );
}
