"use client";
import { BellIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import React from "react";

interface BellButtonProps {
  unreadCount: number;
  isHovered: boolean;
  isLoading: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

export default function BellButton({
  unreadCount,
  isHovered,
  isLoading,
  onMouseEnter,
  onMouseLeave,
  onClick,
}: BellButtonProps) {
  return (
    <Button
      variant="ghost"
      size="icon"
      className={cn("relative", isHovered && "bg-gray-100", isLoading && "opacity-70")}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      <BellIcon className={cn("h-5 w-5", isHovered ? "text-myTheme-accent" : "text-gray-700")} />
      {unreadCount > 0 && (
        <Badge
          variant="destructive"
          className="absolute -top-1 -right-1 px-1 min-w-[1.25rem] h-5 bg-red-500 text-white border border-white"
        >
          {unreadCount > 99 ? "99+" : unreadCount}
        </Badge>
      )}
    </Button>
  );
}
