"use client";
import { Wifi, WifiOff } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface NotificationHeaderProps {
  isSSEConnected: boolean;
  totalCount: number;
  onMarkAllAsRead: () => void;
}

export default function NotificationHeader({
  isSSEConnected,
  totalCount,
  onMarkAllAsRead,
}: NotificationHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <h3 className="font-semibold text-gray-900">Notifications</h3>
        {isSSEConnected ? (
          <Wifi className="h-4 w-4 text-green-500" />
        ) : (
          <WifiOff className="h-4 w-4 text-red-500" />
        )}
      </div>
      {totalCount > 0 && (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
            onClick={onMarkAllAsRead}
          >
            Mark all as read
          </Button>
          <Badge
            variant="outline"
            className="bg-blue-500 text-white border-blue-600"
          >
            {totalCount} new
          </Badge>
        </div>
      )}
    </div>
  );
}
