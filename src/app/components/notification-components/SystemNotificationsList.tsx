"use client";
import React, { useState, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bell, CheckCircle, Info, AlertTriangle, XCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { SystemNotification } from "@/app/util/Interfaces";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

interface SystemNotificationsListProps {
  notifications: SystemNotification[];
  onMarkAsRead: (notificationId: string) => void;
}

const getIconForType = (icon?: string) => {
  switch (icon) {
    case "success":
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case "warning":
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case "error":
      return <XCircle className="h-4 w-4 text-red-500" />;
    default:
      return <Info className="h-4 w-4 text-blue-500" />;
  }
};

const getColorForType = (icon?: string) => {
  switch (icon) {
    case "success":
      return "border-l-green-500 bg-green-50";
    case "warning":
      return "border-l-yellow-500 bg-yellow-50";
    case "error":
      return "border-l-red-500 bg-red-50";
    default:
      return "border-l-blue-500 bg-blue-50";
  }
};

const SystemNotificationsList: React.FC<SystemNotificationsListProps> = ({
  notifications,
  onMarkAsRead,
}) => {
  // Local state to track notifications being marked as read
  const [markedAsReadIds, setMarkedAsReadIds] = useState<Set<string>>(new Set());
  
  // Handle mark as read with local state update
  const handleMarkAsRead = useCallback((notificationId: string) => {
    // Update local state immediately
    setMarkedAsReadIds(prev => new Set([...prev, notificationId]));
    // Call the parent handler
    onMarkAsRead(notificationId);
  }, [onMarkAsRead]);
  if (!notifications || notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-gray-500">
        <Bell className="h-12 w-12 mb-3 text-gray-300" />
        <p className="text-sm font-medium">No system announcements</p>
        <p className="text-xs text-gray-400 mt-1">
          System notifications will appear here
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.slice(0, 5).map((notification) => (
        <div
          key={notification.id}
          className={cn(
            "border-l-4 rounded-lg p-4 transition-all duration-200 hover:shadow-md",
            getColorForType(notification.icon),
            !(notification.read || markedAsReadIds.has(notification.id)) && "ring-2 ring-blue-100"
          )}
        >
          <div className="flex items-start justify-between gap-3">
            <div className="flex items-start gap-3 flex-1 min-w-0">
              <div className="flex-shrink-0 mt-0.5">
                {getIconForType(notification.icon)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-gray-900 text-sm truncate">
                    {notification.title}
                  </h3>
                  {!(notification.read || markedAsReadIds.has(notification.id)) && (
                    <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-blue-100 text-blue-700">
                      New
                    </Badge>
                  )}
                </div>
                
                <p className="text-sm text-gray-700 mb-2 line-clamp-2">
                  {notification.message}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    {dayjs(notification.created_at).fromNow()}
                  </span>
                  
                  {!(notification.read || markedAsReadIds.has(notification.id)) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMarkAsRead(notification.id)}
                      className="text-xs h-6 px-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    >
                      Mark as read
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
      
      {notifications.length > 5 && (
        <div className="text-center pt-2">
          <p className="text-xs text-gray-500">
            Showing 5 most recent announcements
          </p>
        </div>
      )}
    </div>
  );
};

export default SystemNotificationsList;
