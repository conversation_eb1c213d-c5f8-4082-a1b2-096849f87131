"use client";
import React from "react";
import { MessageSquare, CheckCircle, Eye, Info } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { shouldHavePremiumStyling } from "@/app/util/notificationHelpers";
import { getNotificationIcon, getNotificationTypeLabel } from "@/app/util/notificationIcons";
import { Tooltip } from "@mantine/core";
import { useRouter } from "next/navigation";
import { iUserNotification } from "@/app/util/Interfaces";

interface Props {
  notifications: iUserNotification[];
  onMarkAsRead: (id: string) => void;
  onShowDetails: (notification: iUserNotification) => void;
}

const UserNotificationsList: React.FC<Props> = ({ 
  notifications, 
  onMarkAsRead, 
  onShowDetails 
}) => {
  const router = useRouter();

  if (!notifications) return null;

  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10 bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="bg-green-50 p-4 rounded-full mb-4">
          <MessageSquare className="h-8 w-8 text-green-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">No user notifications</h3>
        <p className="text-gray-500 text-center max-w-md">You don't have any user notifications at the moment.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {notifications.map((notification, index) => {
        const isPremium = shouldHavePremiumStyling(notification);
        const isRead = notification.read === true;

        return (
          <div
            key={notification.id}
            className={cn(
              "rounded-lg border p-5 hover:shadow-md transition-all relative overflow-hidden border-l-4 animate-fadeIn",
              isPremium
                ? "bg-gradient-to-r from-slate-50 to-blue-50/30 border-blue-200 border-l-blue-500"
                : "bg-white border-gray-200 border-l-transparent",
              !isRead && "bg-blue-50"
            )}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {!isRead && (
              <div className="absolute top-4 right-4">
                <div className="unread-dot"></div>
              </div>
            )}
            <div className="flex items-start gap-3 mb-2">
              <div className="flex-shrink-0 mt-1">
                {getNotificationIcon(notification)}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <h3
                    className={cn(
                      "font-medium",
                      isPremium ? "owner-text" : "text-gray-900",
                      !isRead && "font-bold"
                    )}
                  >
                    {notification.content}
                  </h3>
                  <Badge 
                    variant="secondary" 
                    className="text-xs px-2 py-1 bg-gray-100 text-gray-600 border-0 ml-2"
                  >
                    {getNotificationTypeLabel(notification)}
                  </Badge>
                </div>
                <span
                  className={cn(
                    "text-xs px-2 py-1 rounded-full",
                    isRead
                      ? "bg-green-50 text-green-700"
                      : "bg-blue-100 text-blue-700"
                  )}
                >
                  {isRead ? "Read" : "New"}
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-2 flex items-center">
              {isPremium ? (
                <>
                  <span className="mr-1">From:</span>
                  <Tooltip label="Business Owner" withArrow>
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1 text-blue-500" />
                      <span className="font-medium text-blue-700">
                        {notification.from_name}
                      </span>
                    </div>
                  </Tooltip>
                  <span className="notification-owner-badge ml-2">
                    Verified Owner
                  </span>
                </>
              ) : (
                <>
                  <span>From: {notification.from_name}</span>
                </>
              )}
            </p>
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>
                {notification.created_at
                  ? new Date(notification.created_at).toLocaleString()
                  : "Date unknown"}
              </span>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => onShowDetails(notification)}
                  className={cn(
                    "flex items-center gap-1 transition-colors cursor-pointer px-2 py-1 rounded hover:bg-gray-100",
                    isPremium
                      ? "text-blue-600 hover:text-blue-800"
                      : "text-gray-600 hover:text-gray-800"
                  )}
                  title="View details"
                >
                  <Info className="w-4 h-4" />
                  Details
                </button>
                <button
                  onClick={() => {
                    onMarkAsRead(notification.id);
                    let url = `/fr?id=${notification.review_id}&productid=${notification.product_id || ""}`;
                    if (notification.comment_id) {
                      url += `&cid=${notification.comment_id}`;
                    }
                    router.push(url);
                  }}
                  className={cn(
                    "flex items-center gap-1 transition-colors cursor-pointer px-2 py-1 rounded hover:bg-gray-100",
                    isPremium
                      ? "text-blue-600 hover:text-blue-800"
                      : "text-myTheme-primary hover:text-myTheme-primary/80"
                  )}
                  title="View item"
                >
                  <Eye className="w-4 h-4" />
                  View
                </button>
                {!isRead && (
                  <button
                    onClick={() => onMarkAsRead(notification.id)}
                    className={cn(
                      "flex items-center gap-1 transition-colors cursor-pointer px-2 py-1 rounded hover:bg-gray-100",
                      isPremium
                        ? "text-green-600 hover:text-green-800"
                        : "text-green-600 hover:text-green-800"
                    )}
                    title="Mark as read"
                  >
                    <CheckCircle className="w-4 h-4" />
                    Mark Read
                  </button>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default UserNotificationsList;