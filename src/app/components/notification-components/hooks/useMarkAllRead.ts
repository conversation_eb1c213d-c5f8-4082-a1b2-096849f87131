import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { markAllAsRead } from "@/app/util/notificationService";
import {
  userNotificationsAtom,
  ownerNotificationsAtom,
  likeNotificationsAtom,
  systemNotifications<PERSON>tom,
} from "@/app/store/store";
import {
  iUserNotification,
  iProductOwnerNotification,
  LikeNotification,
  SystemNotification,
} from "@/app/util/Interfaces";

export function useMarkAllRead() {
  const auth = useAuth();
  const setUserNotificationsAtom = useSetAtom(userNotificationsAtom);
  const setOwnerNotificationsAtom = useSetAtom(ownerNotificationsAtom);
  const setLikeNotificationsAtom = useSetAtom(likeNotificationsAtom);
  const setSystemNotificationsAtom = useSetAtom(systemNotificationsAtom);

  const handleMarkAllAsRead = async (
    userNotifications: iUserNotification[],
    ownerNotifications: iProductOwnerNotification[],
    likeNotifications: LikeNotification[],
    systemNotifications: SystemNotification[] = []
  ) => {
    try {
      // Store original notification states for potential rollback
      const originalUserNotifications = [...userNotifications];
      const originalOwnerNotifications = [...ownerNotifications];
      const originalLikeNotifications = [...likeNotifications];
      const originalSystemNotifications = [...systemNotifications];

      // Optimistically update UI
      setUserNotificationsAtom((prev: iUserNotification[]) => prev.map((n) => ({ ...n, read: true })));
      setOwnerNotificationsAtom((prev: iProductOwnerNotification[]) =>
        prev.map((n) => ({ ...n, read: true })),
      );
      setLikeNotificationsAtom((prev: LikeNotification[]) => prev.map((n) => ({ ...n, read: true })));
      setSystemNotificationsAtom((prev: SystemNotification[]) => prev.map((n) => ({ ...n, read: true })));

      // Call the API to mark all notifications as read using the corrected function
      const response = await markAllAsRead(auth.userId || "");
      // Narrow type for bulk response
      const bulk = response as import("@/app/util/notificationService").MarkAllAsReadResponse;
      if (!response.success) {
        // Revert optimistic updates if API call fails
        setUserNotificationsAtom(originalUserNotifications);
        setOwnerNotificationsAtom(originalOwnerNotifications);
        setLikeNotificationsAtom(originalLikeNotifications);
        setSystemNotificationsAtom(originalSystemNotifications);

        toast.error("Failed to mark all as read", {
          description: response.message || "Please try again later",
          duration: 4000,
        });
        console.error("Bulk mark all as read failed:", response);
      } else {
        // Show success toast with details
        toast.success(`All notifications marked as read (${bulk.total_updated} updated)`, {
          description: `User: ${bulk.user_notifications_updated}, Owner: ${bulk.owner_notifications_updated}, Like: ${bulk.like_notifications_updated}`,
          duration: 2500,
        });
      }
    } catch (error) {
      // Show error toast
      toast.error("Error marking all notifications as read", {
        description: "Please check your connection and try again",
        duration: 3000,
      });
      console.error("Error marking all notifications as read:", error);
    }
  };

  return { handleMarkAllAsRead };
}
