import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  BellIcon,
  RefreshCw,
} from "lucide-react";
import { getNotificationIcon, getNotificationTypeLabel } from "@/app/util/notificationIcons";
// Notification sections now handled in NotificationList component
import NotificationDropdown from "./NotificationDropdown";
import { useAtom, useSetAtom } from "jotai";
import { useAuth } from "@clerk/nextjs";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

import {
  iProductOwnerNotification,
  iUserNotification,
  LikeNotification,
  SystemNotification
} from "@/app/util/Interfaces";
import {
  ownerNotificationsAtom,
  userNotificationsAtom,
  likeNotificationsAtom,
  systemNotifications<PERSON>tom
} from "@/app/store/store";
import {
  isOwnerNotification,
  shouldHavePremiumStyling,
} from "@/app/util/notificationHelpers";
import { Tooltip } from "@mantine/core";
import useSSENotifications from '@/app/hooks/useSSENotifications';
import {
  getNotificationTargetUrl,
  markAsRead,
} from "@/app/util/notificationService";
import { NOTIFICATION_TYPE_MAPPING, getNotificationCategory, NotificationCategory } from "@/app/util/notificationCategory";

dayjs.extend(relativeTime);

type NotificationType =
  | iUserNotification
  | iProductOwnerNotification
  | LikeNotification
  | SystemNotification;



export default function NavNotification() {
  const auth = useAuth();
  const router = useRouter();
  const setUserNotificationsAtom = useSetAtom(userNotificationsAtom);
  const setOwnerNotificationsAtom = useSetAtom(ownerNotificationsAtom);
  const setLikeNotificationsAtom = useSetAtom(likeNotificationsAtom);
  const setSystemNotificationsAtom = useSetAtom(systemNotificationsAtom);
  const [userNotifications] = useAtom(userNotificationsAtom);
  const [ownerNotifications] = useAtom(ownerNotificationsAtom);
  const [likeNotifications] = useAtom(likeNotificationsAtom);
  const [systemNotifications] = useAtom(systemNotificationsAtom);

  // Use SSE notifications hook
  const {
    notifications: sseNotifications,
    unreadCount,
    isSSEConnected,
    isLoading,
    error,
    isUsingPolling,
    reconnect,
    refetch,
  } = useSSENotifications(auth.userId || "", { enabled: true, debug: true });

  // Update atoms when SSE notifications change
  useEffect(() => {
    if (sseNotifications) {
      setUserNotificationsAtom(sseNotifications.userNotifications || []);
      setOwnerNotificationsAtom(sseNotifications.ownerNotifications || []);
      setLikeNotificationsAtom(sseNotifications.likeNotifications || []);
      setSystemNotificationsAtom(sseNotifications.systemNotifications || []);
    }
  }, [
    sseNotifications,
    setUserNotificationsAtom,
    setOwnerNotificationsAtom,
    setLikeNotificationsAtom,
    setSystemNotificationsAtom,
  ]);

  // Handle click on the bell icon when there are no notifications
  const handleBellClick = () => {
    if (unreadCount === 0) {
      router.push("/notifications");
      return;
    }
    // Dropdown state is now handled in NotificationDropdown component
  };

  // Filter unread notifications and calculate counts
  const unreadUserNotifications = userNotifications.filter((n) => !n.read);
  const unreadOwnerNotifications = ownerNotifications.filter((n) => !n.read);
  const unreadLikeNotifications = likeNotifications.filter((n) => !n.read);
  const unreadSystemNotifications = systemNotifications.filter((n) => !n.read);

  // Use the unreadCount from SSE hook if available, otherwise calculate locally
  const totalCount =
    unreadCount !== undefined
      ? unreadCount
      : unreadUserNotifications.length +
        unreadOwnerNotifications.length +
        unreadLikeNotifications.length +
        unreadSystemNotifications.length;

  const latestUserNotifications = unreadUserNotifications.slice(0, 3);
  const latestOwnerNotifications = unreadOwnerNotifications.slice(0, 3);
  const latestLikeNotifications = unreadLikeNotifications.slice(0, 3);
  const latestSystemNotifications = unreadSystemNotifications.slice(0, 3);

  // Handle marking a notification as read and updating local state
  const handleMarkAsReadLocal = async (notificationId: string | undefined, type: "user" | "owner" | "like" | "system") => {
    if (!notificationId) return;
    try {
      // Optimistically update UI
      if (type === "user") {
        setUserNotificationsAtom((prev) =>
          prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n)),
        );
      } else if (type === "owner") {
        setOwnerNotificationsAtom((prev) =>
          prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n)),
        );
      } else if (type === "like") {
        setLikeNotificationsAtom((prev) =>
          prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n)),
        );
      }

      // Call the API to mark as read
      const response = await markAsRead(
        notificationId,
        auth.userId || "",
        type,
      );

      if (!response.success) {
        // Revert optimistic update if API call fails
        if (type === "user") {
          setUserNotificationsAtom((prev) =>
            prev.map((n) =>
              n.id === notificationId ? { ...n, read: false } : n,
            ),
          );
        } else if (type === "owner") {
          setOwnerNotificationsAtom((prev) =>
            prev.map((n) =>
              n.id === notificationId ? { ...n, read: false } : n,
            ),
          );
        } else if (type === "like") {
          setLikeNotificationsAtom((prev) =>
            prev.map((n) =>
              n.id === notificationId ? { ...n, read: false } : n,
            ),
          );
        }

        // Show error toast
        toast.error("Failed to mark notification as read", {
          description: response.message || "Please try again later",
          duration: 3000,
        });
        console.error("Failed to mark notification as read:", response);
      }
    } catch (error) {
      // Show error toast
      toast.error("Error marking notification as read", {
        description: "Please check your connection and try again",
        duration: 3000,
      });
      console.error("Error marking notification as read:", error);
    }
  };

  const handleNotificationClick = async (notification: NotificationType, type: 'user' | 'owner' | 'like' | 'system') => {
    // Use the provided type parameter directly
    const notificationType = type;

    try {
      await handleMarkAsReadLocal(notification.id, notificationType);
      // For system notifications, don't navigate - they're announcements
      if (type === 'system') {
        return;
      }
      const targetUrl = getNotificationTargetUrl(notification as Exclude<NotificationType, SystemNotification>);
      router.push(targetUrl);
      // Dropdown state is now handled in NotificationDropdown component
    } catch (error) {
      console.error(`Error handling ${notificationType} notification click:`, error);
      toast.error("Error navigating to notification target");
    }
  };

  // Show error state with retry option
  if (error) {
    return (
      <div className="relative">
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          onClick={() => router.push("/notifications")}
        >
          <BellIcon className="h-5 w-5 text-gray-400" />
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 px-1 min-w-[1.25rem] h-5"
          >
            !
          </Badge>
        </Button>
        <Tooltip
          label="Connection error. Click to retry."
          position="bottom"
          withArrow
        >
          <Button
            variant="ghost"
            size="icon"
            className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-amber-100 p-0"
            onClick={(e) => {
              e.stopPropagation();
              if (isUsingPolling) {
                refetch();
              } else {
                reconnect();
              }
            }}
          >
            <RefreshCw className="h-3 w-3 text-amber-600" />
          </Button>
        </Tooltip>
      </div>
    );
  }

  return (
    <NotificationDropdown
      userNotifications={userNotifications}
      ownerNotifications={ownerNotifications}
      likeNotifications={likeNotifications}
      systemNotifications={systemNotifications}
      unreadCount={unreadCount}
      totalCount={totalCount}
      latestUserNotifications={latestUserNotifications}
      latestOwnerNotifications={latestOwnerNotifications}
      latestLikeNotifications={latestLikeNotifications}
      latestSystemNotifications={latestSystemNotifications}
      unreadUserNotifications={unreadUserNotifications}
      unreadOwnerNotifications={unreadOwnerNotifications}
      unreadSystemNotifications={unreadSystemNotifications}
      isSSEConnected={isSSEConnected}
      isLoading={isLoading}
      onNotificationClick={handleNotificationClick}
      onMarkAsReadLocal={handleMarkAsReadLocal}
      onBellClick={handleBellClick}
    />
  );
}
