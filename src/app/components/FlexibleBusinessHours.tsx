import React, { useState } from 'react';
import { MdAccessTime, MdCalendarToday, MdExpandMore, MdExpandLess, MdHolidayVillage } from 'react-icons/md';
import { iProduct } from '../util/Interfaces';
import { cn } from '@/lib/utils';
import { BusinessHours, DayOfWeek, DAYS_OF_WEEK, createDefaultBusinessHours } from '../types/businessHours';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface FlexibleBusinessHoursProps {
  product: iProduct;
  showIcon?: boolean;
  className?: string;
  compact?: boolean;
  /**
   * When true, the compact view will expand when the user hovers over the section (desktop only).
   * Touch devices fall back to the click-to-toggle behaviour.
   */
  expandOnHover?: boolean;
}

const FlexibleBusinessHours: React.FC<FlexibleBusinessHoursProps> = ({
  product,
  showIcon = true,
  className = '',
  compact = false,
  expandOnHover = false,
}) => {
  const [expanded, setExpanded] = useState(false);
  const canHover = typeof window !== 'undefined' && window.matchMedia('(hover: hover)').matches;
  let { businessHours } = product;

  // If no business hours data, try to fall back to legacy format
  if (!businessHours && product.openingDays && product.openingHrs && product.closingHrs) {
    const tempBusinessHours = createDefaultBusinessHours();
    tempBusinessHours.schedules = tempBusinessHours.schedules.map(schedule => {
      if (product.openingDays?.includes(schedule.day)) {
        return {
          ...schedule,
          isClosed: false,
          openTime: product.openingHrs || null,
          closeTime: product.closingHrs || null
        };
      }
      return schedule;
    });
    businessHours = tempBusinessHours;
  }

  // If still no business hours data, return null
  if (!businessHours) {
    return null;
  }

  // Group days with the same hours
  const groupedSchedules = groupSchedulesByHours(businessHours.schedules);
  
  // Get today's schedule
  const today = new Date().toLocaleString('en-US', { weekday: 'long' }) as DayOfWeek;
  const todaySchedule = businessHours.schedules.find(s => s.day === today);
  
  // Format for display
  const formatTime = (time: string | null | undefined): string => {
    if (!time) return '';
    
    // Convert 24h format to 12h format
    try {
      const [hours, minutes] = time.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours % 12 || 12;
      return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    } catch (e) {
      return time; // Fallback to original format if parsing fails
    }
  };

  // Compact view shows just today's hours with expand option
  if (compact) {
    const hoverHandlers = expandOnHover && canHover ? {
      onMouseEnter: () => setExpanded(true),
      onMouseLeave: () => setExpanded(false),
    } : {};

    return (
      <div
        className={cn("flex flex-col space-y-1", className)}
        {...hoverHandlers}
      >
        {/* Today's hours */}
        <div className="flex items-center text-sm">
          {showIcon && <MdAccessTime className="shrink-0 mr-2 text-blue-500" />}
          <span className="font-medium text-gray-700">
            {todaySchedule?.isClosed 
              ? 'Closed today' 
              : `Open today: ${formatTime(todaySchedule?.openTime || null)} - ${formatTime(todaySchedule?.closeTime || null)}`}
          </span>
          {!(expandOnHover && canHover) && (
            <Button
              variant="ghost"
              size="sm"
              className="ml-1 p-0 h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                setExpanded((prev) => !prev);
              }}
            >
              {expanded ? <MdExpandLess /> : <MdExpandMore />}
            </Button>
          )}
        </div>
        
        {/* Holiday status */}
        {businessHours.openOnHolidays !== undefined && (
          <div className="flex items-center text-xs text-gray-500 ml-6">
            <MdHolidayVillage className="shrink-0 mr-1" />
            <span>{businessHours.openOnHolidays ? 'Open on holidays' : 'Closed on holidays'}</span>
          </div>
        )}
        
        {/* Expanded view */}
        {expanded && (
          <div className="mt-2 ml-6 space-y-1 text-sm">
            {groupedSchedules.map((group, index) => (
              <div key={index} className="flex items-start">
                <div className="w-24 font-medium text-gray-600">
                  {formatDayGroup(group.days)}:
                </div>
                <div className="text-gray-700">
                  {group.isClosed 
                    ? <span className="text-gray-500">Closed</span>
                    : `${formatTime(group.openTime)} - ${formatTime(group.closeTime)}`}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Full view shows all hours
  return (
    <div className={cn(
      "flex flex-col space-y-3 bg-gray-50 p-3 rounded-md",
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {showIcon && <MdCalendarToday className="shrink-0 mr-2 text-blue-500" />}
          <span className="font-medium text-gray-700">Business Hours</span>
        </div>
        
        {/* Today indicator */}
        {todaySchedule && (
          <Badge variant={todaySchedule.isClosed ? "destructive" : "outline"} className="text-xs">
            {todaySchedule.isClosed 
              ? 'Closed Today' 
              : 'Open Today'}
          </Badge>
        )}
      </div>
      
      {/* All hours by group */}
      <div className="space-y-2 ml-6">
        {groupedSchedules.map((group, index) => (
          <div key={index} className="flex items-start text-sm">
            <div className="w-24 font-medium text-gray-600">
              {formatDayGroup(group.days)}:
            </div>
            <div className="text-gray-700">
              {group.isClosed 
                ? <span className="text-gray-500">Closed</span>
                : `${formatTime(group.openTime)} - ${formatTime(group.closeTime)}`}
            </div>
          </div>
        ))}
      </div>
      
      {/* Holiday status */}
      {businessHours.openOnHolidays !== undefined && (
        <div className="flex items-center text-sm text-gray-600 ml-6 pt-1 border-t border-gray-200">
          <MdHolidayVillage className="shrink-0 mr-2 text-blue-500" />
          <span>{businessHours.openOnHolidays ? 'Open on holidays' : 'Closed on holidays'}</span>
        </div>
      )}
    </div>
  );
};

// Helper function to format a group of days
function formatDayGroup(days: DayOfWeek[]): string {
  if (days.length === 7) return "Every day";
  if (days.length === 5 && 
      days.includes('Monday') && 
      days.includes('Tuesday') && 
      days.includes('Wednesday') && 
      days.includes('Thursday') && 
      days.includes('Friday')) return "Weekdays";
  if (days.length === 2 && 
      days.includes('Saturday') && 
      days.includes('Sunday')) return "Weekends";
  
  // For 1-2 days, show the full names
  if (days.length <= 2) return days.join(', ');
  
  // For 3+ days, use short names
  return days.map(day => day.substring(0, 3)).join(', ');
}

// Helper function to group schedules with the same hours
interface GroupedSchedule {
  days: DayOfWeek[];
  isClosed: boolean;
  openTime: string | null;
  closeTime: string | null;
}

function groupSchedulesByHours(schedules: BusinessHours['schedules']): GroupedSchedule[] {
  const groups: GroupedSchedule[] = [];
  
  // Ensure schedules are sorted by day of week
  const sortedSchedules = [...schedules].sort((a, b) => {
    return DAYS_OF_WEEK.indexOf(a.day) - DAYS_OF_WEEK.indexOf(b.day);
  });
  
  sortedSchedules.forEach(schedule => {
    // Try to find an existing group with the same hours
    const existingGroup = groups.find(group => 
      group.isClosed === schedule.isClosed &&
      group.openTime === schedule.openTime &&
      group.closeTime === schedule.closeTime
    );
    
    if (existingGroup) {
      existingGroup.days.push(schedule.day);
    } else {
      groups.push({
        days: [schedule.day],
        isClosed: schedule.isClosed,
        openTime: schedule.openTime,
        closeTime: schedule.closeTime
      });
    }
  });
  
  return groups;
}

export default FlexibleBusinessHours;
