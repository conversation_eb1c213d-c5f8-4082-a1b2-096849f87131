import React, { useCallback, useRef, useEffect } from "react";
import { iComment, iUser, iProduct, iReview } from "../../util/Interfaces";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { SaveIcon } from "lucide-react";
import { toast } from "sonner";
import { canReplyToComment } from "@/app/util/commentPermissions";
import ReplyRestrictionIndicator from "../ReplyRestrictionIndicator";
import SignInToParticipate from "../SignInToParticipate";
import OwnerComment from "../OwnerComment";
import OwnerReply from "../OwnerReply";
import { isOwnerComment } from "../../util/commentHelpers";
import { Avatar as MantineAvatar, Tooltip } from "@mantine/core";

interface CommentContentProps {
  comment: iComment;
  isEditing: boolean;
  editedBody: string;
  isReplying: boolean;
  replyBody: string;
  showReplies: boolean;
  replies: iComment[];
  isCommentOwner: boolean;
  currentUser: iUser;
  product?: iProduct;
  review?: iReview;
  userId?: string;
  depth: number;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onReply: (parentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  setIsEditing: (value: boolean) => void;
  setEditedBody: (value: string) => void;
  setIsReplying: (value: boolean) => void;
  setReplyBody: (value: string) => void;
  setShowReplies: (value: boolean) => void;
  setReplies: (replies: iComment[]) => void;
}

const CommentContent: React.FC<CommentContentProps> = ({
  comment,
  isEditing,
  editedBody,
  isReplying,
  replyBody,
  showReplies,
  replies,
  isCommentOwner,
  currentUser,
  product,
  review,
  userId,
  depth,
  onEdit,
  onReply,
  onDelete,
  setIsEditing,
  setEditedBody,
  setIsReplying,
  setReplyBody,
  setShowReplies,
  setReplies,
}) => {
  const replyTextareaRef = useRef<HTMLTextAreaElement>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [replyCharCount, setReplyCharCount] = React.useState(0);
  const MAX_REPLY_LENGTH = 1000;

  // Auto-focus reply textarea when replying starts
  useEffect(() => {
    if (isReplying && replyTextareaRef.current) {
      replyTextareaRef.current.focus();
    }
  }, [isReplying]);

  // Update character count when reply body changes
  useEffect(() => {
    setReplyCharCount(replyBody.length);
  }, [replyBody]);

  // Calculate permissions for replying (memoized for performance)
  const permission = React.useMemo(() => {
    try {
      return canReplyToComment(userId ?? null, comment, comment.review as iReview, product);
    } catch (error) {
      console.warn('Error calculating reply permissions for comment:', comment.id, error);
      return { 
        allowed: false, 
        restrictionType: 'not_authenticated' as const, 
        message: 'Unable to determine reply permissions' 
      };
    }
  }, [userId, comment, product]);

  // Get upvoters for avatar display (memoized for performance)
  const getUpvoters = useCallback(() => {
    if (!comment.votes) return [];

    // Get all upvoters and sort by newest first
    let upvoters = comment.votes
      .filter((vote) => vote.voteType === "UP")
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

    // If current user upvoted, put them last (will be most visible)
    if (userId) {
      const currentUserVoteIndex = upvoters.findIndex(
        (vote) => vote.clerkUserId === userId
      );
      if (currentUserVoteIndex >= 0) {
        const currentUserVote = upvoters.splice(currentUserVoteIndex, 1)[0];
        upvoters.push(currentUserVote);
      }
    }

    // Reverse the array so newest votes (including current user) appear on top
    return upvoters.reverse();
  }, [comment.votes, userId]);

  const handleReply = useCallback(async () => {
    if (!comment.id || !permission.allowed) {
      return;
    }

    if (replyBody.trim().length === 0) {
      toast.error("Reply cannot be empty");
      return;
    }

    if (replyBody.length > MAX_REPLY_LENGTH) {
      toast.error(`Reply is too long. Maximum ${MAX_REPLY_LENGTH} characters allowed.`);
      return;
    }

    setIsSubmitting(true);

    try {
      const newReply: iComment = {
        id: Date.now().toString(),
        body: replyBody,
        user: currentUser,
        createdDate: new Date(),
        review: comment.review,
        parentId: comment.id,
        userId: currentUser?.id || '',
        isDeleted: false,
        reviewId: comment.reviewId,
        replies: [],
        upvotes: 0,
        downvotes: 0,
      };

      // Optimistically update replies
      const updatedReplies = [...replies, newReply];
      setReplies(updatedReplies);
      setIsReplying(false);
      setReplyBody("");
      setShowReplies(true);

      await onReply(comment.id, replyBody);
      toast.success("Reply added successfully!");
    } catch (error) {
      console.error('Reply error:', error);
      // Revert on error
      setReplies(replies);
      setIsReplying(true);
      setReplyBody(replyBody);
      
      if (error instanceof Error) {
        if (error.message.includes('401')) {
          toast.error("Please sign in to reply");
        } else if (error.message.includes('403')) {
          toast.error("You don't have permission to reply to this comment");
        } else {
          toast.error(error.message || "Failed to add reply. Please try again.");
        }
      } else {
        toast.error("Network error. Please check your connection.");
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [comment.id, comment.review, comment.reviewId, currentUser, permission.allowed, replyBody, replies, onReply, setReplies, setIsReplying, setReplyBody, setShowReplies]);

  const handleSave = useCallback(async () => {
    if (!comment.id || !isCommentOwner) {
      return;
    }

    if (editedBody.trim().length === 0) {
      toast.error("Comment cannot be empty");
      return;
    }

    try {
      await onEdit(comment.id, editedBody);
      setIsEditing(false);
      toast.success("Comment updated successfully!");
    } catch (error) {
      console.error('Edit error:', error);
      if (error instanceof Error) {
        toast.error(error.message || "Failed to update comment");
      } else {
        toast.error("Failed to update comment. Please try again.");
      }
    }
  }, [comment.id, isCommentOwner, editedBody, onEdit, setIsEditing]);

  const toggleReplies = useCallback(() => {
    setShowReplies(!showReplies);
  }, [showReplies, setShowReplies]);

  const handleCancelReply = useCallback(() => {
    setIsReplying(false);
    setReplyBody("");
    setReplyCharCount(0);
  }, [setIsReplying, setReplyBody]);

  if (isOwnerComment(comment, product)) {
    return <OwnerComment 
      comment={comment}
      onReply={onReply}
      onEdit={onEdit}
      onDelete={async (commentId: string) => {
        try {
          // Pass the onDelete function from props
          if (comment.id) {
            await onDelete(comment.id);
          }
        } catch (error) {
          console.error('Error deleting comment:', error);
          toast.error("Failed to delete comment. Please try again.");
        }
      }}
      depth={depth}
      clerkUserId={userId || ''}
      currentUser={currentUser}
      productName={product?.name || ''}
      review={review}
    />;
  }

  return (
    <div className="space-y-2">
      {isEditing ? (
        <div className="space-y-2">
          <Textarea
            value={editedBody}
            onChange={(e) => setEditedBody(e.target.value)}
            className="min-h-[100px]"
          />
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(false)}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              disabled={!editedBody.trim()}
            >
              <SaveIcon className="h-4 w-4 mr-1" />
              Save
            </Button>
          </div>
        </div>
      ) : (
        <>
          <p className="text-sm whitespace-pre-wrap">{comment.body}</p>
          <div className="flex items-center gap-2 mt-2">
            {!isReplying && permission.allowed && (
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-600"
                onClick={() => setIsReplying(true)}
              >
                Reply
              </Button>
            )}
            {!permission.allowed && !userId && (
              <SignInToParticipate />
            )}
            {!permission.allowed && userId && (
              <div className="text-sm text-muted-foreground">
                {permission.message || ''}
              </div>
            )}
            {replies.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleReplies}
              >
                {showReplies ? "Hide" : "Show"} {replies.length}{" "}
                {replies.length === 1 ? "reply" : "replies"}
              </Button>
            )}
          </div>
          
          {/* Upvoter avatars display */}
          {comment.votes &&
            comment.votes.some((vote) => vote.voteType === "UP") && (
              <Tooltip.Group openDelay={300} closeDelay={100}>
                <div className="flex items-center justify-end gap-2 mt-2">
                  <span className="text-xs text-gray-500">Liked by</span>
                  <MantineAvatar.Group spacing="sm">
                    {getUpvoters()
                      .slice(0, 5)
                      .map((vote) => (
                        <Tooltip
                          key={vote.id}
                          label={vote.user?.userName || "Unknown user"}
                          withArrow
                        >
                          <MantineAvatar
                            src={vote.user?.avatar || "/default-avatar.png"}
                            radius="xl"
                            size="sm"
                            alt={vote.user?.userName || "Unknown user"}
                          >
                            {vote.user?.firstName?.[0]}
                            {vote.user?.lastName?.[0]}
                          </MantineAvatar>
                        </Tooltip>
                      ))}
                    {getUpvoters().length > 5 && (
                      <Tooltip
                        label={`${getUpvoters().length - 5} more ${
                          getUpvoters().length - 5 === 1 ? "person" : "people"
                        } liked this`}
                        withArrow
                      >
                        <MantineAvatar radius="xl" size="sm">
                          +{getUpvoters().length - 5}
                        </MantineAvatar>
                      </Tooltip>
                    )}
                  </MantineAvatar.Group>
                </div>
              </Tooltip.Group>
            )}
        </>
      )}

      {isReplying && (
        <div className="space-y-2 mt-4">
          <Textarea
            ref={replyTextareaRef}
            value={replyBody}
            onChange={(e) => {
              const newValue = e.target.value;
              if (newValue.length <= MAX_REPLY_LENGTH) {
                setReplyBody(newValue);
              }
            }}
            placeholder="Write a thoughtful reply..."
            className="min-h-[100px] resize-none"
            maxLength={MAX_REPLY_LENGTH}
            aria-label="Reply to comment"
          />
          <div className="flex justify-between items-center mt-1">
            <span className={`text-xs ${
              replyCharCount > MAX_REPLY_LENGTH * 0.9 ? 'text-red-500' : 'text-gray-500'
            }`}>
              {replyCharCount}/{MAX_REPLY_LENGTH}
            </span>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancelReply}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleReply}
              disabled={!replyBody.trim() || isSubmitting || replyBody.length > MAX_REPLY_LENGTH}
              aria-label="Submit reply"
            >
              {isSubmitting ? "Posting..." : "Reply"}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommentContent;
