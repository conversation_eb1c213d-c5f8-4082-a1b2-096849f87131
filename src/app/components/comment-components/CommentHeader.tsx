import React from "react";
import { iUser } from "../../util/Interfaces";
import { Avatar, AvatarFallback, AvatarImage } from "../../../components/ui/avatar";
import OptionsMenu from "../CommentOptionsMenu";
import dayjs from "dayjs";
import Link from "next/link";
import { profileUrl } from "../../util/userHelpers";

interface CommentHeaderProps {
  user: iUser;
  createdAt: Date;
  isCommentOwner: boolean;
  onEdit: () => void;
  onDelete: () => void;
}

const CommentHeader: React.FC<CommentHeaderProps> = ({
  user,
  createdAt,
  isCommentOwner,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center gap-3">
        <Link 
          href={profileUrl({ userName: user.userName, id: user.id })}
          className="flex-shrink-0"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage 
              src={user.avatar || ""} 
              alt={`${user.userName}'s avatar`} 
            />
            <AvatarFallback className="bg-gray-500 text-white text-xs">
              {(user.userName || '')[0]?.toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </Link>
        <div className="flex flex-col">
          <Link
            href={profileUrl({ userName: user.userName, id: user.id })}
            className="font-medium text-sm hover:text-blue-600 transition-colors"
          >
            @{user.userName}
          </Link>
          <span className="text-xs text-gray-500">
            {dayjs(createdAt).format("MMM D, YYYY")}
          </span>
        </div>
      </div>
      {isCommentOwner && (
        <OptionsMenu 
          onEdit={onEdit} 
          onDelete={onDelete} 
          setIsEditing={() => {}} 
        />
      )}
    </div>
  );
};

export default CommentHeader;
