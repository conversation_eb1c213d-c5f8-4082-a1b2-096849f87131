import React, { useState, useEffect } from "react";
import { useAuth, useUser } from "@clerk/nextjs";
import { toast } from 'sonner';
import { iComment, iUser, iCommentVote, iProduct, iReview } from "../../util/Interfaces";
import { canReplyToComment } from '../../util/commentPermissions';
import CommentHeader from "./CommentHeader";
import CommentVoting from "./CommentVoting";
import CommentContent from "./CommentContent";
import OwnerReply from "../OwnerReply";
import { isOwnerComment } from "../../util/commentHelpers";
import { cn } from "@/lib/utils";
import { Button } from "../../../components/ui/button";
import { Textarea } from "../../../components/ui/textarea";
import { Avatar, AvatarImage, AvatarFallback } from "../../../components/ui/avatar";
import Link from "next/link";
import { Tooltip } from "../../../components/ui/tooltip";
import { SignInToParticipate } from "../../../components/auth/SignInToParticipate";
import { ReplyRestrictionIndicator } from "../../../components/ui/reply-restriction-indicator";
import { OptionsMenu } from "../../../components/ui/options-menu";
import { Avatar as MantineAvatar } from "@mantine/core";
import dayjs from "dayjs";
import { ReplyIcon, ArrowUpIcon, ArrowDownIcon, ChevronUpIcon, ChevronDownIcon } from "lucide-react";

// Removed local stub declarations - using proper imports instead

const profileUrl = (userId: string) => `/profile/${userId}`;

export interface CommentProps {
  comment: iComment;
  onReply: (parentId: string, body: string) => Promise<void>;
  onEdit: (commentId: string, body: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  depth?: number;
  clerkUserId: string;
  currentUser: iUser;
  product?: iProduct;
  review?: iReview;
  isUserAuthenticated?: boolean;
  children?: React.ReactNode;
}

const MAX_VISIBLE_DEPTH = 5;
const DEPTH_COLORS = [
  "border-blue-400",
  "border-green-400",
  "border-purple-400",
  "border-orange-400",
  "border-pink-400",
  "border-teal-400",
  "border-red-400",
  "border-indigo-400",
];

export const Comment: React.FC<CommentProps> = ({
  comment: initialComment,
  onReply,
  onEdit,
  onDelete,
  depth = 0,
  clerkUserId,
  currentUser: initialCurrentUser,
  product,
  review,
  isUserAuthenticated = false,
}) => {
  const { userId } = useAuth();
  const { user: clerkUser } = useUser();
  const userDatabaseId = (clerkUser?.publicMetadata?.id as string | undefined)
  const currentUser = initialCurrentUser || clerkUser;

  const [comment, setComment] = useState<iComment>(initialComment);
  const [votes, setVotes] = useState<iCommentVote[]>(comment.votes || []);
  const [isReplying, setIsReplying] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showReplies, setShowReplies] = useState(depth < MAX_VISIBLE_DEPTH);
  const [replyContent, setReplyContent] = useState('');
  const [editContent, setEditContent] = useState(comment.body);
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);
  const [isSubmittingEdit, setIsSubmittingEdit] = useState(false);
  const [isSubmittingDelete, setIsSubmittingDelete] = useState(false);
  const [isSubmittingVote, setIsSubmittingVote] = useState(false);
  const [editedBody, setEditedBody] = useState(comment.body);
  const [replyBody, setReplyBody] = useState("");
  const [replies, setReplies] = useState<iComment[]>(comment.replies || []);

  const sortVotesByDate = (a: iCommentVote, b: iCommentVote): number => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  };

  const sortVotes = (votes: iCommentVote[]): iCommentVote[] => {
    return [...votes].sort(sortVotesByDate);
  };

  useEffect(() => {
    setComment(initialComment);
    setVotes(initialComment.votes || []);
    setEditedBody(initialComment.body);
    setReplies(initialComment.replies || []);
  }, [initialComment]);

  // More robust ownership check
  const isOwner = Boolean(
    userDatabaseId === comment.userId ||
    userId === comment.user?.clerkUserId ||
    userId === comment.user?.id
  );

  // Get border color for the current depth
  const depthColor = DEPTH_COLORS[depth % DEPTH_COLORS.length] || DEPTH_COLORS[0];

  const handleVote = async (voteType: "UP" | "DOWN"): Promise<void> => {
    if (!userId || !currentUser) {
      toast.error('You must be logged in to vote');
      return;
    }

    if (isSubmittingVote) return;
    setIsSubmittingVote(true);

    try {
      const response = await fetch(`/api/vote/comment`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          commentId: comment.id,
          voteType,
          userId: currentUser.id
        })
      });

      if (!response.ok) throw new Error('Failed to vote');
      
      const data = await response.json();
      handleVoteUpdate(data.votes, data.upvotes, data.downvotes);
    } catch (error) {
      toast.error('Failed to vote on comment');
    } finally {
      setIsSubmittingVote(false);
    }
  };

  const handleVoteUpdate = (updatedVotes: iCommentVote[], upvotes: number, downvotes: number): void => {
    setComment(prev => ({
      ...prev,
      votes: updatedVotes,
      upvotes,
      downvotes
    }));
  };

  // Check if this is an owner comment to avoid duplicate rendering
  const isOwnerCommentCheck = Boolean(product && isOwnerComment(comment, product));

  return (
    <div className={cn(
      "pl-4 relative pb-2 mb-2 border-b border-gray-200/60",
      depth > 0 && `border-l-2 ${depthColor}`
    )}>
      {/* Only render CommentHeader for non-owner comments to avoid duplication */}
      {!isOwnerCommentCheck && (
        <CommentHeader
          user={comment.user || currentUser}
          createdAt={comment.createdDate}
          isCommentOwner={isOwner}
          onEdit={() => setIsEditing(true)}
          onDelete={() => comment.id ? onDelete(comment.id) : undefined}
        />
      )}

      <div className="flex flex-col w-full">
        <CommentContent
          comment={comment}
          isEditing={isEditing}
          editedBody={editedBody}
          isReplying={isReplying}
          replyBody={replyBody}
          showReplies={showReplies}
          replies={replies}
          isCommentOwner={isOwner}
          currentUser={currentUser}
          product={product}
          review={review}
          userId={userDatabaseId ?? ''}
          depth={depth}
          onEdit={onEdit}
          onReply={onReply}
          onDelete={onDelete}
          setIsEditing={setIsEditing}
          setEditedBody={setEditedBody}
          setIsReplying={setIsReplying}
          setReplyBody={setReplyBody}
          setShowReplies={setShowReplies}
          setReplies={setReplies}
        />
        
        {/* Only show voting for non-owner comments since OwnerComment handles its own layout */}
        {!isOwnerCommentCheck && (
          <div className="flex items-center gap-2 mt-1">
            <CommentVoting
              commentId={comment.id || ''}
              upvotes={comment.upvotes}
              downvotes={comment.downvotes}
              votes={comment.votes}
              userId={userDatabaseId ?? ''}
              isCommentOwner={isOwner}
              currentUser={currentUser}
              onVoteUpdate={handleVoteUpdate}
            />
          </div>
        )}
      </div>

      {showReplies && replies.length > 0 && (
        <div className="mt-4 space-y-4">
          {replies.map((reply) => {
            // Check if this is an owner reply
            const isOwnerReply = Boolean(product && isOwnerComment(reply, product));
            
            if (isOwnerReply) {
              return (
                <OwnerReply
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  depth={depth + 1}
                  clerkUserId={clerkUserId}
                  currentUser={currentUser}
                  productName={product?.name || 'Unknown Product'}
                  review={review}
                />
              );
            } else {
              return (
                <Comment
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  depth={depth + 1}
                  clerkUserId={clerkUserId}
                  currentUser={currentUser}
                  product={product}
                  review={review}
                  isUserAuthenticated={isUserAuthenticated}
                />
              );
            }
          })}
        </div>
      )}
    </div>
  );
};

export default Comment;
