import React, { useCallback, useRef } from "react";
import { iUser, iCommentVote } from "../../util/Interfaces";
import { cn } from "@/lib/utils";
import { ArrowUpIcon, ArrowDownIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";

interface CommentVotingProps {
  commentId: string;
  upvotes: number;
  downvotes: number;
  votes?: iCommentVote[];
  userId?: string;
  isCommentOwner: boolean;
  currentUser: iUser;
  onVoteUpdate: (updatedVotes: iCommentVote[], upvotes: number, downvotes: number) => void;
}

const CommentVoting: React.FC<CommentVotingProps> = ({
  commentId,
  upvotes,
  downvotes,
  votes = [],
  userId,
  isCommentOwner,
  currentUser,
  onVoteUpdate,
}) => {
  const [upvoted, setUpvoted] = React.useState(false);
  const [downvoted, setDownvoted] = React.useState(false);
  const [voteCount, setVoteCount] = React.useState(upvotes - downvotes);
  const [isVoting, setIsVoting] = React.useState(false);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  React.useEffect(() => {
    if (userId && votes) {
      const userVote = votes.find(
        (vote: iCommentVote) => vote.clerkUserId === userId
      );

      if (userVote) {
        setUpvoted(userVote.voteType === "UP");
        setDownvoted(userVote.voteType === "DOWN");
      } else {
        setUpvoted(false);
        setDownvoted(false);
      }
    } else {
      setUpvoted(false);
      setDownvoted(false);
    }
    setVoteCount(upvotes - downvotes);
  }, [votes, userId, upvotes, downvotes]);

  const handleVote = useCallback(async (voteType: "UP" | "DOWN") => {
    if (isCommentOwner) {
      toast.info("You cannot vote on your own comment.");
      return;
    }
    if (!userId) {
      toast.error("Please sign in to vote");
      return;
    }
    if (isVoting) {
      return; // Prevent multiple simultaneous votes
    }

    // Clear any existing debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    setIsVoting(true);

    const existingVote = votes?.find(
      (vote: iCommentVote) => vote.clerkUserId === userId
    );
    const isRemovingVote = existingVote?.voteType === voteType;

    // Create optimistic votes array
    let optimisticVotes = [...(votes || [])];
    let newUpvotes = upvotes;
    let newDownvotes = downvotes;

    // Remove any existing vote
    optimisticVotes = optimisticVotes.filter(
      (vote) => vote.clerkUserId !== userId
    );

    if (isRemovingVote) {
      if (voteType === "UP") {
        newUpvotes--;
        setUpvoted(false);
      } else {
        newDownvotes--;
        setDownvoted(false);
      }
    } else {
      if (voteType === "UP") {
        if (existingVote?.voteType === "DOWN") {
          newDownvotes--;
          newUpvotes++;
        } else {
          newUpvotes++;
        }
        setUpvoted(true);
        setDownvoted(false);
        optimisticVotes.push({
          id: Date.now().toString(),
          commentId,
          userId,
          clerkUserId: userId,
          voteType: "UP",
          createdAt: new Date(),
          user: currentUser,
        });
      } else {
        if (existingVote?.voteType === "UP") {
          newUpvotes--;
          newDownvotes++;
        } else {
          newDownvotes++;
        }
        setUpvoted(false);
        setDownvoted(true);
        optimisticVotes.push({
          id: Date.now().toString(),
          commentId,
          userId,
          clerkUserId: userId,
          voteType: "DOWN",
          createdAt: new Date(),
          user: currentUser,
        });
      }
    }

    // Optimistic update
    setVoteCount(newUpvotes - newDownvotes);
    onVoteUpdate(optimisticVotes, newUpvotes, newDownvotes);

    try {
      const response = await fetch("/api/vote/comment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include',
        body: JSON.stringify({
          commentId,
          voteType,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to process vote");
      }

      // If server returns updated comment data, use it
      if (data.comment) {
        onVoteUpdate(data.comment.votes || [], data.comment.upvotes || 0, data.comment.downvotes || 0);
        setVoteCount((data.comment.upvotes || 0) - (data.comment.downvotes || 0));
      }
    } catch (error) {
      console.error('Vote error:', error);
      // Revert optimistic updates on error
      setVoteCount(upvotes - downvotes);
      if (votes) {
        const userVote = votes.find(
          (vote: iCommentVote) => vote.userId === userId
        );
        setUpvoted(userVote?.voteType === "UP" || false);
        setDownvoted(userVote?.voteType === "DOWN" || false);
      }
      onVoteUpdate(votes || [], upvotes, downvotes);
      
      // More specific error messages
      if (error instanceof Error) {
        if (error.message.includes('401')) {
          toast.error("Please sign in to vote");
        } else if (error.message.includes('429')) {
          toast.error("Too many votes. Please wait a moment.");
        } else {
          toast.error(error.message || "Failed to process vote");
        }
      } else {
        toast.error("Network error. Please check your connection.");
      }
    } finally {
      setIsVoting(false);
    }
  }, [isCommentOwner, userId, isVoting, commentId, votes, upvotes, downvotes, onVoteUpdate, currentUser]);

  // Keyboard support
  const handleKeyDown = useCallback((event: React.KeyboardEvent, voteType: "UP" | "DOWN") => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleVote(voteType);
    }
  }, [handleVote]);

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "p-0 h-8 w-8 transition-colors",
          upvoted && "text-green-600 hover:text-green-600",
          isVoting && "opacity-50 cursor-not-allowed"
        )}
        onClick={() => handleVote("UP")}
        onKeyDown={(e) => handleKeyDown(e, "UP")}
        disabled={isVoting || isCommentOwner}
        aria-label={`${upvoted ? 'Remove upvote' : 'Upvote'} comment`}
        title={isCommentOwner ? "You cannot vote on your own comment" : undefined}
      >
        <ArrowUpIcon className="h-5 w-5" />
      </Button>
      <span className="text-sm font-medium min-w-[2ch] text-center">
        {voteCount}
      </span>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "p-0 h-8 w-8 transition-colors",
          downvoted && "text-red-600 hover:text-red-600",
          isVoting && "opacity-50 cursor-not-allowed"
        )}
        onClick={() => handleVote("DOWN")}
        onKeyDown={(e) => handleKeyDown(e, "DOWN")}
        disabled={isVoting || isCommentOwner}
        aria-label={`${downvoted ? 'Remove downvote' : 'Downvote'} comment`}
        title={isCommentOwner ? "You cannot vote on your own comment" : undefined}
      >
        <ArrowDownIcon className="h-5 w-5" />
      </Button>
    </div>
  );
};

export default CommentVoting;
