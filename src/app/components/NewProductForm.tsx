"use client";
import React, { useState, useRef } from "react";
import { iProduct } from "../util/Interfaces";
import { createDefaultBusinessHours } from "../types/businessHours";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { invalidateProductCaches } from "../util/cacheInvalidation";
import { Button } from "@/components/ui/button";
import ProductImageUploadModal from "./ProductImageUploadModal";
import ProductSubmissionConfirmModal from "./ProductSubmissionConfirmModal";
import BasicInformationSection from "./product-form/BasicInformationSection";
import MediaLinksSection from "./product-form/MediaLinksSection";
import CategoriesTagsSection from "./product-form/CategoriesTagsSection";
import BusinessHoursSection from "./product-form/BusinessHoursSection";
import ContactInformationSection from "./product-form/ContactInformationSection";
import LocationAddressSection from "./product-form/LocationAddressSection";

export default function ProductForm() {
  const initialProduct: iProduct = {
    address: "",        // Keep for backward compatibility
    streetAddress: "",  // NEW: Street address only
    city: "",           // NEW: City/Town/Area
    latitude: null,
    longitude: null,
    createdDate: new Date(),
    description: "",
    display_image:
      "https://res.cloudinary.com/dhglzlaqf/image/upload/v1688140420/myassets/placeholder_jpxutd.png",
    images: [],
    videos: [],
    links: [],
    name: "",
    tags: [],
    openingHrs: null,
    closingHrs: null,
    openingDays: [],
    businessHours: createDefaultBusinessHours(),
    telephone: null,
    website: [],
    rating: 0,
    hasOwner: false, // Changed default value from null to false
    ownerId: null,
    createdById: "",
    isDeleted: false,
    email: null,
  };

  const [product, setProduct] = useState<iProduct>(initialProduct);
  const [isLoading, setIsLoading] = useState(false);
  const [imageInputValue, setImageInputValue] = useState("");
  const [videoInputValue, setVideoInputValue] = useState("");
  const [linkInputValue, setLinkInputValue] = useState("");
  const [websiteInputValue, setWebsiteInputValue] = useState("");
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const router = useRouter();
  const queryClient = useQueryClient();

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setProduct({ ...product, [name]: value });
  };

  const handleArrayInput = (field: keyof iProduct, value: string) => {
    setProduct((prev) => ({
      ...prev,
      [field]: [...(prev[field] as string[]), value],
    }));
  };

  const handleRemoveArrayItem = (field: keyof iProduct, index: number) => {
    setProduct((prev) => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index),
    }));
  };

  const handleImagesUploaded = (imageUrls: string[]) => {
    setProduct((prev) => ({
      ...prev,
      images: imageUrls,
    }));
  };

  const mutations = useMutation({
    mutationFn: async () => {
      const response = await fetch("/api/create/product", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: 'include',
        body: JSON.stringify(product),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(
          data.error || `Failed to create product: ${response.status}`
        );
      }

      return data;
    },
    onSuccess: async (data) => {
      try {
        // Comprehensive cache invalidation (Redis + React Query + Next.js)
        const invalidationResult = await invalidateProductCaches({
          invalidateServer: true,
          invalidateClient: false, // We'll handle React Query manually for better error control
          queryKeys: [["products"]],
          serverActions: ["invalidate-all-products", "invalidate-search"]
        });

        // Check if server invalidation failed
        if (!invalidationResult.serverInvalidation && invalidationResult.errors.length > 0) {
          console.error("Server cache invalidation failed:", invalidationResult.errors);
          throw new Error("Failed to invalidate server cache");
        }

        // Only invalidate React Query if server invalidation succeeded
        await queryClient.invalidateQueries({ queryKey: ["products"] });
        
        const encodedProduct = encodeURIComponent(
          JSON.stringify(data.data || data)
        );
        router.push(`/mybusinesses/productsuccess?product=${encodedProduct}`);
      } catch (error) {
        console.error("Cache invalidation failed:", error);
        // Still navigate but show warning
        const encodedProduct = encodeURIComponent(
          JSON.stringify(data.data || data)
        );
        router.push(`/mybusinesses/productsuccess?product=${encodedProduct}&cacheWarning=true`);
      }
    },
    onError: (error: Error) => {
      console.error("Error creating product:", error);
      alert(`Failed to create product: ${error.message}`);
    },
  });

  const handleImageUploaded = (imageUrl: string) => {
    setProduct({ ...product, display_image: imageUrl });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that at least one opening day is selected (using flexible hours)
    const hasOpenDay = product.businessHours?.schedules?.some((s) => !s.isClosed) ?? false;
    if (!hasOpenDay) {
      alert("Please select at least one opening day.");
      return;
    }

    // Validate that at least one category tag is added
    if (!product.tags || product.tags.length === 0) {
      alert("Please add at least one category tag.");
      return;
    }

    // Show confirmation modal instead of directly submitting
    setIsConfirmModalOpen(true);
  };

  const handleConfirmSubmit = () => {
    setIsConfirmModalOpen(false);
    mutations.mutate();
  };

  const handleLocationSelect = (location: {
    lat: number;
    lng: number;
    address: string;
  }) => {
    setProduct({
      ...product,
      latitude: location.lat,
      longitude: location.lng,
      address: location.address,
    });
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="max-w-4xl w-full mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden"
    >
      {/* Form Header */}
      <div className="bg-gradient-to-r from-myTheme-primary to-myTheme-secondary p-6 sm:p-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">Create New Product</h1>
        <p className="text-white/80">Fill in the details below to add your business to our platform</p>
      </div>
      
      {/* Form Content */}
      <div className="p-6 sm:p-8">
        <div className="space-y-8">
          <div className="space-y-6">
            <BasicInformationSection
              product={product}
              onChange={handleChange}
            />

            <MediaLinksSection
              product={product}
              onImageUploaded={handleImageUploaded}
              onOpenImageModal={() => setIsImageModalOpen(true)}
              onArrayInput={handleArrayInput}
              onRemoveArrayItem={handleRemoveArrayItem}
              videoInputValue={videoInputValue}
              setVideoInputValue={setVideoInputValue}
              linkInputValue={linkInputValue}
              setLinkInputValue={setLinkInputValue}
              websiteInputValue={websiteInputValue}
              setWebsiteInputValue={setWebsiteInputValue}
            />

            <CategoriesTagsSection
              product={product}
              onArrayInput={handleArrayInput}
              onRemoveArrayItem={handleRemoveArrayItem}
            />

            <BusinessHoursSection
              businessHours={product.businessHours || createDefaultBusinessHours()}
              onChange={(hours) => {
                const openDays = hours.schedules
                  .filter((s) => !s.isClosed)
                  .map((s) => s.day);
                setProduct({ ...product, businessHours: hours, openingDays: openDays });
              }}
            />

            <ContactInformationSection
              product={product}
              onChange={handleChange}
            />

            <LocationAddressSection
              product={product}
              onChange={handleChange}
              onLocationSelect={handleLocationSelect}
              onCityChange={(city) => setProduct(prev => ({ ...prev, city }))}
            />
          </div>
        </div>
      </div>
      
      {/* Submit Section */}
      <div className="bg-gradient-to-r from-myTheme-lightbg to-gray-50 p-6 sm:p-8 border-t border-gray-200 mt-8">
        <div className="text-center">
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full sm:w-auto px-12 py-3 bg-gradient-to-r from-myTheme-primary to-myTheme-secondary hover:from-myTheme-primary/90 hover:to-myTheme-secondary/90 text-white font-semibold text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Creating Product...
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <span>🚀 Create This Product</span>
              </div>
            )}
          </Button>
          
          <p className="text-sm text-gray-600 mt-4">
            By submitting, you agree to our terms of service and privacy policy.
          </p>
        </div>
      </div>
      
      {/* Product Image Upload Modal */}
      <ProductImageUploadModal
        isOpen={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        onImagesUploaded={handleImagesUploaded}
        existingImages={product.images}
        maxImages={10}
      />

      {/* Product Submission Confirmation Modal */}
      <ProductSubmissionConfirmModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmSubmit}
        product={product}
        isLoading={mutations.isPending}
      />
    </form>
  );
}