'use client';

import { Widget, Business, Product } from '@prisma/client';
import { renderReviewBody } from '../../../util/htmlSanitizer';
import { calculateWeightedRating } from '@/app/util/calculateWeightedRating';
import RatingDisplayWithThreshold from '@/app/components/RatingDisplayWithThreshold';

interface MiniReviewWidgetProps {
  widget: Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: any;
    businessRating?: number;
    totalReviews?: number;
  };
  onWidgetClick: (elementType: string) => void;
}

export function MiniReviewWidget({ widget, onWidgetClick }: MiniReviewWidgetProps) {
  const reviews = widget.product?.reviews || [];
  const latestReview = reviews[0]; // Get the most recent review
  
  const reviewCount = widget.product?._count?.reviews || widget.totalReviews || 0;
  const rating = reviewCount > 0 ? (widget.product?.rating || widget.businessRating || 0) : 0;
  const businessName = widget.business.ownerName || 'Business';
  const productName = widget.product?.name;

  // Calculate weighted rating with proper validation
  const ratingData = calculateWeightedRating(
    widget.product?.reviews || [],
    {
      actualReviewCount: reviewCount
    }
  );

  const containerStyle: React.CSSProperties = {
    backgroundColor: widget.theme === 'dark' ? '#1f2937' : '#ffffff',
    color: widget.theme === 'dark' ? '#ffffff' : '#374151',
    border: `1px solid ${widget.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
    borderRadius: widget.borderRadius,
    padding: '12px',
    maxWidth: '300px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: '14px',
    lineHeight: '1.4',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '8px',
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '12px',
    fontWeight: '600',
    color: widget.theme === 'dark' ? '#d1d5db' : '#6b7280',
    margin: 0,
  };

  const ratingContainerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    marginBottom: latestReview ? '8px' : '0',
  };

  const ratingTextStyle: React.CSSProperties = {
    fontSize: '12px',
    color: widget.theme === 'dark' ? '#d1d5db' : '#6b7280',
    marginLeft: '4px',
  };

  const reviewStyle: React.CSSProperties = {
    fontSize: '13px',
    lineHeight: '1.4',
    color: widget.theme === 'dark' ? '#e5e7eb' : '#374151',
    marginBottom: '6px',
  };

  const reviewerStyle: React.CSSProperties = {
    fontSize: '11px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
    fontWeight: '500',
  };

  const handleClick = () => {
    onWidgetClick('mini-review');
    // Optionally redirect to product page
    if (widget.product?.id) {
      window.open(`https://reviewit.gy/product/${widget.product.id}`, '_blank');
    }
  };

  return (
    <div 
      style={containerStyle}
      onClick={handleClick}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-1px)';
        e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
      }}
    >
      <div style={headerStyle}>
        <h4 style={titleStyle}>
          {productName || businessName}
        </h4>
        {widget.business.isVerified && (
          <span style={{ 
            fontSize: '10px', 
            color: '#10b981',
            fontWeight: '600'
          }}>
            ✓ Verified
          </span>
        )}
      </div>

      <div style={ratingContainerStyle}>
        <RatingDisplayWithThreshold
          ratingData={ratingData}
          size="xs"
          showReviewCount={true}
          showConfidence={false}
          minimumReviewsMessage="Not enough reviews"
          className="text-xs"
        />
      </div>

      {latestReview && widget.showReviewText && (
        <>
          <div style={reviewStyle}>
            <span>
               "
               <span 
                 dangerouslySetInnerHTML={renderReviewBody(
                   latestReview.body?.length > 80 
                     ? latestReview.body.substring(0, 80) + '...' 
                     : latestReview.body
                 ) || { __html: '' }}
               />
               "
             </span>
          </div>
          {widget.showReviewerName && (
            <div style={reviewerStyle}>
              - {latestReview.user?.firstName} {latestReview.user?.lastName?.charAt(0)}.
              {widget.showReviewDate && latestReview.createdDate && (
                <span style={{ marginLeft: '4px' }}>
                  • {new Date(latestReview.createdDate).toLocaleDateString()}
                </span>
              )}
            </div>
          )}
        </>
      )}

      {!latestReview && (
        <div style={{
          fontSize: '12px',
          color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
          fontStyle: 'italic'
        }}>
          Be the first to leave a review!
        </div>
      )}
    </div>
  );
}