'use client';

import { useState } from 'react';
import { Widget, Business, Product } from '@prisma/client';
import { renderReviewBody } from '../../../util/htmlSanitizer';
import { calculateWeightedRating } from '@/app/util/calculateWeightedRating';
import RatingDisplayWithThreshold from '@/app/components/RatingDisplayWithThreshold';

interface ReviewPopupWidgetProps {
  widget: Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: any;
    businessRating?: number;
    totalReviews?: number;
  };
  onWidgetClick: (elementType: string) => void;
}

export function ReviewPopupWidget({ widget, onWidgetClick }: ReviewPopupWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const reviews = widget.product?.reviews || [];
  const displayReviews = reviews.slice(0, widget.maxReviews);
  
  const reviewCount = widget.product?._count?.reviews || widget.totalReviews || 0;
  const rating = reviewCount > 0 ? (widget.product?.rating || widget.businessRating || 0) : 0;
  const businessName = widget.business.ownerName || 'Business';
  const productName = widget.product?.name;

  // Calculate weighted rating with proper validation
  const ratingData = calculateWeightedRating(
    widget.product?.reviews || [],
    {
      actualReviewCount: reviewCount
    }
  );

  // Generate star display for individual reviews (this is acceptable as it's for individual reviews, not product rating)
  const renderIndividualReviewStars = (rating: number, size: 'small' | 'medium' = 'small') => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const starSize = size === 'medium' ? '16px' : '12px';

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <span key={i} style={{ color: widget.primaryColor || '#fbbf24', fontSize: starSize }}>★</span>
      );
    }

    if (hasHalfStar) {
      stars.push(
        <span key="half" style={{ color: widget.primaryColor || '#fbbf24', fontSize: starSize }}>☆</span>
      );
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <span key={`empty-${i}`} style={{ color: '#e5e7eb', fontSize: starSize }}>☆</span>
      );
    }

    return stars;
  };

  const triggerStyle: React.CSSProperties = {
    backgroundColor: widget.primaryColor || '#3b82f6',
    color: '#ffffff',
    border: 'none',
    borderRadius: widget.borderRadius,
    padding: '8px 16px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  };

  const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 9999,
    padding: '20px',
  };

  const modalStyle: React.CSSProperties = {
    backgroundColor: widget.theme === 'dark' ? '#1f2937' : '#ffffff',
    color: widget.theme === 'dark' ? '#ffffff' : '#374151',
    borderRadius: widget.borderRadius,
    padding: '24px',
    maxWidth: '500px',
    width: '100%',
    maxHeight: '80vh',
    overflowY: 'auto',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '20px',
  };

  const closeButtonStyle: React.CSSProperties = {
    background: 'none',
    border: 'none',
    fontSize: '24px',
    cursor: 'pointer',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
    padding: '0',
    lineHeight: '1',
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '18px',
    fontWeight: '700',
    margin: '0 0 8px 0',
    color: widget.theme === 'dark' ? '#ffffff' : '#111827',
  };

  const ratingContainerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: '20px',
  };

  const reviewItemStyle: React.CSSProperties = {
    borderBottom: `1px solid ${widget.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
    paddingBottom: '16px',
    marginBottom: '16px',
  };

  const reviewHeaderStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '8px',
  };

  const reviewerStyle: React.CSSProperties = {
    fontSize: '14px',
    fontWeight: '600',
    color: widget.theme === 'dark' ? '#e5e7eb' : '#374151',
  };

  const reviewDateStyle: React.CSSProperties = {
    fontSize: '12px',
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
  };

  const reviewTextStyle: React.CSSProperties = {
    fontSize: '14px',
    lineHeight: '1.5',
    color: widget.theme === 'dark' ? '#d1d5db' : '#4b5563',
    marginTop: '8px',
  };

  const handleTriggerClick = () => {
    setIsOpen(true);
    onWidgetClick('popup-trigger');
  };

  const handleClose = () => {
    setIsOpen(false);
    onWidgetClick('popup-close');
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleViewAllClick = () => {
    onWidgetClick('view-all');
    if (widget.product?.id) {
      window.open(`https://reviewit.gy/product/${widget.product.id}`, '_blank');
    }
  };

  return (
    <>
      <button
        style={triggerStyle}
        onClick={handleTriggerClick}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-1px)';
          e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
        }}
      >
        ⭐ See Reviews ({reviewCount})
      </button>

      {isOpen && (
        <div style={overlayStyle} onClick={handleOverlayClick}>
          <div style={modalStyle}>
            <div style={headerStyle}>
              <div>
                <h3 style={titleStyle}>
                  {productName || businessName}
                  {widget.business.isVerified && (
                    <span style={{ 
                      marginLeft: '8px',
                      fontSize: '14px', 
                      color: '#10b981',
                      fontWeight: '600'
                    }}>
                      ✓ Verified
                    </span>
                  )}
                </h3>
                <div style={ratingContainerStyle}>
                  <RatingDisplayWithThreshold
                    ratingData={ratingData}
                    size="sm"
                    showReviewCount={false}
                    showConfidence={false}
                    minimumReviewsMessage="Not enough reviews"
                    className="flex items-center"
                  />
                  <span style={{ 
                    fontSize: '14px',
                    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}>
                    ({reviewCount} review{reviewCount !== 1 ? 's' : ''})
                  </span>
                </div>
              </div>
              <button style={closeButtonStyle} onClick={handleClose}>
                ×
              </button>
            </div>

            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {displayReviews.length > 0 ? (
                displayReviews.map((review: any, index: number) => (
                  <div key={review.id || index} style={reviewItemStyle}>
                    <div style={reviewHeaderStyle}>
                      <div>
                        {widget.showReviewerName && (
                          <div style={reviewerStyle}>
                            {review.user?.firstName} {review.user?.lastName?.charAt(0)}.
                          </div>
                        )}
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '4px' }}>
                          {renderIndividualReviewStars(review.rating)}
                          {widget.showReviewDate && review.createdDate && (
                            <span style={reviewDateStyle}>
                              {new Date(review.createdDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    {widget.showReviewText && review.body && (
                      <div 
                        style={reviewTextStyle}
                        dangerouslySetInnerHTML={renderReviewBody(review.body) || { __html: '' }}
                      />
                    )}
                  </div>
                ))
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 20px',
                  color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280',
                  fontSize: '14px'
                }}>
                  No reviews yet. Be the first to leave a review!
                </div>
              )}
            </div>

            {reviewCount > displayReviews.length && (
              <div style={{ 
                marginTop: '20px', 
                textAlign: 'center',
                borderTop: `1px solid ${widget.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
                paddingTop: '16px'
              }}>
                <button
                  style={{
                    ...triggerStyle,
                    backgroundColor: 'transparent',
                    color: widget.primaryColor || '#3b82f6',
                    border: `1px solid ${widget.primaryColor || '#3b82f6'}`,
                    fontSize: '14px',
                    padding: '8px 16px'
                  }}
                  onClick={handleViewAllClick}
                >
                  View All Reviews
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}