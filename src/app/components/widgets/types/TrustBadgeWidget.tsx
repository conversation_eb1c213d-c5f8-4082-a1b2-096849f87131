'use client';

import { Widget, Business, Product } from '@prisma/client';
import { calculateWeightedRating } from '@/app/util/calculateWeightedRating';
import RatingDisplayWithThreshold from '@/app/components/RatingDisplayWithThreshold';

interface TrustBadgeWidgetProps {
  widget: Widget & {
    business: Pick<Business, 'id' | 'ownerName' | 'isVerified'>;
    product?: any;
    businessRating?: number;
    totalReviews?: number;
  };
  onWidgetClick: (elementType: string) => void;
}

export function TrustBadgeWidget({ widget, onWidgetClick }: TrustBadgeWidgetProps) {
  const reviewCount = widget.product?._count?.reviews || widget.totalReviews || 0;
  const rating = reviewCount > 0 ? (widget.product?.rating || widget.businessRating || 0) : 0;
  const businessName = widget.business.ownerName || 'Business';
  const productName = widget.product?.name;

  // Calculate weighted rating with proper validation
  const ratingData = calculateWeightedRating(
    widget.product?.reviews || [],
    {
      actualReviewCount: reviewCount
    }
  );

  const containerStyle: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 12px',
    borderRadius: widget.borderRadius || '20px',
    border: '1px solid #e5e7eb',
    backgroundColor: widget.theme === 'dark' ? '#1f2937' : '#ffffff',
    color: widget.theme === 'dark' ? '#ffffff' : '#333333',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    fontSize: '12px',
    fontWeight: '500',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    whiteSpace: 'nowrap'
  };

  const badgeIconStyle: React.CSSProperties = {
    width: '16px',
    height: '16px',
    borderRadius: '50%',
    backgroundColor: widget.primaryColor || '#10b981',
    color: '#ffffff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '10px',
    fontWeight: '600'
  };

  const ratingTextStyle: React.CSSProperties = {
    color: widget.theme === 'dark' ? '#ffffff' : '#1f2937',
    fontWeight: '600'
  };

  const reviewCountStyle: React.CSSProperties = {
    color: widget.theme === 'dark' ? '#9ca3af' : '#6b7280'
  };

  return (
    <div 
      style={containerStyle}
      onClick={() => onWidgetClick('trust-badge')}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        e.currentTarget.style.transform = 'translateY(-1px)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      {/* Trust Icon */}
      <div style={badgeIconStyle}>
        ✓
      </div>

      {/* Rating */}
      {widget.showRating && (
        <RatingDisplayWithThreshold
          ratingData={ratingData}
          size="xs"
          showReviewCount={false}
          showConfidence={false}
          minimumReviewsMessage="Not enough reviews"
          className="text-xs flex items-center"
        />
      )}

      {/* Review Count */}
      <div style={reviewCountStyle}>
        {reviewCount === 0 ? (
          'Verified'
        ) : reviewCount === 1 ? (
          '1 review'
        ) : (
          `${reviewCount > 999 ? `${Math.floor(reviewCount / 1000)}k` : reviewCount} reviews`
        )}
      </div>

      {/* Verified Badge */}
      {widget.business.isVerified && (
        <div style={{
          fontSize: '10px',
          color: '#10b981',
          fontWeight: '600'
        }}>
          VERIFIED
        </div>
      )}
    </div>
  );
}