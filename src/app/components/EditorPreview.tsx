"use client";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment, Suspense, useState } from "react";
import parse from "html-react-parser";
import { ReadOnlyRating } from "./RatingSystem";
import { iReview } from "../util/Interfaces";
import { useUser } from "@/app/hooks/useUser";
import { useUser as useClerkUser } from "@clerk/nextjs";
import VideoEmbed from "./VideoEmbed";

interface EditorPreviewProps {
  reviewData: iReview;
}

const EditorPreview = ({ reviewData }: EditorPreviewProps) => {
  const { user } = useUser();
  const { user: clerkUser } = useClerkUser();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const handleParse = (node: any): any => {
    if (
      node.type === "tag" &&
      node.name === "p" &&
      (!node.children || node.children.length === 0)
    ) {
      return <br />;
    }
    return undefined;
  };

  const options = {
    replace: handleParse,
  };

  const closeModal = () => setIsOpen(false);
  const openModal = () => setIsOpen(true);

  const hasContent = reviewData.title || reviewData.body || 
                    reviewData.images.length > 0 || reviewData.videos.length > 0;

  return (
    <>
      <div className="flex flex-col w-full">
        <button
          type="button"
          onClick={openModal}
          disabled={!hasContent}
          className={`font-semibold py-4 px-8 rounded-xl shadow-lg transition-all duration-300 flex items-center justify-center gap-3 ${
            hasContent
              ? "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white hover:shadow-xl transform hover:scale-105"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }`}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          {hasContent ? "Preview Your Review" : "Add Content to Preview"}
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog onClose={closeModal} className="relative z-50">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-4xl max-h-[90vh] overflow-y-auto transform rounded-3xl bg-white shadow-2xl transition-all">
                  {/* Header */}
                  <div className="sticky top-0 bg-white/95 backdrop-blur-sm border-b border-gray-200 p-6 rounded-t-3xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900">Review Preview</h3>
                      </div>
                      <button
                        onClick={closeModal}
                        className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                      >
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-8">
                    <div className="max-w-3xl mx-auto">
                      {/* Review Header */}
                      <div className="text-center mb-8">
                        {reviewData.title && (
                          <h1 className="text-3xl font-bold text-gray-900 mb-4 leading-tight">
                            {parse(reviewData.title)}
                          </h1>
                        )}

                        <div className="flex items-center justify-center gap-4 mb-6">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-sm font-bold">
                                {(user?.firstName?.[0] || clerkUser?.firstName?.[0] || 'U').toUpperCase()}
                              </span>
                            </div>
                            <span className="font-medium text-gray-700">
                              {user?.userName || `${clerkUser?.firstName || ''} ${clerkUser?.lastName || ''}`.trim() || 'Anonymous'}
                            </span>
                          </div>
                          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                          <span className="text-gray-500 text-sm">
                            {new Date().toLocaleDateString()}
                          </span>
                        </div>

                        <div className="flex justify-center">
                          <Suspense fallback={
                            <div className="flex items-center gap-1">
                              {[...Array(5)].map((_, i) => (
                                <div key={i} className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                              ))}
                            </div>
                          }>
                            <ReadOnlyRating
                              name="rating"
                              rating={reviewData.rating}
                              size="xl"
                            />
                          </Suspense>
                        </div>
                      </div>

                      {/* Review Body */}
                      {reviewData.body && (
                        <div className="mb-8">
                          <div className="prose prose-lg prose-gray max-w-none leading-relaxed">
                            {parse(reviewData.body, options)}
                          </div>
                        </div>
                      )}

                      {/* Media Section */}
                      {(reviewData.images.length > 0 || reviewData.videos.length > 0) && (
                        <div className="space-y-8">
                          {/* Images */}
                          {reviewData.images.length > 0 && (
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                Photos ({reviewData.images.length})
                              </h4>
                              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                {reviewData.images.map((image, index) => (
                                  <div 
                                    key={index} 
                                    className="relative aspect-square rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                                    onClick={() => setSelectedImage(image)}
                                  >
                                    <img
                                      src={image}
                                      alt={`Review image ${index + 1}`}
                                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                    />
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                                      <svg className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                      </svg>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Videos */}
                          {reviewData.videos.length > 0 && reviewData.videos.some(video => video.trim()) && (
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                                Videos
                              </h4>
                              <div className="space-y-6">
                                {reviewData.videos.filter(video => video.trim()).map((video, index) => (
                                  <div key={index} className="bg-gray-50 rounded-xl p-4">
                                    <VideoEmbed url={video} />
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="sticky bottom-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 p-6 rounded-b-3xl">
                    <div className="flex justify-center">
                      <button
                        onClick={closeModal}
                        className="px-8 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                      >
                        Close Preview
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Image Lightbox */}
      {selectedImage && (
        <Transition appear show={!!selectedImage} as={Fragment}>
          <Dialog onClose={() => setSelectedImage(null)} className="relative z-[60]">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" />
            </Transition.Child>

            <div className="fixed inset-0 overflow-y-auto">
              <div className="flex min-h-full items-center justify-center p-4">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <Dialog.Panel className="relative max-w-4xl max-h-[90vh]">
                    <button
                      onClick={() => setSelectedImage(null)}
                      className="absolute -top-12 right-0 w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors z-10"
                    >
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                    <img
                      src={selectedImage}
                      alt="Full size preview"
                      className="max-w-full max-h-[90vh] object-contain rounded-lg shadow-2xl"
                    />
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>
      )}
    </>
  );
};

export default EditorPreview;
