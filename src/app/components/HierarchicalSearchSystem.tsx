'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Star, MapPin, Phone, Globe, ChevronDown, ChevronRight, Building2, Tag, Users, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

// TypeScript Interfaces
interface BusinessNode {
  id: string;
  name: string;
  rating: number;
  reviewCount: number;
  category: string;
  address: string;
  phone?: string;
  website?: string;
  description?: string;
  tags?: string[];
}

interface HierarchicalNode {
  id: string;
  name: string;
  type: 'sector' | 'subsector' | 'industry' | 'business';
  children?: HierarchicalNode[];
  businesses?: BusinessNode[];
  icon?: React.ReactNode;
  color?: string;
}

interface HierarchicalSearchConfig {
  placeholder?: string;
  enableSearch?: boolean;
  enableFilters?: boolean;
  maxDepth?: number;
}

// Mock Data
const mockHierarchicalData: HierarchicalNode[] = [
  {
    id: 'sector-tech',
    name: 'Technology',
    type: 'sector',
    icon: <TrendingUp className="w-5 h-5" />,
    color: 'bg-blue-500',
    children: [
      {
        id: 'subsector-software',
        name: 'Software Development',
        type: 'subsector',
        children: [
          {
            id: 'industry-saas',
            name: 'SaaS Platforms',
            type: 'industry',
            businesses: [
              {
                id: 'business-1',
                name: 'CloudFlow Solutions',
                rating: 4.8,
                reviewCount: 342,
                category: 'Cloud Infrastructure',
                address: '123 Tech Avenue, San Francisco, CA',
                phone: '(*************',
                website: 'cloudflow.com',
                description: 'Leading cloud infrastructure provider with cutting-edge solutions',
                tags: ['Cloud', 'Enterprise', 'Scalable']
              },
              {
                id: 'business-2',
                name: 'DataSync Pro',
                rating: 4.6,
                reviewCount: 189,
                category: 'Data Management',
                address: '456 Innovation Blvd, Palo Alto, CA',
                phone: '(*************',
                website: 'datasyncpro.com',
                description: 'Advanced data synchronization and management platform',
                tags: ['Data', 'Analytics', 'Real-time']
              }
            ]
          },
          {
            id: 'industry-mobile',
            name: 'Mobile Apps',
            type: 'industry',
            businesses: [
              {
                id: 'business-3',
                name: 'AppCrafters Studio',
                rating: 4.9,
                reviewCount: 267,
                category: 'Mobile Development',
                address: '789 Mobile Way, Mountain View, CA',
                phone: '(*************',
                website: 'appcrafters.com',
                description: 'Award-winning mobile app development agency',
                tags: ['iOS', 'Android', 'UX/UI']
              }
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'sector-food',
    name: 'Food & Beverage',
    type: 'sector',
    icon: <Building2 className="w-5 h-5" />,
    color: 'bg-orange-500',
    children: [
      {
        id: 'subsector-restaurants',
        name: 'Restaurants',
        type: 'subsector',
        children: [
          {
            id: 'industry-fine-dining',
            name: 'Fine Dining',
            type: 'industry',
            businesses: [
              {
                id: 'business-4',
                name: 'The Golden Fork',
                rating: 4.7,
                reviewCount: 523,
                category: 'Fine Dining',
                address: '321 Gourmet Street, New York, NY',
                phone: '(*************',
                website: 'goldenfork.com',
                description: 'Exquisite fine dining experience with world-class cuisine',
                tags: ['Fine Dining', 'Michelin', 'Romantic']
              }
            ]
          }
        ]
      }
    ]
  }
];

// Helper Components
const StarRating: React.FC<{ rating: number; size?: 'sm' | 'md' | 'lg' }> = ({ rating, size = 'sm' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            sizeClasses[size],
            star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
          )}
        />
      ))}
      <span className="text-sm font-medium ml-1">{rating.toFixed(1)}</span>
    </div>
  );
};

const BusinessCard: React.FC<{ business: BusinessNode; onClick: () => void }> = ({ business, onClick }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 p-6 cursor-pointer border border-gray-100"
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">{business.name}</h3>
          <p className="text-sm text-gray-600">{business.category}</p>
        </div>
        <div className="flex items-center gap-2">
          <StarRating rating={business.rating} size="sm" />
          <span className="text-sm text-gray-500">({business.reviewCount})</span>
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <MapPin className="w-4 h-4" />
          <span>{business.address}</span>
        </div>
        {business.phone && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="w-4 h-4" />
            <span>{business.phone}</span>
          </div>
        )}
      </div>
      
      {business.tags && business.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-4">
          {business.tags.map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>
      )}
    </motion.div>
  );
};

const HierarchyNode: React.FC<{
  node: HierarchicalNode;
  level: number;
  onBusinessClick: (business: BusinessNode) => void;
  searchTerm?: string;
  ancestorMatches?: boolean;
}> = ({ node, level, onBusinessClick, searchTerm, ancestorMatches = false }) => {
  const [isExpanded, setIsExpanded] = useState(level < 2);
  
  const filteredChildren = useMemo(() => {
    if (!searchTerm) return node.children || [];
    
    const searchLower = searchTerm.toLowerCase();
    
    // Check if current node matches (only for sectors/subsectors)
    const currentNodeMatches = (node.type === 'sector' || node.type === 'subsector') &&
                               node.name.toLowerCase().includes(searchLower);
    
    // If current node matches or ancestor matches, show ALL children
    if (currentNodeMatches || ancestorMatches) {
      return node.children || [];
    }
    
    // Otherwise, recursively filter children
    return (node.children || []).filter(child => {
      if (child.type === 'sector' || child.type === 'subsector') {
        return child.name.toLowerCase().includes(searchLower);
      }
      // For industries, they should be shown if any ancestor matches
      return false; // This will be handled by the ancestorMatches prop
    });
  }, [node.children, searchTerm, node.name, node.type, ancestorMatches]);

  const filteredBusinesses = useMemo(() => {
    if (!searchTerm) return node.businesses || [];
    
    const searchLower = searchTerm.toLowerCase();
    
    // Check if current node matches (only for sectors/subsectors)
    const currentNodeMatches = (node.type === 'sector' || node.type === 'subsector') &&
                               node.name.toLowerCase().includes(searchLower);
    
    // Show all businesses if current node matches or ancestor matches
    if (currentNodeMatches || ancestorMatches) {
      return node.businesses || [];
    }
    
    return [];
  }, [node.businesses, searchTerm, node.name, node.type, ancestorMatches]);

  const hasVisibleContent = filteredChildren.length > 0 || filteredBusinesses.length > 0;

  return (
    <div className={cn('transition-all duration-200', level > 0 && 'ml-6')}>
      <div
        className={cn(
          'flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200',
          'hover:bg-gray-50 border border-gray-100',
          node.color && `${node.color} bg-opacity-10`
        )}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className={cn(
          'flex items-center justify-center w-8 h-8 rounded-full',
          node.color || 'bg-gray-100'
        )}>
          {node.icon || <Building2 className="w-4 h-4 text-white" />}
        </div>
        
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">{node.name}</h3>
          <p className="text-sm text-gray-600 capitalize">{node.type}</p>
        </div>
        
        {(node.children || node.businesses) && (
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-400" />
          </motion.div>
        )}
      </div>
      
      <AnimatePresence>
        {isExpanded && hasVisibleContent && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="mt-4 space-y-4">
              {filteredChildren.map((child) => {
                const searchLower = searchTerm?.toLowerCase() || '';
                const currentNodeMatches = (node.type === 'sector' || node.type === 'subsector') &&
                                         node.name.toLowerCase().includes(searchLower);
                const childAncestorMatches = ancestorMatches || currentNodeMatches;
                
                return (
                  <HierarchyNode
                    key={child.id}
                    node={child}
                    level={level + 1}
                    onBusinessClick={onBusinessClick}
                    searchTerm={searchTerm}
                    ancestorMatches={childAncestorMatches}
                  />
                );
              })}
              
              {filteredBusinesses.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                  {filteredBusinesses.map((business) => (
                    <BusinessCard
                      key={business.id}
                      business={business}
                      onClick={() => onBusinessClick(business)}
                    />
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const BusinessModal: React.FC<{
  business: BusinessNode | null;
  isOpen: boolean;
  onClose: () => void;
}> = ({ business, isOpen, onClose }) => {
  if (!business || !isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">{business.name}</h2>
                  <p className="text-gray-600">{business.category}</p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronDown className="w-5 h-5 rotate-45" />
                </button>
              </div>
              
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <StarRating rating={business.rating} size="md" />
                  <span className="text-gray-600">{business.reviewCount} reviews</span>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">{business.address}</span>
                  </div>
                  
                  {business.phone && (
                    <div className="flex items-center gap-3">
                      <Phone className="w-5 h-5 text-gray-400" />
                      <a href={`tel:${business.phone}`} className="text-blue-600 hover:underline">
                        {business.phone}
                      </a>
                    </div>
                  )}
                  
                  {business.website && (
                    <div className="flex items-center gap-3">
                      <Globe className="w-5 h-5 text-gray-400" />
                      <a href={`https://${business.website}`} className="text-blue-600 hover:underline">
                        {business.website}
                      </a>
                    </div>
                  )}
                </div>
                
                {business.description && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">About</h3>
                    <p className="text-gray-700">{business.description}</p>
                  </div>
                )}
                
                {business.tags && business.tags.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {business.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Main Component
const HierarchicalSearchSystem: React.FC<{
  config?: HierarchicalSearchConfig;
  data?: HierarchicalNode[];
}> = ({ config = {}, data = mockHierarchicalData }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBusiness, setSelectedBusiness] = useState<BusinessNode | null>(null);

  const filteredData = useMemo(() => {
    if (!searchTerm) return data;
    
    const searchLower = searchTerm.toLowerCase();
    
    const filterNode = (node: HierarchicalNode, ancestorMatches: boolean = false): HierarchicalNode | null => {
      // Check if current node matches (only for sectors and subsectors)
      const nodeMatches = (node.type === 'sector' || node.type === 'subsector') &&
                          node.name.toLowerCase().includes(searchLower);
      
      // If this node or an ancestor matches, include ALL children
      const shouldShowAllChildren = nodeMatches || ancestorMatches;
      
      let filteredChildren: HierarchicalNode[] | undefined;
      
      if (shouldShowAllChildren) {
        // Show all children when this node or ancestor matches
        filteredChildren = node.children?.map(child => filterNode(child, true)).filter(Boolean) as HierarchicalNode[] | undefined;
      } else {
        // Otherwise, recursively filter children
        filteredChildren = node.children?.map(child => filterNode(child, false)).filter(Boolean) as HierarchicalNode[] | undefined;
      }
      
      // Show node if it matches, ancestor matches, or has matching children
      if (nodeMatches || ancestorMatches || (filteredChildren && filteredChildren.length > 0)) {
        return {
          ...node,
          children: filteredChildren,
          // Include all businesses when this node or ancestor matches
          businesses: shouldShowAllChildren ? node.businesses : node.businesses
        };
      }
      
      return null;
    };
    
    return data.map(node => filterNode(node, false)).filter(Boolean) as HierarchicalNode[];
  }, [data, searchTerm]);

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Business Directory</h1>
        <p className="text-gray-600">Explore businesses by category and find the best-rated services</p>
      </div>
      
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder={config.placeholder || "Search sectors and categories..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>
      
      <div className="space-y-6">
        {filteredData.length > 0 ? (
          filteredData.map((node) => (
            <HierarchyNode
              key={node.id}
              node={node}
              level={0}
              onBusinessClick={setSelectedBusiness}
              searchTerm={searchTerm}
              ancestorMatches={false}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">No sectors or categories found matching your search criteria.</p>
          </div>
        )}
      </div>
      
      <BusinessModal
        business={selectedBusiness}
        isOpen={!!selectedBusiness}
        onClose={() => setSelectedBusiness(null)}
      />
    </div>
  );
};

export default HierarchicalSearchSystem;