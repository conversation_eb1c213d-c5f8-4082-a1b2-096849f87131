"use client";
import React from "react";
import { Label } from "@/components/ui/label";
import { BusinessHours } from "@/app/types/businessHours";
import BusinessHoursInput from "../BusinessHoursInput";

interface BusinessHoursSectionProps {
  businessHours: BusinessHours;
  onChange: (hours: BusinessHours) => void;
}

export default function BusinessHoursSection({
  businessHours,
  onChange,
}: BusinessHoursSectionProps) {
  return (
    <div className="bg-gradient-to-r from-orange-50 to-yellow-50 p-6 rounded-xl border border-orange-100">
      <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
        <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
        Business Hours
      </h2>
      
      <div className="space-y-4">
        <BusinessHoursInput 
          value={businessHours} 
          onChange={onChange}
        />
      </div>
    </div>
  );
}