"use client";
import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { iProduct } from "@/app/util/Interfaces";

interface BasicInformationSectionProps {
  product: iProduct;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export default function BasicInformationSection({
  product,
  onChange,
}: BasicInformationSectionProps) {
  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
      <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
        <div className="w-2 h-2 bg-myTheme-primary rounded-full mr-3"></div>
        Basic Information
      </h2>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="name" className="text-myTheme-primary font-medium">
            Business Name *
          </Label>
          <Input
            id="name"
            name="name"
            value={product.name}
            onChange={onChange}
            required
            className="mt-2 border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20"
            placeholder="Enter your business name"
          />
        </div>

        <div>
          <Label htmlFor="description" className="text-myTheme-primary font-medium">
            Description *
          </Label>
          <Textarea
            id="description"
            name="description"
            value={product.description}
            onChange={onChange}
            required
            className="mt-2 border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20 min-h-[120px]"
            placeholder="Describe your business, products, or services..."
          />
        </div>
      </div>
    </div>
  );
}