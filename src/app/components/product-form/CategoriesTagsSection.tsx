"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, X } from "lucide-react";
import { iProduct } from "@/app/util/Interfaces";
import SmartTags from "@/app/components/SmartTags";

interface CategoriesTagsSectionProps {
  product: iProduct;
  onArrayInput: (field: keyof iProduct, value: string) => void;
  onRemoveArrayItem: (field: keyof iProduct, index: number) => void;
}

export default function CategoriesTagsSection({
  product,
  onArrayInput,
  onRemoveArrayItem,
}: CategoriesTagsSectionProps) {
  const handleManualTagAdd = () => {
    const input = document.querySelector(
      'input[placeholder="Enter a category"]'
    ) as HTMLInputElement;
    const value = input?.value?.trim();
    if (value) {
      onArrayInput("tags", value);
      input.value = "";
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      const value = e.currentTarget.value.trim();
      if (value) {
        onArrayInput("tags", value);
        e.currentTarget.value = "";
      }
    }
  };

  return (
    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 sm:p-6 rounded-xl border border-purple-100">
      <h2 className="text-lg sm:text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
        <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
        Categories & Tags
      </h2>
      
      <div className="space-y-4 sm:space-y-6">
        {/* User's Current Tags - Now at the top */}
        <div className="bg-white p-3 sm:p-4 rounded-lg border border-gray-200">
          <Label className="text-myTheme-primary font-medium flex items-center text-sm sm:text-base">
            <div className="w-1.5 h-1.5 bg-myTheme-primary rounded-full mr-2"></div>
            Your Categories
          </Label>
          <p className="text-xs sm:text-sm text-gray-600 mb-3 mt-1">
            {product.tags?.length > 0 ? `${product.tags.length} categories added` : "No categories added yet"}
          </p>
          
          {/* Display current tags */}
          {product.tags?.length > 0 && (
            <div className="mb-3 flex flex-wrap gap-1.5 sm:gap-2">
              {product.tags.map((tag, index) => (
                <div
                  key={index}
                  className="flex items-center bg-gradient-to-r from-myTheme-primary to-myTheme-secondary text-white rounded-full px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm font-medium shadow-sm"
                >
                  <span>{tag}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2 hover:bg-white/20 text-white p-0"
                    onClick={() => onRemoveArrayItem("tags", index)}
                  >
                    <X className="h-2 w-2 sm:h-3 sm:w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
          
          {/* Manual input */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
            <Input
              placeholder="Enter a category"
              onKeyPress={handleKeyPress}
              className="border-gray-200 focus:border-purple-400 focus:ring-purple-400/20 text-sm"
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleManualTagAdd}
              className="border-purple-200 hover:bg-purple-50 whitespace-nowrap"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </div>

        {/* AI Suggestions - Now at the bottom with clearer messaging */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-3 sm:p-4 rounded-lg border border-blue-100">
          <Label className="text-blue-700 font-medium flex items-center text-sm sm:text-base">
            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
            AI Category Suggestions
          </Label>
          <p className="text-xs sm:text-sm text-blue-600 mb-3 mt-1">
            Click any suggestion below to add it to your categories
          </p>
          <div className="min-h-[60px]">
            <SmartTags
              description={product.description}
              handleArrayInput={onArrayInput}
              handleRemoveArrayItem={onRemoveArrayItem}
              field="tags"
            />
          </div>
        </div>
      </div>
    </div>
  );
}