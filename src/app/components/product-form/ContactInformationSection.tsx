"use client";
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { iProduct } from "@/app/util/Interfaces";

interface ContactInformationSectionProps {
  product: iProduct;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function ContactInformationSection({
  product,
  onChange,
}: ContactInformationSectionProps) {
  return (
    <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-6 rounded-xl border border-teal-100">
      <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
        <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
        Contact Information
      </h2>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="telephone" className="text-myTheme-primary font-medium">
            Phone Number
          </Label>
          <Input
            id="telephone"
            name="telephone"
            type="tel"
            value={product.telephone || ""}
            onChange={onChange}
            placeholder="+****************"
            className="mt-2 border-gray-200 focus:border-teal-400 focus:ring-teal-400/20"
          />
        </div>

        <div>
          <Label htmlFor="email" className="text-myTheme-primary font-medium">
            Email Address
          </Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={product.email || ""}
            onChange={onChange}
            placeholder="<EMAIL>"
            className="mt-2 border-gray-200 focus:border-teal-400 focus:ring-teal-400/20"
          />
        </div>
      </div>
    </div>
  );
}