"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, X, Trash2, Plus } from "lucide-react";
import { iProduct } from "@/app/util/Interfaces";
import ImageUpload from "../ImageUpload";

interface MediaLinksSectionProps {
  product: iProduct;
  onImageUploaded: (imageUrl: string) => void;
  onOpenImageModal: () => void;
  onArrayInput: (field: keyof iProduct, value: string) => void;
  onRemoveArrayItem: (field: keyof iProduct, index: number) => void;
  videoInputValue: string;
  setVideoInputValue: (value: string) => void;
  linkInputValue: string;
  setLinkInputValue: (value: string) => void;
  websiteInputValue: string;
  setWebsiteInputValue: (value: string) => void;
  showEditInfo?: boolean;
}

export default function MediaLinksSection({
  product,
  onImageUploaded,
  onOpenImageModal,
  onArrayInput,
  onRemoveArrayItem,
  videoInputValue,
  setVideoInputValue,
  linkInputValue,
  setLinkInputValue,
  websiteInputValue,
  setWebsiteInputValue,
  showEditInfo = false,
}: MediaLinksSectionProps) {
  const handleKeyPress = (
    e: React.KeyboardEvent<HTMLInputElement>,
    field: keyof iProduct,
    value: string,
    setValue: (value: string) => void
  ) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (value.trim()) {
        onArrayInput(field, value.trim());
        setValue("");
      }
    }
  };

  const handleAddClick = (
    field: keyof iProduct,
    value: string,
    setValue: (value: string) => void
  ) => {
    if (value.trim()) {
      onArrayInput(field, value.trim());
      setValue("");
    }
  };

  return (
    <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
      <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
        <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
        Media & Links
      </h2>
      
      <div className="space-y-6">
        {showEditInfo && (
          <div className="text-sm text-gray-500 mb-4 p-3 bg-gray-50 rounded-lg">
            <p className="font-medium mb-1">Image Link Sources:</p>
          </div>
        )}
        
        <ImageUpload
          initialImage={product.display_image}
          onImageUploaded={onImageUploaded}
          label="Display Image / Logo"
        />

        <div>
          <Label className="text-myTheme-primary font-medium">Product Images</Label>
          <div className="mt-2">
            <Button
              type="button"
              variant="outline"
              onClick={onOpenImageModal}
              className="w-full border-green-200 hover:bg-green-50 text-green-700"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Product Images ({product.images?.length || 0})
            </Button>
            
            {product.images && product.images.length > 0 && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {product.images.map((image, index) => (
                  <div key={index} className="relative group">
                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                      <img
                        src={image}
                        alt={`Product image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      onClick={() => onRemoveArrayItem("images", index)}
                      className="absolute -top-2 -right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div>
          <Label className="text-myTheme-primary font-medium">Video Links</Label>
          <div className="flex items-center space-x-2 mt-2">
            <Input
              placeholder="Video URL (YouTube, Vimeo, etc.)"
              value={videoInputValue}
              onChange={(e) => setVideoInputValue(e.target.value)}
              onKeyPress={(e) => handleKeyPress(e, "videos", videoInputValue, setVideoInputValue)}
              className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
            />
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => handleAddClick("videos", videoInputValue, setVideoInputValue)}
              className="border-green-200 hover:bg-green-50"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-2 space-y-2">
            {product.videos?.map((video, index) => (
              <div key={index} className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100">
                <Input value={video} readOnly className="bg-gray-50" />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => onRemoveArrayItem("videos", index)}
                  className="border-red-200 hover:bg-red-50 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>

        <div>
          <Label className="text-myTheme-primary font-medium">Additional Links</Label>
          <div className="flex items-center space-x-2 mt-2">
            <Input
              placeholder="Link URL"
              value={linkInputValue}
              onChange={(e) => setLinkInputValue(e.target.value)}
              onKeyPress={(e) => handleKeyPress(e, "links", linkInputValue, setLinkInputValue)}
              className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
            />
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => handleAddClick("links", linkInputValue, setLinkInputValue)}
              className="border-green-200 hover:bg-green-50"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-2 space-y-2">
            {product.links?.map((link, index) => (
              <div key={index} className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100">
                <Input value={link} readOnly className="bg-gray-50" />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => onRemoveArrayItem("links", index)}
                  className="border-red-200 hover:bg-red-50 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>

        <div>
          <Label className="text-myTheme-primary font-medium">Website URLs</Label>
          <div className="flex items-center space-x-2 mt-2">
            <Input
              placeholder="Website URL (https://...)"
              value={websiteInputValue}
              onChange={(e) => setWebsiteInputValue(e.target.value)}
              onKeyPress={(e) => handleKeyPress(e, "website", websiteInputValue, setWebsiteInputValue)}
              className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
            />
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => handleAddClick("website", websiteInputValue, setWebsiteInputValue)}
              className="border-green-200 hover:bg-green-50"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-2 space-y-2">
            {product.website?.map((site, index) => (
              <div key={index} className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100">
                <Input value={site} readOnly className="bg-gray-50" />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => onRemoveArrayItem("website", index)}
                  className="border-red-200 hover:bg-red-50 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}