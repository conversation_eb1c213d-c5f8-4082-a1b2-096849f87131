"use client";
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { iProduct } from "@/app/util/Interfaces";
import LocationPicker from "../LocationPicker";
import CitySelector from "../CitySelector";

interface LocationAddressSectionProps {
  product: iProduct;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onLocationSelect: (location: { lat: number; lng: number; address: string }) => void;
  onCityChange: (city: string) => void;
  isEditMode?: boolean;
}

export default function LocationAddressSection({
  product,
  onChange,
  onLocationSelect,
  onCityChange,
  isEditMode = false,
}: LocationAddressSectionProps) {
  return (
    <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-xl border border-indigo-100">
      <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
        <div className="w-2 h-2 bg-indigo-500 rounded-full mr-3"></div>
        Location & Address
      </h2>
      
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Street Address */}
          <div>
            <Label htmlFor="street-address" className="text-myTheme-primary font-medium">
              Street Address *
            </Label>
            <Input
              id="street-address"
              name="streetAddress"
              value={product.streetAddress || ""}
              onChange={onChange}
              placeholder="123 Main Street, Apt 4B"
              className="mt-2 border-gray-200 focus:border-indigo-400 focus:ring-indigo-400/20"
            />
            <p className="text-sm text-gray-600 mt-1">
              Street number, name, and unit/apartment if applicable
            </p>
          </div>

          {/* City/Area Selector */}
          <CitySelector
            value={product.city}
            onChange={onCityChange}
            label="City/Area"
            placeholder="Select city or area"
          />
        </div>

        {/* Legacy Address Field (for backward compatibility in edit mode) */}
        {isEditMode && product.address && !product.streetAddress && !product.city && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <Label className="text-yellow-800 font-medium">
              Legacy Address
            </Label>
            <p className="text-sm text-yellow-700 mt-1">
              This product has an old-style address. You can split it into street and city above.
            </p>
            <p className="text-sm text-yellow-600 mt-1 font-mono">
              {product.address}
            </p>
          </div>
        )}

        <p className="text-sm text-gray-600">
          You can enter address details above and/or use the map below to select a location
        </p>

        <div>
          <Label className="text-myTheme-primary font-medium">
            Map Location (Optional)
          </Label>
          <p className="text-sm text-gray-600 mb-3 mt-1">
            {isEditMode 
              ? "Click on the map to update the location or use \"Use My Current Location\" button"
              : "Click on the map to select a location or use the \"Use My Current Location\" button"
            }
          </p>
          <div className="bg-white p-3 rounded-lg border border-gray-200">
            <LocationPicker onLocationSelect={onLocationSelect} />
          </div>
        </div>

        {product.address && (
          <div className="bg-white p-3 rounded-lg border border-gray-200">
            <p className="text-sm text-gray-600 font-medium">
              {isEditMode ? "Current Address:" : "Selected Address:"}
            </p>
            <p className="text-sm text-myTheme-primary mt-1">{product.address}</p>
          </div>
        )}
      </div>
    </div>
  );
}