import React, { useState, useRef, useEffect, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { avatarTrigger<PERSON><PERSON> } from "@/app/store/store";
import { useAtom } from "jotai";
import { Edit } from "lucide-react";
import { iUser } from "../util/Interfaces";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { uploadProfilePicToCloudinary } from "../util/uploadImageToCloudinary";
import { useImageResizer } from "../util/useImageResizer";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import ProfileHeader from "./user-info/ProfileHeader";
import EditProfileForm from "./user-info/EditProfileForm";
import UserContentTabs from "./user-info/UserContentTabs";

interface UserInfoProps {
  user: iUser;
  onUpdateUser: (updatedUser: Partial<iUser>) => void;
}

// Define gradient combinations
const profileGradients = [
  "from-blue-500 to-purple-600",
  "from-pink-500 to-orange-400",
  "from-green-400 to-blue-500",
  "from-purple-500 to-pink-500",
  "from-yellow-400 to-orange-500",
  "from-indigo-500 to-purple-600",
  "from-teal-400 to-blue-500",
  "from-red-500 to-pink-500",
  "from-emerald-500 to-teal-500",
  "from-violet-500 to-purple-600",
  "from-cyan-500 to-blue-500",
  "from-rose-500 to-pink-500",
  "from-amber-500 to-orange-500",
  "from-sky-500 to-indigo-500",
  "from-fuchsia-500 to-purple-500",
];

export default function UserInfo({ user, onUpdateUser }: UserInfoProps) {
  const { userId: clerkUserId } = useAuth();
  let {
    firstName,
    lastName,
    userName,
    avatar,
    reviews,
    comments,
    likedReviews,
    bio,
    commentVotes,
  } = user;
  
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<"date" | "rating">("date");
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [editedFirstName, setEditedFirstName] = useState(firstName);
  const [editedLastName, setEditedLastName] = useState(lastName);
  const [editedUserName, setEditedUserName] = useState(userName);
  const [editedBio, setEditedBio] = useState(bio || "");
  const [isAvatarUploading, setIsAvatarUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [currentAvatar, setCurrentAvatar] = useState(avatar || "");
  const [, setAvatarTrigger] = useAtom(avatarTriggerAtom);
  const [selectedGradient, setSelectedGradient] = useState("");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  dayjs.extend(relativeTime);

  // Input sanitization to prevent XSS
  const sanitizeInput = (input: string): string => {
    return input.replace(/<[^>]*>/g, "");
  };

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!editedFirstName.trim()) {
      errors.firstName = "First name is required";
    }

    if (!editedLastName.trim()) {
      errors.lastName = "Last name is required";
    }

    if (!editedUserName.trim()) {
      errors.userName = "Username is required";
    } else if (editedUserName.length < 3) {
      errors.userName = "Username must be at least 3 characters";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAvatarClick = () => {
    if (isEditable) {
      fileInputRef.current?.click();
    }
  };

  const { processImage, isResizing } = useImageResizer();

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (!isEditable) return;
    const file = event.target.files?.[0];
    if (file) {
      setIsAvatarUploading(true);
      const tempUrl = URL.createObjectURL(file);
      setCurrentAvatar(tempUrl);

      try {
        // Process the file directly with our enhanced hook
        const result = await processImage(file, {
          maxDimension: 400, // Appropriate size for profile pictures
          quality: 0.8,
        });

        // Extract the dataUrl from the result
        let dataUrl: string;
        if ("dataUrl" in result) {
          dataUrl = result.dataUrl ?? "";
        } else {
          // This shouldn't happen now that we always return dataUrl
          console.warn("Unexpected result type when processing profile image");
          throw new Error("Failed to process image");
        }

        try {
          const res = await uploadProfilePicToCloudinary(dataUrl);
          const imageUrl = res.secure_url;
          setCurrentAvatar(imageUrl);
          onUpdateUser({ avatar: imageUrl });
          setAvatarTrigger(imageUrl);
          toast.success("Profile picture updated successfully");
        } catch (error) {
          console.error("Error uploading image:", error);
          toast.error("Failed to upload image. Please try again.");
        } finally {
          setIsAvatarUploading(false);
          URL.revokeObjectURL(tempUrl);
        }
      } catch (error) {
        console.error("Error processing image:", error);
        toast.error("Failed to process image. Please try again.");
        setIsAvatarUploading(false);
        URL.revokeObjectURL(tempUrl);
      }
    }
  };

  const handleSaveProfile = () => {
    if (!isEditable) return;

    if (validateForm()) {
      onUpdateUser({
        firstName: sanitizeInput(editedFirstName),
        lastName: sanitizeInput(editedLastName),
        userName: sanitizeInput(editedUserName),
        bio: sanitizeInput(editedBio),
      });
      setIsEditingProfile(false);
    } else {
      toast.error("Please fix the errors in the form");
    }
  };

  const isEditable = user.clerkUserId === clerkUserId;

  // Select a random gradient on component mount
  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * profileGradients.length);
    setSelectedGradient(profileGradients[randomIndex]);
  }, []);

  return (
    <Card className="w-full border-0 shadow-none overflow-hidden">
      <ProfileHeader
          user={user}
          onUpdateUser={onUpdateUser}
          isEditingProfile={isEditingProfile}
          setIsEditingProfile={setIsEditingProfile}
          editedFirstName={editedFirstName}
          editedLastName={editedLastName}
          editedUserName={editedUserName}
          editedBio={editedBio}
          setEditedFirstName={setEditedFirstName}
          setEditedLastName={setEditedLastName}
          setEditedUserName={setEditedUserName}
          setEditedBio={setEditedBio}
          currentAvatar={currentAvatar}
          setCurrentAvatar={setCurrentAvatar}
          selectedGradient={selectedGradient}
          handleAvatarClick={handleAvatarClick}
          handleFileChange={handleFileChange}
          fileInputRef={fileInputRef}
          isAvatarUploading={isAvatarUploading}
          isResizing={isResizing}
        />
      <CardContent className="pt-3 sm:pt-5 md:pt-7 px-4 sm:px-6 md:px-8">
        <div className="px-4 sm:px-6 mb-6">
          {isEditingProfile && isEditable ? (
            <EditProfileForm
          editedFirstName={editedFirstName}
          editedLastName={editedLastName}
          editedUserName={editedUserName}
          editedBio={editedBio}
          setEditedFirstName={setEditedFirstName}
          setEditedLastName={setEditedLastName}
          setEditedUserName={setEditedUserName}
          setEditedBio={setEditedBio}
          onUpdateUser={onUpdateUser}
          setIsEditingProfile={setIsEditingProfile}
          handleSaveProfile={handleSaveProfile}
          sanitizeInput={sanitizeInput}
          validateForm={validateForm}
        />
          ) : (
            <div className="text-center animate-fadeIn">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3 tracking-tight">
                {`${editedFirstName} ${editedLastName}`}
              </h2>
              <p className="text-lg text-gray-600 mb-4 font-medium">@{editedUserName}</p>
              
              {editedBio ? (
                <div className="relative max-w-2xl mx-auto mb-6 px-4 sm:px-6">
                  <div className="absolute -left-2 top-0 text-gray-300 opacity-30">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-8 h-8 sm:w-10 sm:h-10">
                      <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                      <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                    </svg>
                  </div>
                  <p className="text-gray-700 leading-relaxed text-base sm:text-lg px-6 py-4 bg-gray-50/80 rounded-xl shadow-sm border border-gray-100">
                    {editedBio}
                  </p>
                </div>
              ) : null}

              {isEditable && (
                <Button
                  variant="outline"
                  onClick={() => setIsEditingProfile(true)}
                  className="mt-2 hover:bg-primary/10 border-gray-300 hover:border-primary/30 transition-all duration-300 shadow-sm hover:shadow px-5 py-2 h-auto"
                  aria-label="Edit profile"
                >
                  <Edit className="h-4 w-4 mr-2" aria-hidden="true" />
                  Edit Profile
                </Button>
              )}
            </div>
          )}
        </div>

        <UserContentTabs
          user={user}
          onUpdateUser={onUpdateUser}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedTag={selectedTag}
          setSelectedTag={setSelectedTag}
          sortBy={sortBy}
          setSortBy={setSortBy}
        />
      </CardContent>
    </Card>
  );
}
