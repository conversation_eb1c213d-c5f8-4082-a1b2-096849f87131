"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { invalidateProductCaches } from "../util/cacheInvalidation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { iProduct } from "@/app/util/Interfaces";
import ProductImageUploadModal from "./ProductImageUploadModal";
import { updateProduct } from "../util/api";
import { createDefaultBusinessHours } from "../types/businessHours";
import BasicInformationSection from "./product-form/BasicInformationSection";
import MediaLinksSection from "./product-form/MediaLinksSection";
import CategoriesTagsSection from "./product-form/CategoriesTagsSection";
import BusinessHoursSection from "./product-form/BusinessHoursSection";
import ContactInformationSection from "./product-form/ContactInformationSection";
import LocationAddressSection from "./product-form/LocationAddressSection";

interface EditProductFormProps {
  initialProduct: iProduct;
  onSuccess?: () => void;
  onSubmit?: (updatedProduct: iProduct) => Promise<void>;
  onCancel?: () => void;
}

export default function EditProductForm({
  initialProduct,
  onSuccess,
  onSubmit,
  onCancel,
}: EditProductFormProps) {
  const [product, setProduct] = useState<iProduct>({
    ...initialProduct,
    businessHours: initialProduct.businessHours || createDefaultBusinessHours()
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [imageInputValue, setImageInputValue] = useState("");
  const [videoInputValue, setVideoInputValue] = useState("");
  const [linkInputValue, setLinkInputValue] = useState("");
  const [websiteInputValue, setWebsiteInputValue] = useState("");
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);

  const router = useRouter();
  const queryClient = useQueryClient();

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setProduct((prev) => ({ ...prev, [name]: value }));
  };

  const handleArrayInput = (field: keyof iProduct, value: string) => {
    setProduct((prev) => ({
      ...prev,
      [field]: [...((prev[field] as string[]) || []), value],
    }));
  };

  const handleRemoveArrayItem = (field: keyof iProduct, index: number) => {
    setProduct((prev) => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index),
    }));
  };

  const handleImagesUploaded = (imageUrls: string[]) => {
    setProduct((prev) => ({
      ...prev,
      images: imageUrls, // ProductImageUploadModal already handles merging with existing images
    }));
  };

  const mutation = useMutation({
    mutationFn: (product: iProduct) => updateProduct(product),
    onSuccess: async (data) => {
      setIsLoading(false);
      setError(null);

      try {
        // Comprehensive cache invalidation (Redis + React Query + Next.js)
        const invalidationResult = await invalidateProductCaches({
          invalidateServer: true,
          invalidateClient: false, // We'll handle React Query manually for better error control
          queryKeys: [["products"]],
          serverActions: ["invalidate-all-products", "invalidate-search"]
        });

        // Check if server invalidation failed
        if (!invalidationResult.serverInvalidation && invalidationResult.errors.length > 0) {
          console.error("Server cache invalidation failed:", invalidationResult.errors);
          throw new Error("Failed to invalidate server cache");
        }

        // Only invalidate React Query if server invalidation succeeded
        await queryClient.invalidateQueries({ queryKey: ["products"] });

        if (onSuccess) {
          onSuccess();
        } else {
          router.push(
            `/mybusinesses/productsuccess?product=${encodeURIComponent(JSON.stringify(data))}`,
          );
        }
      } catch (error) {
        console.error("Cache invalidation failed:", error);
        setError("Product updated but cache refresh failed. You may need to refresh the page to see changes.");

        // Still call success callback but with warning
        if (onSuccess) {
          onSuccess();
        } else {
          router.push(
            `/mybusinesses/productsuccess?product=${encodeURIComponent(JSON.stringify(data))}&cacheWarning=true`,
          );
        }
      }
    },
    onError: (error: Error) => {
      setIsLoading(false);
      setError(error.message);
    },
  });

  const handleImageUploaded = (imageUrl: string) => {
    setProduct((prev) => ({ ...prev, display_image: imageUrl }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (!product.id) {
      setError("Product ID is missing");
      setIsLoading(false);
      return;
    }

    // Check if at least one day is open in either legacy or flexible business hours
    const hasLegacyOpeningDays = product.openingDays && product.openingDays.length > 0;
    const hasFlexibleOpeningDays = product.businessHours?.schedules.some(schedule => !schedule.isClosed);

    if (!hasLegacyOpeningDays && !hasFlexibleOpeningDays) {
      setError("Please select at least one opening day");
      setIsLoading(false);
      return;
    }

    try {
      if (onSubmit) {
        // Use the provided onSubmit prop if available
        await onSubmit(product);
      } else {
        // Otherwise use the default mutation
        await mutation.mutateAsync(product);
      }
    } catch (error) {
      console.error("Error updating product:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLocationSelect = (location: {
    lat: number;
    lng: number;
    address: string;
  }) => {
    setProduct({
      ...product,
      latitude: location.lat,
      longitude: location.lng,
      address: location.address,
    });
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="max-w-4xl w-full mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden"
    >
      {/* Form Header */}
      <div className="bg-gradient-to-r from-myTheme-primary to-myTheme-secondary p-6 sm:p-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">
          Edit Product
        </h1>
        <p className="text-white/80">Update your business details below</p>
      </div>

      {/* Form Content */}
      <div className="p-6 sm:p-8">
        {error && (
          <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-xl border border-red-200">
            {error}
          </div>
        )}

        <div className="space-y-8">
          <div className="space-y-6">
            <BasicInformationSection
              product={product}
              onChange={handleChange}
            />

            <MediaLinksSection
              product={product}
              onImageUploaded={handleImageUploaded}
              onOpenImageModal={() => setIsImageModalOpen(true)}
              onArrayInput={handleArrayInput}
              onRemoveArrayItem={handleRemoveArrayItem}
              videoInputValue={videoInputValue}
              setVideoInputValue={setVideoInputValue}
              linkInputValue={linkInputValue}
              setLinkInputValue={setLinkInputValue}
              websiteInputValue={websiteInputValue}
              setWebsiteInputValue={setWebsiteInputValue}
              showEditInfo={true}
            />

            <CategoriesTagsSection
              product={product}
              onArrayInput={handleArrayInput}
              onRemoveArrayItem={handleRemoveArrayItem}
            />

            <BusinessHoursSection
              businessHours={product.businessHours || createDefaultBusinessHours()}
              onChange={(businessHours) => {
                setProduct({
                  ...product,
                  businessHours,
                  // Update legacy fields for backward compatibility
                  openingDays: businessHours.schedules
                    .filter(schedule => !schedule.isClosed)
                    .map(schedule => schedule.day),
                });
              }}
            />

            <ContactInformationSection
              product={product}
              onChange={handleChange}
            />

            <LocationAddressSection
              product={product}
              onChange={handleChange}
              onLocationSelect={handleLocationSelect}
              onCityChange={(city) => setProduct((prev) => ({ ...prev, city }))}
              isEditMode={true}
            />
          </div>

          {/* Submit Button */}
          <div className="mt-8 flex justify-center space-x-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="max-w-md py-3 px-8 rounded-xl"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full max-w-md bg-gradient-to-r from-myTheme-primary to-myTheme-secondary hover:from-myTheme-primary/90 hover:to-myTheme-secondary/90 text-white font-semibold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            >
              {isLoading ? "Updating Product..." : "Update Product"}
            </Button>
          </div>
        </div>
      </div>

      {/* Product Image Upload Modal */}
      <ProductImageUploadModal
        isOpen={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        onImagesUploaded={handleImagesUploaded}
        existingImages={product.images || []}
        maxImages={10}
      />
    </form>
  );
}
