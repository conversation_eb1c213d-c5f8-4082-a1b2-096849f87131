"use client";
import React, { useState, useRef, useEffect, useCallback } from "react";
import { Upload } from "lucide-react";
import Image from "next/image";
import { Label } from "@/components/ui/label";
import { uploadImageToCloudinary } from "../util/uploadImageToCloudinary";
import { useImageResizer } from "../util/useImageResizer";

interface ImageUploadProps {
  initialImage?: string;
  onImageUploaded: (imageUrl: string) => void;
  label?: string;
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  maxFileSize?: number;
  allowedFileTypes?: string[];
  className?: string;
}

export default function ImageUpload({
  initialImage = "",
  onImageUploaded,
  label = "Image",
  maxWidth = 800,
  maxHeight = 800,
  quality = 0.7,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  allowedFileTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
  className = "",
}: ImageUploadProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(initialImage);
  const [previewBlobUrl, setPreviewBlobUrl] = useState<string | null>(null);
  const [isImageUploading, setIsImageUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { processImage, isResizing } = useImageResizer();

  // Update imagePreview when initialImage changes
  useEffect(() => {
    if (initialImage) {
      setImagePreview(initialImage);
    }
  }, [initialImage]);

  // Cleanup blob URL to prevent memory leaks
  useEffect(() => {
    return () => {
      if (previewBlobUrl) {
        URL.revokeObjectURL(previewBlobUrl);
      }
    };
  }, [previewBlobUrl]);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    // Prevent any form submission
    event.preventDefault();
    event.stopPropagation();
    
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset error state
    setUploadError(null);

    // Validate file type
    if (!allowedFileTypes.includes(file.type)) {
      setUploadError(
        `Invalid file type. Allowed types: ${allowedFileTypes.map((type) => type.split("/")[1]).join(", ")}`
      );
      return;
    }

    // Validate file size
    if (file.size > maxFileSize) {
      setUploadError(
        `File too large. Maximum size is ${maxFileSize / (1024 * 1024)}MB`
      );
      return;
    }

    // Clean up previous blob URL if exists
    if (previewBlobUrl) {
      URL.revokeObjectURL(previewBlobUrl);
    }

    // Create blob URL for efficient preview
    const blobUrl = URL.createObjectURL(file);
    setPreviewBlobUrl(blobUrl);
    setImagePreview(blobUrl);

    // Process and upload the image in the background
    try {
      const result = await processImage(file, {
        maxWidth,
        maxHeight,
        quality,
      });

      if (result.dataUrl) {
        await triggerUploadImage(result.dataUrl);
      }
    } catch (error) {
      console.error("Error processing image:", error);
      setUploadError("Failed to process image. Please try again.");
    }
  };

  const triggerUploadImage = useCallback(
    async (dataUrl: string) => {
      setIsImageUploading(true);
      setUploadProgress(0);
      setUploadError(null);

      try {
        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploadProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + 10;
          });
        }, 300);

        let res = await uploadImageToCloudinary(dataUrl);
        clearInterval(progressInterval);
        setUploadProgress(100);
        onImageUploaded(res.secure_url);
      } catch (error) {
        console.error("Error uploading image:", error);
        setUploadError("Failed to upload image. Please try again.");
      } finally {
        setIsImageUploading(false);
      }
    },
    [onImageUploaded]
  );

  return (
    <div className={className}>
      <Label htmlFor="display_image">{label}</Label>
      <div className="mt-2 flex items-center space-x-4">
        <div
          className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer relative"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            fileInputRef.current?.click();
          }}
        >
          {imagePreview ? (
            <>
              <Image
                src={imagePreview}
                alt="Display"
                className="w-full h-full object-cover rounded-lg"
                width={128}
                height={128}
              />
              {(isImageUploading || isResizing) && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center rounded-lg">
                  <span className="text-white mb-2">
                    {isResizing ? "Processing..." : "Uploading..."}
                  </span>
                  {isImageUploading && (
                    <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-500 transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              )}
            </>
          ) : (
            <Upload className="w-8 h-8 text-gray-400" />
          )}
        </div>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept={allowedFileTypes.join(",")}
          onChange={handleImageUpload}
        />
      </div>
      {uploadError && (
        <p className="mt-2 text-sm text-red-500">{uploadError}</p>
      )}
      <p className="mt-2 text-xs text-gray-500">
        Maximum file size: {maxFileSize / (1024 * 1024)}MB. Allowed formats:{" "}
        {allowedFileTypes
          .map((type) => type.split("/")[1].toUpperCase())
          .join(", ")}
        .
      </p>
    </div>
  );
}
