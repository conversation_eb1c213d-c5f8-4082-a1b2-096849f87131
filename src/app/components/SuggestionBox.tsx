"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { SuggestionBox } from "@/app/util/Interfaces";

export default function SuggestionBoxComponent() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [userIP, setUserIP] = useState<string>("");
    const [formData, setFormData] = useState({
        description: "",
        user_email: "",
        user_name: "",
    });

    // Get user's IP address
    useEffect(() => {
        const getUserIP = async () => {
            try {
                // Try multiple IP services for reliability
                const ipServices = [
                    'https://api.ipify.org?format=json',
                    'https://ipapi.co/json/',
                    'https://api.ip.sb/jsonip'
                ];

                for (const service of ipServices) {
                    try {
                        const response = await fetch(service);
                        const data = await response.json();
                        const ip = data.ip || data.query;
                        if (ip) {
                            setUserIP(ip);
                            return;
                        }
                    } catch (error) {
                        console.warn(`Failed to get IP from ${service}:`, error);
                        continue;
                    }
                }
                
                // Fallback: try to get from headers (if available)
                setUserIP("Unable to detect");
            } catch (error) {
                console.error("Error getting IP address:", error);
                setUserIP("Unable to detect");
            }
        };

        getUserIP();
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            const suggestionData = {
                ...formData,
                ip_address: userIP,
            };

            const response = await fetch("/api/create/suggestions", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(suggestionData),
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.error || "Failed to submit suggestion");
            }
            
            toast.success("Suggestion submitted successfully! Thank you for your feedback.");
            
            // Reset form
            setFormData({
                description: "",
                user_email: "",
                user_name: "",
            });
        } catch (error) {
            console.error("Error submitting suggestion:", error);
            toast.error(error instanceof Error ? error.message : "Failed to submit suggestion");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    return (
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700">
                <h2 className="text-xl font-semibold text-white">Submit a Suggestion</h2>
                <p className="mt-1 text-sm text-indigo-100">
                    Help us improve by sharing your ideas and suggestions. We value your feedback!
                </p>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-6 p-6">
                <div className="space-y-4">
                    <div>
                        <label
                            htmlFor="description"
                            className="block text-sm font-medium text-gray-700"
                        >
                            Your Suggestion *
                        </label>
                        <div className="mt-1">
                            <textarea
                                id="description"
                                name="description"
                                value={formData.description}
                                onChange={handleChange}
                                required
                                rows={6}
                                className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                placeholder="Please describe your suggestion in detail:
1. What feature or improvement would you like to see?
2. How would this benefit users?
3. Any specific implementation ideas?"
                            />
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                            The more details you provide, the better we can understand and implement your suggestion.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label
                                htmlFor="user_name"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Your Name (Optional)
                            </label>
                            <div className="mt-1">
                                <input
                                    type="text"
                                    id="user_name"
                                    name="user_name"
                                    value={formData.user_name}
                                    onChange={handleChange}
                                    className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                    placeholder="Your name"
                                />
                            </div>
                        </div>

                        <div>
                            <label
                                htmlFor="user_email"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Email (Optional)
                            </label>
                            <div className="mt-1">
                                <input
                                    type="email"
                                    id="user_email"
                                    name="user_email"
                                    value={formData.user_email}
                                    onChange={handleChange}
                                    className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors duration-200 px-4 py-2.5"
                                    placeholder="<EMAIL>"
                                />
                            </div>
                            <p className="mt-1 text-sm text-gray-500">
                                We'll only use this to follow up on your suggestion if needed.
                            </p>
                        </div>
                    </div>
                </div>

                <div className="flex justify-end space-x-4">
                    <button
                        type="button"
                        onClick={() => {
                            setFormData({
                                description: "",
                                user_email: "",
                                user_name: "",
                            });
                        }}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                    >
                        Clear Form
                    </button>
                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                        {isSubmitting ? (
                            <>
                                <svg className="w-4 h-4 mr-2 animate-spin" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                </svg>
                                Submitting...
                            </>
                        ) : (
                            'Submit Suggestion'
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
}