"use client";
import { iReview } from "../util/Interfaces";
import ReviewBox from "./ReviewBox";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "../util/serverFunctions";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { TopReviewsSkeleton } from "./skeletons";

const EnhancedTopReviews = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [reviewsPerPage, setReviewsPerPage] = useState(12);

  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ["latestReviews"],
    queryFn: async () => {
      const response = await getLatestReviews("latest");
      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch reviews");
      }
      return response.data;
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const reviews = data || [];

  if (isError) {
    return (
      <div className="text-center p-8 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-lg font-medium text-red-800">
          Unable to load reviews
        </p>
        <p className="text-red-600 mb-4">
          {error instanceof Error ? error.message : "Unknown error"}
        </p>
        <Button onClick={() => refetch()} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (isLoading) return <TopReviewsSkeleton />;

  return (
    <div className="w-full space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900">
          Latest Reviews
        </h2>
        <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
          Discover what others are saying about businesses in Guyana
        </p>
      </div>

      {/* Reviews Grid */}
      {reviews.length === 0 ? (
        <div className="text-center py-16 bg-gray-50 rounded-2xl">
          <div className="text-6xl mb-4">✍️</div>
          <p className="text-lg font-semibold text-gray-800 mb-2">
            No reviews yet
          </p>
          <p className="text-gray-600 mb-4">
            Be the first to share your experience!
          </p>
        </div>
      ) : (
        <>
          <div className="w-full columns-1 md:columns-2 xl:columns-3 gap-4 md:gap-6 lg:gap-8 space-y-4 md:space-y-6 lg:space-y-8">
              {reviews
                .slice(
                  (currentPage - 1) * reviewsPerPage,
                  currentPage * reviewsPerPage,
                )
                .map((review: iReview, index: number) => (
                  <div
                    key={review.id}
                    className="break-inside-avoid mb-4 md:mb-6 lg:mb-8"
                  >
                    <ReviewBox review={review} />
                  </div>
                ))}
          </div>

          {/* Pagination Controls */}
          {Math.ceil(reviews.length / reviewsPerPage) > 1 && (
            <div className="flex justify-center items-center gap-2 mt-8">
              <Button
                variant="outline"
                onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-4 py-2"
              >
                Previous
              </Button>

              <div className="flex gap-1">
                {Array.from({
                  length: Math.ceil(reviews.length / reviewsPerPage),
                })
                  .map((_, index) => index + 1)
                  .filter((page) => {
                    const totalPages = Math.ceil(reviews.length / reviewsPerPage);
                    if (totalPages <= 7) return true;
                    if (page <= 3) return true;
                    if (page >= totalPages - 2) return true;
                    if (Math.abs(page - currentPage) <= 1) return true;
                    return false;
                  })
                  .map((page, index, array) => {
                    const showEllipsis = index > 0 && page - array[index - 1] > 1;
                    return (
                      <div key={page} className="flex items-center gap-1">
                        {showEllipsis && (
                          <span className="px-2 py-1 text-gray-500">...</span>
                        )}
                        <Button
                          variant={currentPage === page ? "default" : "outline"}
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-2 min-w-[40px] ${
                            currentPage === page
                              ? "bg-myTheme-primary text-white"
                              : "hover:bg-gray-100"
                          }`}
                        >
                          {page}
                        </Button>
                      </div>
                    );
                  })}
              </div>

              <Button
                variant="outline"
                onClick={() =>
                  setCurrentPage((prev) =>
                    Math.min(Math.ceil(reviews.length / reviewsPerPage), prev + 1),
                  )
                }
                disabled={currentPage === Math.ceil(reviews.length / reviewsPerPage)}
                className="px-4 py-2"
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default EnhancedTopReviews;