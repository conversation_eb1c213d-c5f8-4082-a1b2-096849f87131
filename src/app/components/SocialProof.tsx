"use client";

import Image from "next/image";
import { motion } from "framer-motion";

const partners = [
  { src: "/logos/banks-dih.svg", alt: "Banks DIH" },
  { src: "/logos/digicel.svg", alt: "Digicel" },
  { src: "/logos/gtt.svg", alt: "GTT" },
  { src: "/logos/massive.svg", alt: "Massy" },
  { src: "/logos/guyana-airways.svg", alt: "Guyana Airways" },
];

const SocialProof = () => (
  <motion.section
    initial="hidden"
    whileInView="show"
    viewport={{ once: true, amount: 0.3 }}
    variants={{
      hidden: { opacity: 0, y: 20 },
      show: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
    }}
    className="py-6 bg-white border-hairline"
  >
    <div className="mx-auto max-w-7xl px-4">
      <div className="flex items-center justify-center gap-6 sm:gap-10 flex-wrap opacity-80 grayscale">
        {partners.map((p) => (
          <div key={p.alt} className="relative h-8 sm:h-10 w-auto">
            <Image
              src={p.src}
              alt={p.alt}
              height={40}
              width={120}
              className="object-contain"
            />
          </div>
        ))}
      </div>
      <motion.p
        variants={{
          hidden: { opacity: 0, y: 20 },
          show: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
        }}
        className="mt-4 text-center text-sm sm:text-base text-gray-500"
      >
        Trusted by <span className="font-semibold text-gray-700">2,000+</span> Guyanese consumers
      </motion.p>
    </div>
  </motion.section>
);

export default SocialProof;