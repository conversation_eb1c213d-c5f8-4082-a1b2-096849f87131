"use client";

import { useEffect, useState, useMemo, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { useAtom } from "jotai";
import { allProductsAtom } from "@/app/store/store";
import { iProduct } from "@/app/util/Interfaces";

interface FeedItem {
  id: string;
  name: string;
  image: string;
}

export default function LiveFeedStrip() {
  const [allProducts] = useAtom(allProductsAtom);
  const [isVisible, setIsVisible] = useState(true);
  const stripRef = useRef<HTMLDivElement>(null);
  
  // Memoize the product processing to avoid recalculating on every render
  const feedItems = useMemo(() => {
    // Get the latest 12 products
    const latestProducts = allProducts
      .filter(product => !product.isDeleted && product.isPublic !== false)
      .sort((a, b) => new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime())
      .slice(0, 12)
      .map((product: iProduct): FeedItem => ({
        id: product.id || '',
        name: product.name,
        image: product.display_image || '/placeholder.svg'
      }));

    // Fallback to mock data if no products available
    if (latestProducts.length > 0) {
      return latestProducts;
    }
    
    return [
      { id: "1", name: "Blue Mountain Coffee", image: "/placeholder.svg" },
      { id: "2", name: "Organic Coconut Oil", image: "/placeholder.svg" },
      { id: "3", name: "Guyana Pepper Sauce", image: "/placeholder.svg" },
      { id: "4", name: "Cassava Bread", image: "/placeholder.svg" },
      { id: "5", name: "Demerara Rum", image: "/placeholder.svg" },
      { id: "6", name: "Plantain Chips", image: "/placeholder.svg" },
    ];
  }, [allProducts]);
  
  // Duplicate list for seamless scrolling
  // Reduce number of items to minimize DOM elements while maintaining smooth scroll
  const scrollingItems = useMemo(() => {
    // Only duplicate the minimum number of items needed for smooth scrolling
    const minItemsNeeded = Math.ceil(window.innerWidth / 200) + 2; // 200px is approximate width of each item
    const itemsToShow = Math.min(feedItems.length, minItemsNeeded);
    return [...feedItems.slice(0, itemsToShow), ...feedItems.slice(0, itemsToShow)];
  }, [feedItems]);

  // Use Intersection Observer with reduced updates and hardware acceleration hints
  useEffect(() => {
    if (!stripRef.current) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );
    
    // Store a reference to the current strip element
    const currentStrip = stripRef.current;
    
    if (currentStrip) {
      observer.observe(currentStrip);
    }
    
    return () => {
      // Use the stored reference in the cleanup function
      if (currentStrip) {
        observer.unobserve(currentStrip);
      }
    };
  }, []);

  return (
    <div 
      ref={stripRef}
      className="w-full overflow-hidden bg-gradient-to-r from-blue-50 via-white to-blue-50 border-y border-blue-100 shadow-sm"
    >
      {/* Header */}
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm sm:text-base font-semibold text-gray-700">Latest Products</span>
          </div>
          <span className="text-xs sm:text-sm text-gray-500 hidden sm:block font-medium">
            {feedItems.length} products • Live feed
          </span>
        </div>
      </div>
      
      {/* Scrolling Feed */}
      <div 
        className={`flex items-center gap-6 py-4 whitespace-nowrap will-change-transform ${
          isVisible ? "animate-feed-scroll" : "paused"
        } hover:pause-animation`}
      >
        {scrollingItems.map((item, idx) => {
          const isMockProduct = item.id.length <= 2;
          
          const productCard = (
            <div className="flex items-center gap-3 px-4 py-2 rounded-lg bg-white/80 backdrop-blur-sm border border-gray-200/50 hover:border-blue-300 hover:shadow-md transition-all duration-300 hover:scale-105 cursor-pointer group shrink-0">
              <div className="relative w-10 h-10 rounded-lg overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shrink-0 group-hover:shadow-lg">
                <Image
                  src={item.image}
                  alt={item.name}
                  width={40}
                  height={40}
                  className="object-cover"
                  loading="lazy"
                  unoptimized
                />
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition-colors line-clamp-1">
                  {item.name}
                </span>
                <span className="text-xs text-gray-500 group-hover:text-gray-600 font-medium">
                  {isMockProduct ? "Coming soon" : "View details →"}
                </span>
              </div>
            </div>
          );

          // Wrap in Link only for real products
          if (isMockProduct) {
            return (
              <div key={`${item.id}-${idx}`} className="opacity-75">
                {productCard}
              </div>
            );
          }

          return (
            <Link key={`${item.id}-${idx}`} href={`/product/${item.id}`} prefetch={false}>
              {productCard}
            </Link>
          );
        })}
      </div>

      {/* Enhanced styled-JSX with hover pause */}
      <style jsx>{`
        @keyframes feed-scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        .animate-feed-scroll {
          animation: feed-scroll 60s linear infinite;
          transform: translateZ(0);
          backface-visibility: hidden;
          perspective: 1000;
        }
        .animate-feed-scroll:hover {
          animation-play-state: paused;
        }
        .pause-animation {
          animation-play-state: paused;
        }
        .line-clamp-1 {
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
      `}</style>
    </div>
  );
}