"use client";
import { useUser } from "@clerk/nextjs";
import { useEffect, useState } from "react";
import Link from "next/link";

/**
 * Displays a dismiss-until-fixed banner prompting a user with an
 * auto-generated username to choose a new one.
 * Uses database as source of truth but checks Clerk metadata first for performance.
 */
export default function UsernameChangePrompt() {
  const { user, isLoaded } = useUser();
  
  // First check Clerk metadata for performance
  const needsChangeFromClerk = Boolean(user?.publicMetadata?.usernameNeedsChange);
  
  // State to track the final determination after DB check
  const [needsChange, setNeedsChange] = useState(needsChangeFromClerk);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    // Only query the database if Clerk metadata suggests a username change is needed
    // This avoids unnecessary API calls for most users
    if (isLoaded && user && needsChangeFromClerk) {
      setIsChecking(true);
      
      // Verify with database (source of truth)
      fetch('/api/user/check-username-change-needed')
        .then(res => res.json())
        .then(data => {
          setNeedsChange(data.needsChange);
          
          // Log if metadata was out of sync with database
          if (data.metadataWasOutOfSync) {
            console.log('🟠 Username change metadata was out of sync with database. Using DB value:', data.needsChange);
          }
        })
        .catch(err => {
          console.error('Error checking username change status:', err);
          // Fall back to Clerk metadata if API fails
          setNeedsChange(needsChangeFromClerk);
        })
        .finally(() => {
          setIsChecking(false);
        });
    } else if (isLoaded && !needsChangeFromClerk) {
      // If Clerk says no change needed, trust it for performance
      setNeedsChange(false);
    }
  }, [isLoaded, user, needsChangeFromClerk]);

  // Debug logging
  useEffect(() => {
    if (isLoaded && user) {
      console.log('🟡 UsernameChangePrompt Debug:', {
        userId: user.id,
        username: user.username,
        publicMetadata: user.publicMetadata,
        needsChangeFromClerk,
        finalNeedsChange: needsChange,
        isChecking
      });
    }
  }, [isLoaded, user, needsChangeFromClerk, needsChange, isChecking]);

  // Don't show anything while loading or if no change needed
  if (!isLoaded || !needsChange) {
    return null;
  }

  console.log('🟡 SHOWING BANNER! DB confirms usernameNeedsChange');
  return (
    <div className="fixed inset-x-0 top-0 z-50 w-full">
      {/* Overlay with semi-transparent background */}
      <div className="bg-yellow-500 py-4 px-6 shadow-lg border-b-4 border-yellow-600">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center justify-between">
          <div className="flex items-center mb-3 md:mb-0">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-8 w-8 text-yellow-900 mr-3 animate-pulse" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
              />
            </svg>
            <h2 className="text-xl font-bold text-yellow-900">Action Required: Username Change</h2>
          </div>
          
          <div className="text-yellow-900 text-center md:text-right">
            <p className="text-lg mb-2">Your current username was restricted or auto-generated.</p>
            <Link 
              href="/change-username" 
              className="inline-block bg-yellow-900 text-white px-6 py-2 rounded-md font-bold hover:bg-yellow-800 transition-colors"
            >
              Choose New Username Now
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
