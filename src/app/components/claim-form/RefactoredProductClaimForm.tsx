"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import { iProduct } from "@/app/util/Interfaces";
import { toast } from "sonner";
import { baseUrl } from '@/app/util/serverFunctions';

// Import our new components
import AuthCheck from "./AuthCheck";
import ProductSearch from "./ProductSearch";
import SelectedProductDisplay from "./SelectedProductDisplay";
import ClaimFormFields from "./ClaimFormFields";
import SubmitButton from "./SubmitButton";
import ClaimConfirmModal from "../ClaimConfirmModal";

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_IMAGES = 5;

const ProductClaimForm = () => {
  const { isSignedIn, userId } = useAuth();
  const router = useRouter();

  // Search state
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<iProduct[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Product selection state
  const [selectedProduct, setSelectedProduct] = useState<iProduct | null>(null);

  // Form state
  const [contactInfo, setContactInfo] = useState({
    name: "",
    email: "",
    phone: "",
  });
  const [additionalInfo, setAdditionalInfo] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // Claim check state
  const [isCheckingClaim, setIsCheckingClaim] = useState(false);
  const [userHasExistingClaim, setUserHasExistingClaim] = useState(false);
  const [existingClaimDetails, setExistingClaimDetails] = useState<{
    id: string;
    status: string;
    createdAt: string;
    rejectionReason?: string;
  } | null>(null);

  // Submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  // Load product from localStorage on initial render
  useEffect(() => {
    const productId = localStorage.getItem("claimProductId");
    if (productId) {
      fetchProduct(productId);
    }
  }, []);

  // Check for existing claims when product is selected
  useEffect(() => {
    const checkExistingClaim = async () => {
      if (isSignedIn && userId && selectedProduct?.id) {
        setIsCheckingClaim(true);
        setUserHasExistingClaim(false);
        setExistingClaimDetails(null);
        try {
          const response = await fetch(
            `${baseUrl}/api/claims/check-user-product?productId=${selectedProduct.id}`,
            { credentials: 'include' }
          );
          const data = await response.json();

          if (data.success && data.hasExistingClaim) {
            setUserHasExistingClaim(true);
            setExistingClaimDetails(data.claimDetails);
            toast.info("You have already submitted a claim for this product.");
          } else if (!data.success) {
            console.error("Failed to check existing claim:", data.message);
            toast.error(data.message || "Could not verify existing claims.");
          }
        } catch (err) {
          console.error("Error checking existing claim:", err);
          toast.error("An error occurred while checking your existing claims.");
        } finally {
          setIsCheckingClaim(false);
        }
      }
    };

    checkExistingClaim();
  }, [isSignedIn, userId, selectedProduct]);

  // Fetch product by ID
  const fetchProduct = async (productId: string) => {
    try {
      const response = await fetch(`${baseUrl}/api/get/product`, {
        credentials: 'include',
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id: productId }),
      });

      const data = await response.json();

      if (!response.ok || !data.success || !data.data) {
        throw new Error(data.error || "Failed to load product details");
      }

      // Ensure the product has all required fields with defaults
      const product = {
        ...data.data,
        tags: data.data.tags || [], // Ensure tags is always an array
        images: data.data.images || [],
        videos: data.data.videos || [],
        links: data.data.links || [],
        website: data.data.website || [],
        openingDays: data.data.openingDays || [],
      };

      setSelectedProduct(product);
    } catch (error) {
      console.error("Error fetching product:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to load product details"
      );
    }
  };

  // Handle product search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setError(null);

    try {
      const response = await fetch(`${baseUrl}/api/productsearch`, {
        credentials: 'include',
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query: searchQuery }),
      });

      const data = await response.json();

      if (!response.ok || !data.success || !data.data) {
        throw new Error(data.error || "Failed to search products");
      }

      setSearchResults(data.data);

      if (data.data.length === 0) {
        setError("No available products found matching your search.");
      }
    } catch (err) {
      console.error("Search error details:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Failed to search products. Please try again."
      );
    } finally {
      setIsSearching(false);
    }
  };

  // Handle key press in search input
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle file upload
  const handleFileUpload = async (uploadedFiles: File[]) => {
    const validFiles = uploadedFiles.filter((file) => {
      if (file.size > MAX_FILE_SIZE) {
        toast.error(`File ${file.name} is too large. Maximum size is 5MB.`);
        return false;
      }
      return true;
    });

    setFiles((prev) => [...prev, ...validFiles]);

    try {
      setIsUploading(true);
      const newUploadedImages: string[] = [];

      for (const file of validFiles) {
        // Skip if we already have this file uploaded
        const existingFile = uploadedImages.find((url) =>
          url.includes(file.name.replace(/\.[^/.]+$/, ""))
        );
        if (existingFile) {
          continue;
        }

        const formData = new FormData();
        formData.append("file", file);

        const response = await fetch(`${baseUrl}/api/upload/claim-images`, {
          credentials: 'include',
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const data = await response.json();
        if (data.success && data.imageUrl) {
          newUploadedImages.push(data.imageUrl);
        }
      }

      // Deduplicate before setting
      const uniqueImages = [
        ...new Set([...uploadedImages, ...newUploadedImages]),
      ];
      setUploadedImages(uniqueImages);
      toast.success("Images uploaded successfully");
    } catch (err) {
      console.error("Error uploading files:", err);
      toast.error("Failed to upload some files. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (userHasExistingClaim) {
      toast.error(
        "You have already submitted a claim for this product. Cannot submit again."
      );
      return;
    }

    // Show confirmation modal instead of directly submitting
    setIsConfirmModalOpen(true);
  };

  // Handle confirmed submission
  const handleConfirmSubmit = async () => {
    setIsConfirmModalOpen(false);
    setIsSubmitting(true);

    try {
      const response = await fetch(`${baseUrl}/api/update/claim-product`, {
        credentials: 'include',
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          product: selectedProduct,
          contactInfo: JSON.stringify(contactInfo),
          additionalInfo,
          images: uploadedImages,
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || "Failed to submit claim");
      }

      // Clear form and show success message
      setSelectedProduct(null);
      setContactInfo({ name: "", email: "", phone: "" });
      setAdditionalInfo("");
      setFiles([]);
      setUploadedImages([]);
      setSubmitSuccess(true);
      toast.success("Claim submitted successfully");
      localStorage.removeItem("claimProductId");

      // Redirect to claims page after a short delay
      setTimeout(() => {
        router.push("/claims");
      }, 2000);
    } catch (error) {
      console.error("Error submitting claim:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to submit claim. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle product selection
  const handleProductSelect = (product: iProduct) => {
    if (product.id) {
      setSelectedProduct(product);
      setSearchResults([]);
      localStorage.setItem("claimProductId", product.id);
      setError(null);
    } else {
      console.error("Selected product has no ID:", product);
      toast.error("Cannot select this product, ID is missing.");
    }
  };

  // Handle changing product selection
  const handleChangeProduct = () => {
    setSelectedProduct(null);
    setSearchResults([]);
    setSearchQuery("");
    setError(null);
    localStorage.removeItem("claimProductId");
    // Reset claim check states when product is changed/deselected
    setUserHasExistingClaim(false);
    setExistingClaimDetails(null);
    setIsCheckingClaim(false);
  };

  // Success view
  if (submitSuccess) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center text-green-600">
            <CheckCircle className="mr-2 h-6 w-6" />
            Claim Submitted Successfully!
          </CardTitle>
          <CardDescription>
            Your claim has been submitted. We will review it and get back to you
            soon.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => router.push("/dashboard/claims")}>
            View My Claims
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <AuthCheck isSignedIn={isSignedIn ?? false}>
      <Card>
        <CardHeader>
          <CardTitle>
            {selectedProduct ? "Claim Product" : "Search for Product to Claim"}
          </CardTitle>
          <CardDescription>
            {selectedProduct
              ? "Fill out the form below to claim ownership of the selected product."
              : "Search for the product you wish to claim by its name or ID."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!selectedProduct ? (
            <ProductSearch
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              handleSearch={handleSearch}
              handleKeyPress={handleKeyPress}
              isSearching={isSearching}
              error={error}
              searchResults={searchResults}
              onProductSelect={handleProductSelect}
            />
          ) : (
            <>
              <SelectedProductDisplay
                selectedProduct={selectedProduct}
                onChangeProduct={handleChangeProduct}
                isCheckingClaim={isCheckingClaim}
                userHasExistingClaim={userHasExistingClaim}
                existingClaimDetails={existingClaimDetails}
              />

              <form onSubmit={handleSubmit} className="space-y-6 mt-6">
                <ClaimFormFields
                  contactInfo={contactInfo}
                  setContactInfo={setContactInfo}
                  additionalInfo={additionalInfo}
                  setAdditionalInfo={setAdditionalInfo}
                  uploadedImages={uploadedImages}
                  setUploadedImages={setUploadedImages}
                  handleFileUpload={handleFileUpload}
                  isUploading={isUploading}
                  isDisabled={isCheckingClaim || userHasExistingClaim}
                  maxImages={MAX_IMAGES}
                />

                <SubmitButton
                  isSubmitting={isSubmitting}
                  isDisabled={
                    isSubmitting ||
                    isUploading ||
                    isCheckingClaim ||
                    userHasExistingClaim ||
                    !selectedProduct
                  }
                />

                {/* Claim Confirmation Modal */}
                <ClaimConfirmModal
                  isOpen={isConfirmModalOpen}
                  onClose={() => setIsConfirmModalOpen(false)}
                  onConfirm={handleConfirmSubmit}
                  product={selectedProduct}
                  contactInfo={contactInfo}
                  isLoading={isSubmitting}
                />
              </form>
            </>
          )}
        </CardContent>
      </Card>
    </AuthCheck>
  );
};

export default ProductClaimForm;
