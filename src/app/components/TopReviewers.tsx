'use client';

import { baseUrl } from '@/app/util/serverFunctions';
import { useQuery } from "@tanstack/react-query";
import { profileUrl } from "@/app/util/userHelpers";
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CarouselWrapper from "./CarouselWrapper";
import { Badge } from '@/components/ui/badge';
import { Star, Trophy, User } from 'lucide-react';
import { TopReviewer, TopReviewersResponse } from '@/app/util/Interfaces';


// Reusable card for both carousel (mobile) and grid (desktop)
const ReviewerCard = ({ reviewer, index }: { reviewer: TopReviewer; index: number }) => (
    <Link
        key={reviewer.userId}
        href={profileUrl({ id: reviewer.userId, userName: reviewer.user.userName })}
        className="block group"
    >
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100 hover:border-green-300 transition-all duration-200 hover:shadow-md group-hover:scale-105">
            <div className="flex items-center space-x-3">
                {/* Rank Badge */}
                <div className="flex-shrink-0">
                    <Badge
                        variant="outline"
                        className="font-bold bg-green-100 text-green-800 border-green-300"
                    >
                        #{index + 1}
                    </Badge>
                </div>
                {/* Avatar */}
                <div className="flex-shrink-0">
                    {reviewer.user.avatar ? (
                        <Image
                            src={reviewer.user.avatar}
                            alt={`${reviewer.user.firstName} ${reviewer.user.lastName}`}
                            width={56}
                            height={56}
                            className="rounded-full border-2 border-white shadow-sm"
                        />
                    ) : (
                        <div className="w-14 h-14 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center border-2 border-white shadow-sm">
                            <User className="h-7 w-7 text-white" />
                        </div>
                    )}
                </div>
                {/* User Info */}
                <div className="flex-1 min-w-0">
                    <p className="font-semibold text-gray-900 truncate group-hover:text-green-600 transition-colors">
                        {reviewer.user.firstName} {reviewer.user.lastName}
                    </p>
                    <p className="text-xs sm:text-sm text-gray-500 truncate">@{reviewer.user.userName}</p>
                    <div className="flex items-center gap-1 mt-1">
                        <Star className="h-3 w-3 text-yellow-500 fill-current" />
                        <span className="text-xs text-gray-600 font-medium">{reviewer.reviewCount} reviews</span>
                    </div>
                </div>
            </div>
        </div>
    </Link>
);

export default function TopReviewers() {
    const { data: topReviewers = [], isLoading: loading, isError, error } = useQuery({
        queryKey: ['topReviewers'],
        queryFn: async () => {
                        const response = await fetch(`${baseUrl}/api/top-reviewers`, { credentials: 'include' });
            
            if (!response.ok) {
                throw new Error('Failed to fetch top reviewers');
            }
            
            const result: TopReviewersResponse = await response.json();
            
            if (!result.success) {
                throw new Error('Failed to load top reviewers');
            }
            
            return result.data;
        },
    });

    if (loading) {
        return (
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4 pb-4">
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                    <CardHeader className="text-center">
                        <CardTitle className="text-xl sm:text-2xl font-extrabold text-gray-800 flex items-center justify-center gap-2">
                            <Trophy className="h-6 w-6 text-yellow-500" />
                            Top Reviewers
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col gap-4">
                            {[...Array(6)].map((_, index) => (
                                <div key={index} className="animate-pulse">
                                    <div className="bg-gray-200 rounded-lg p-4 h-24"></div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (isError || topReviewers.length === 0) {
        return null; // Don't show the section if there's an error or no data
    }

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4 pb-4">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center">
                    <CardTitle className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-black text-gray-800 flex items-center justify-center gap-3 leading-tight">
                        <Trophy className="h-8 w-8 sm:h-10 sm:w-10 text-green-500" />
                        Top Reviewers
                    </CardTitle>
                    <p className="text-gray-600 mt-4 text-base sm:text-lg font-medium">
                        Our most active community members sharing valuable insights
                    </p>
                </CardHeader>
                <CardContent>
                    {/* Vertical stack layout for all screen sizes */}
                    <div className="flex flex-col gap-4">
                        {topReviewers.map((reviewer, index) => (
                            <ReviewerCard key={reviewer.userId} reviewer={reviewer} index={index} />
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}