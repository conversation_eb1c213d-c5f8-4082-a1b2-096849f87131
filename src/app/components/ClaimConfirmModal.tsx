"use client";

import React from "react";
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Shield, AlertTriangle, CheckCircle2, Building } from "lucide-react";
import { iProduct } from "../util/Interfaces";

interface ClaimConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  product: iProduct;
  contactInfo: {
    name: string;
    email: string;
    phone: string;
  };
  isLoading?: boolean;
}

export default function ClaimConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  product,
  contactInfo,
  isLoading = false,
}: ClaimConfirmModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl font-semibold text-myTheme-primary">
            <Shield className="h-6 w-6 mr-3 text-myTheme-primary" />
            Confirm Ownership Claim
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 mt-2">
            Please confirm that you are the legitimate owner of this business before proceeding.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Business Summary */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
            <h3 className="font-semibold text-myTheme-primary mb-2 flex items-center">
              <Building className="h-4 w-4 mr-2" />
              You're claiming ownership of:
            </h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">Business:</span> {product.name}</p>
              <p><span className="font-medium">Location:</span> {product.streetAddress || product.address || "Not specified"}</p>
              <p><span className="font-medium">Your Contact:</span> {contactInfo.name} ({contactInfo.email})</p>
            </div>
          </div>

          {/* Ownership Verification */}
          <div className="space-y-3">
            <div className="flex items-start space-x-3 p-3 bg-red-50 rounded-lg border border-red-200">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-red-800 mb-1">Ownership Verification</p>
                <p className="text-red-700">
                  I confirm that I am the legitimate owner, manager, or authorized representative of this business.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-yellow-800 mb-1">Legal Responsibility</p>
                <p className="text-yellow-700">
                  I understand that false claims are prohibited and may result in account suspension or legal action.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg border border-green-200">
              <CheckCircle2 className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-green-800 mb-1">Documentation Ready</p>
                <p className="text-green-700">
                  I can provide business documentation (license, registration, etc.) if requested during verification.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <CheckCircle2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 mb-1">Contact Information</p>
                <p className="text-blue-700">
                  The contact information provided is accurate and I can be reached for verification purposes.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg text-xs text-gray-600">
            <p className="mb-2">
              <strong>What happens next:</strong>
            </p>
            <ul className="list-disc list-inside space-y-1">
              <li>Your claim will be reviewed by our verification team</li>
              <li>We may contact you to verify ownership</li>
              <li>Once approved, you'll gain management access to this listing</li>
              <li>Processing typically takes 1-3 business days</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isLoading}
            className="w-full sm:w-auto bg-gradient-to-r from-myTheme-primary to-myTheme-secondary hover:from-myTheme-primary/90 hover:to-myTheme-secondary/90"
          >
            {isLoading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting Claim...
              </div>
            ) : (
              "Yes, I Confirm Ownership"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}