"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { invalidateProductCaches } from "../util/cacheInvalidation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { iProduct } from "@/app/util/Interfaces";
import { Plus, Trash2, X, Upload } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import SmartTags from "@/app/components/SmartTags";
import LocationPicker from "./LocationPicker";
import ImageUpload from "./ImageUpload";
import CitySelector from "./CitySelector";
import ProductImageUploadModal from "./ProductImageUploadModal";
import { updateProduct } from "../util/api";
import BusinessHoursInput from "./BusinessHoursInput";
import { createDefaultBusinessHours } from "../types/businessHours";

interface EditProductFormProps {
  initialProduct: iProduct;
  onSuccess?: () => void;
  onSubmit?: (updatedProduct: iProduct) => Promise<void>;
  onCancel?: () => void;
}

export default function EditProductForm({
  initialProduct,
  onSuccess,
  onSubmit,
  onCancel,
}: EditProductFormProps) {
  const [product, setProduct] = useState<iProduct>({
    ...initialProduct,
    businessHours: initialProduct.businessHours || createDefaultBusinessHours()
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [imageInputValue, setImageInputValue] = useState("");
  const [videoInputValue, setVideoInputValue] = useState("");
  const [linkInputValue, setLinkInputValue] = useState("");
  const [websiteInputValue, setWebsiteInputValue] = useState("");
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);

  const router = useRouter();
  const queryClient = useQueryClient();

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setProduct((prev) => ({ ...prev, [name]: value }));
  };

  const handleArrayInput = (field: keyof iProduct, value: string) => {
    setProduct((prev) => ({
      ...prev,
      [field]: [...((prev[field] as string[]) || []), value],
    }));
  };

  const handleRemoveArrayItem = (field: keyof iProduct, index: number) => {
    setProduct((prev) => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index),
    }));
  };

  const handleImagesUploaded = (imageUrls: string[]) => {
    setProduct((prev) => ({
      ...prev,
      images: imageUrls, // ProductImageUploadModal already handles merging with existing images
    }));
  };

  const mutation = useMutation({
    mutationFn: (product: iProduct) => updateProduct(product),
    onSuccess: async (data) => {
      setIsLoading(false);
      setError(null);

      try {
        // Comprehensive cache invalidation (Redis + React Query + Next.js)
        const invalidationResult = await invalidateProductCaches({
          invalidateServer: true,
          invalidateClient: false, // We'll handle React Query manually for better error control
          queryKeys: [["products"]],
          serverActions: ["invalidate-all-products", "invalidate-search"]
        });

        // Check if server invalidation failed
        if (!invalidationResult.serverInvalidation && invalidationResult.errors.length > 0) {
          console.error("Server cache invalidation failed:", invalidationResult.errors);
          throw new Error("Failed to invalidate server cache");
        }

        // Only invalidate React Query if server invalidation succeeded
        await queryClient.invalidateQueries({ queryKey: ["products"] });

        if (onSuccess) {
          onSuccess();
        } else {
          router.push(
            `/mybusinesses/productsuccess?product=${encodeURIComponent(JSON.stringify(data))}`,
          );
        }
      } catch (error) {
        console.error("Cache invalidation failed:", error);
        setError("Product updated but cache refresh failed. You may need to refresh the page to see changes.");
        
        // Still call success callback but with warning
        if (onSuccess) {
          onSuccess();
        } else {
          router.push(
            `/mybusinesses/productsuccess?product=${encodeURIComponent(JSON.stringify(data))}&cacheWarning=true`,
          );
        }
      }
    },
    onError: (error: Error) => {
      setIsLoading(false);
      setError(error.message);
    },
  });

  const handleImageUploaded = (imageUrl: string) => {
    setProduct((prev) => ({ ...prev, display_image: imageUrl }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (!product.id) {
      setError("Product ID is missing");
      setIsLoading(false);
      return;
    }

    // Check if at least one day is open in either legacy or flexible business hours
    const hasLegacyOpeningDays = product.openingDays && product.openingDays.length > 0;
    const hasFlexibleOpeningDays = product.businessHours?.schedules.some(schedule => !schedule.isClosed);
    
    if (!hasLegacyOpeningDays && !hasFlexibleOpeningDays) {
      setError("Please select at least one opening day");
      setIsLoading(false);
      return;
    }

    try {
      if (onSubmit) {
        // Use the provided onSubmit prop if available
        await onSubmit(product);
      } else {
        // Otherwise use the default mutation
        await mutation.mutateAsync(product);
      }
    } catch (error) {
      console.error("Error updating product:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLocationSelect = (location: {
    lat: number;
    lng: number;
    address: string;
  }) => {
    setProduct({
      ...product,
      latitude: location.lat,
      longitude: location.lng,
      address: location.address,
    });
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="max-w-4xl w-full mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden"
    >
      {/* Form Header */}
      <div className="bg-gradient-to-r from-myTheme-primary to-myTheme-secondary p-6 sm:p-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">
          Edit Product
        </h1>
        <p className="text-white/80">Update your business details below</p>
      </div>

      {/* Form Content */}
      <div className="p-6 sm:p-8">
        {error && (
          <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-xl border border-red-200">
            {error}
          </div>
        )}

        <div className="space-y-8">
          {/* Two Column Layout for Main Sections */}
          <div className="space-y-4">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Basic Information Section */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
                <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                  <div className="w-2 h-2 bg-myTheme-primary rounded-full mr-3"></div>
                  Basic Information
                </h2>

                <div className="space-y-4">
                  <div>
                    <Label
                      htmlFor="name"
                      className="text-myTheme-primary font-medium"
                    >
                      Business Name *
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      value={product.name}
                      onChange={handleChange}
                      required
                      className="mt-2 border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20"
                      placeholder="Enter your business name"
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="description"
                      className="text-myTheme-primary font-medium"
                    >
                      Description *
                    </Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={product.description}
                      onChange={handleChange}
                      required
                      className="mt-2 border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20 min-h-[120px]"
                      placeholder="Describe your business, products, or services..."
                    />
                  </div>
                </div>
              </div>

              {/* Media Section */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
                <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  Media & Links
                </h2>
                <div className="text-sm text-gray-500 mb-4 p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium mb-1">Image Link Sources:</p>
                </div>

                <div className="space-y-6">
                  <ImageUpload
                    initialImage={product.display_image}
                    onImageUploaded={handleImageUploaded}
                    label="Display Image / Logo"
                  />

                  <div>
                    <Label className="text-myTheme-primary font-medium">
                      Product Images
                    </Label>
                    <div className="mt-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsImageModalOpen(true)}
                        className="w-full border-green-200 hover:bg-green-50 text-green-700"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Product Images ({product.images?.length || 0})
                      </Button>
                      
                      {product.images && product.images.length > 0 && (
                        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                          {product.images.map((image, index) => (
                            <div key={index} className="relative group">
                              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                                <img
                                  src={image}
                                  alt={`Product image ${index + 1}`}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <Button
                                type="button"
                                variant="destructive"
                                size="icon"
                                onClick={() => handleRemoveArrayItem("images", index)}
                                className="absolute -top-2 -right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-myTheme-primary font-medium">
                      Video Links
                    </Label>
                    <div className="flex items-center space-x-2 mt-2">
                      <Input
                        placeholder="Video URL (YouTube, Vimeo, etc.)"
                        value={videoInputValue}
                        onChange={(e) => setVideoInputValue(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            if (videoInputValue.trim()) {
                              handleArrayInput(
                                "videos",
                                videoInputValue.trim(),
                              );
                              setVideoInputValue("");
                            }
                          }
                        }}
                        className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          if (videoInputValue.trim()) {
                            handleArrayInput("videos", videoInputValue.trim());
                            setVideoInputValue("");
                          }
                        }}
                        className="border-green-200 hover:bg-green-50"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="mt-2 space-y-2">
                      {product.videos?.map((video, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100"
                        >
                          <Input
                            value={video}
                            readOnly
                            className="bg-gray-50"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() =>
                              handleRemoveArrayItem("videos", index)
                            }
                            className="border-red-200 hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-myTheme-primary font-medium">
                      Additional Links
                    </Label>
                    <div className="flex items-center space-x-2 mt-2">
                      <Input
                        placeholder="Link URL"
                        value={linkInputValue}
                        onChange={(e) => setLinkInputValue(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            if (linkInputValue.trim()) {
                              handleArrayInput("links", linkInputValue.trim());
                              setLinkInputValue("");
                            }
                          }
                        }}
                        className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          if (linkInputValue.trim()) {
                            handleArrayInput("links", linkInputValue.trim());
                            setLinkInputValue("");
                          }
                        }}
                        className="border-green-200 hover:bg-green-50"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="mt-2 space-y-2">
                      {product.links?.map((link, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100"
                        >
                          <Input value={link} readOnly className="bg-gray-50" />
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() =>
                              handleRemoveArrayItem("links", index)
                            }
                            className="border-red-200 hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-myTheme-primary font-medium">
                      Website URLs
                    </Label>
                    <div className="flex items-center space-x-2 mt-2">
                      <Input
                        placeholder="Website URL (https://...)"
                        value={websiteInputValue}
                        onChange={(e) => setWebsiteInputValue(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            if (websiteInputValue.trim()) {
                              handleArrayInput(
                                "website",
                                websiteInputValue.trim(),
                              );
                              setWebsiteInputValue("");
                            }
                          }
                        }}
                        className="border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          if (websiteInputValue.trim()) {
                            handleArrayInput(
                              "website",
                              websiteInputValue.trim(),
                            );
                            setWebsiteInputValue("");
                          }
                        }}
                        className="border-green-200 hover:bg-green-50"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="mt-2 space-y-2">
                      {product.website?.map((site, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-100"
                        >
                          <Input value={site} readOnly className="bg-gray-50" />
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() =>
                              handleRemoveArrayItem("website", index)
                            }
                            className="border-red-200 hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                {/* Categories Section */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
                  <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                    Categories & Tags
                  </h2>

                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-100">
                      <Label className="text-blue-700 font-medium flex items-center">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                        AI-Suggested Categories
                      </Label>
                      <p className="text-sm text-blue-600 mb-3 mt-1">
                        Based on your product description
                      </p>
                      <div className="flex flex-wrap gap-2">
                        <SmartTags
                          description={product.description}
                          handleArrayInput={handleArrayInput}
                          handleRemoveArrayItem={handleRemoveArrayItem}
                          field="tags"
                        />
                      </div>
                    </div>

                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      <Label className="text-myTheme-primary font-medium flex items-center">
                        <div className="w-1.5 h-1.5 bg-myTheme-primary rounded-full mr-2"></div>
                        Manual Categories
                      </Label>
                      <p className="text-sm text-gray-600 mb-3 mt-1">
                        Add your own categories
                      </p>
                      <div className="flex items-center space-x-2">
                        <Input
                          placeholder="Enter a category"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              const value = e.currentTarget.value.trim();
                              if (value) {
                                handleArrayInput("tags", value);
                                e.currentTarget.value = "";
                              }
                            }
                          }}
                          className="border-gray-200 focus:border-purple-400 focus:ring-purple-400/20"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            const input = document.querySelector(
                              'input[placeholder="Enter a category"]',
                            ) as HTMLInputElement;
                            const value = input.value.trim();
                            if (value) {
                              handleArrayInput("tags", value);
                              input.value = "";
                            }
                          }}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="mt-2 flex flex-wrap md:gap-2 gap-4">
                        {product.tags?.map((tag, index) => (
                          <div
                            key={index}
                            className="flex items-center bg-gray-100 rounded-full px-3 py-1"
                          >
                            <span>{tag}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="h-4 w-4 ml-2"
                              onClick={() =>
                                handleRemoveArrayItem("tags", index)
                              }
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Business Hours Section */}
                <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-xl border border-orange-100">
                  <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                    Business Hours
                  </h2>

                  <div className="space-y-4">
                    {/* Flexible Business Hours Input */}
                    <BusinessHoursInput
                      value={product.businessHours || createDefaultBusinessHours()}
                      onChange={(businessHours) => {
                        setProduct({
                          ...product,
                          businessHours,
                          // Update legacy fields for backward compatibility
                          openingDays: businessHours.schedules
                            .filter(schedule => !schedule.isClosed)
                            .map(schedule => schedule.day),
                        });
                      }}
                    />
                    
                    {/* Legacy Business Hours (hidden but maintained for backward compatibility) */}
                    <div className="hidden">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label
                            htmlFor="openingHrs"
                            className="text-myTheme-primary font-medium"
                          >
                            Opening Hours
                          </Label>
                          <Input
                            id="openingHrs"
                            name="openingHrs"
                            type="time"
                            value={product.openingHrs || ""}
                            onChange={handleChange}
                            className="mt-2 border-gray-200 focus:border-orange-400 focus:ring-orange-400/20"
                          />
                        </div>
                        <div>
                          <Label
                            htmlFor="closingHrs"
                            className="text-myTheme-primary font-medium"
                          >
                            Closing Hours
                          </Label>
                          <Input
                            id="closingHrs"
                            name="closingHrs"
                            type="time"
                            value={product.closingHrs || ""}
                            onChange={handleChange}
                            className="mt-2 border-gray-200 focus:border-orange-400 focus:ring-orange-400/20"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information Section */}
                <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-6 rounded-xl border border-teal-100">
                  <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                    <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
                    Contact Information
                  </h2>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label
                        htmlFor="telephone"
                        className="text-myTheme-primary font-medium"
                      >
                        Phone Number
                      </Label>
                      <Input
                        id="telephone"
                        name="telephone"
                        value={product.telephone || ""}
                        onChange={handleChange}
                        placeholder="+****************"
                        className="mt-2 border-gray-200 focus:border-teal-400 focus:ring-teal-400/20"
                      />
                    </div>
                    <div>
                      <Label
                        htmlFor="email"
                        className="text-myTheme-primary font-medium"
                      >
                        Email
                      </Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={product.email || ""}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                        className="mt-2 border-gray-200 focus:border-teal-400 focus:ring-teal-400/20"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Location Section - Full Width */}
            <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-xl border border-indigo-100">
              <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-indigo-500 rounded-full mr-3"></div>
                Location & Address
              </h2>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Street Address */}
                  <div>
                    <Label
                      htmlFor="street-address"
                      className="text-myTheme-primary font-medium"
                    >
                      Street Address *
                    </Label>
                    <Input
                      id="street-address"
                      name="streetAddress"
                      value={product.streetAddress || ""}
                      onChange={handleChange}
                      placeholder="123 Main Street, Apt 4B"
                      className="mt-2 border-gray-200 focus:border-indigo-400 focus:ring-indigo-400/20"
                    />
                    <p className="text-sm text-gray-600 mt-1">
                      Street number, name, and unit/apartment if applicable
                    </p>
                  </div>

                  {/* City/Area Selector */}
                  <CitySelector
                    value={product.city}
                    onChange={(city) =>
                      setProduct((prev) => ({ ...prev, city }))
                    }
                    label="City/Area"
                    placeholder="Select city or area"
                  />
                </div>

                {/* Legacy Address Field (for backward compatibility) */}
                {product.address && !product.streetAddress && !product.city && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <Label className="text-yellow-800 font-medium">
                      Legacy Address
                    </Label>
                    <p className="text-sm text-yellow-700 mt-1">
                      This product has an old-style address. You can split it
                      into street and city above.
                    </p>
                    <p className="text-sm text-yellow-600 mt-1 font-mono">
                      {product.address}
                    </p>
                  </div>
                )}

                <p className="text-sm text-gray-600">
                  You can enter address details above and/or use the map below
                  to select a location
                </p>

                <div>
                  <Label className="text-myTheme-primary font-medium">
                    Map Location (Optional)
                  </Label>
                  <p className="text-sm text-gray-600 mb-3 mt-1">
                    Click on the map to update the location or use "Use My
                    Current Location" button
                  </p>
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <LocationPicker onLocationSelect={handleLocationSelect} />
                  </div>
                </div>

                {product.address && (
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <p className="text-sm text-gray-600 font-medium">
                      Current Address:
                    </p>
                    <p className="text-sm text-myTheme-primary mt-1">
                      {product.address}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="mt-8 flex justify-center space-x-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="max-w-md py-3 px-8 rounded-xl"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full max-w-md bg-gradient-to-r from-myTheme-primary to-myTheme-secondary hover:from-myTheme-primary/90 hover:to-myTheme-secondary/90 text-white font-semibold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            >
              {isLoading ? "Updating Product..." : "Update Product"}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Product Image Upload Modal */}
      <ProductImageUploadModal
        isOpen={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        onImagesUploaded={handleImagesUploaded}
        existingImages={product.images || []}
        maxImages={10}
      />
    </form>
  );
}