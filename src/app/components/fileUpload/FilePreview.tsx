import ProgressBar from "@ramonak/react-progress-bar";
import Image from "next/legacy/image";

type Props = {
  imagePreviews: string[];
  files: File[];
  uploadProgress: number[];
  removeFile: (index: number) => void;
  cancelUpload: (index: number) => void;
  uploading: boolean;
};

const AFileDisplay = ({
  imagePreviews,
  files,
  uploadProgress,
  removeFile,
  cancelUpload,
  uploading,
}: Props) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
      {imagePreviews.map((preview, index) => (
        <div key={index} className="relative group">
          <div className="aspect-square relative overflow-hidden rounded-lg bg-white shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
            <Image
              src={preview}
              alt={`Preview ${index + 1}`}
              layout="fill"
              objectFit="cover"
              className="rounded-lg"
            />
            
            {/* Remove button */}
            <button
              onClick={() => removeFile(index)}
              className="absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg"
              title="Remove image"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Upload progress overlay */}
            {uploadProgress[index] > 0 && uploadProgress[index] < 100 && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                <div className="text-center">
                  <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mb-2"></div>
                  <p className="text-white text-sm font-medium">{uploadProgress[index]}%</p>
                </div>
              </div>
            )}

            {/* Success overlay */}
            {uploadProgress[index] === 100 && (
              <div className="absolute inset-0 bg-green-500 bg-opacity-20 flex items-center justify-center rounded-lg">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            )}

            {/* Hover tooltip */}
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <p className="text-xs font-medium truncate">{files[index].name}</p>
              <p className="text-xs text-gray-300">
                {files[index].type} • {(files[index].size / 1024 / 1024).toFixed(1)} MB
              </p>
            </div>
          </div>

          {/* Progress bar below image */}
          {uploadProgress[index] > 0 && uploadProgress[index] < 100 && (
            <div className="mt-2">
              <ProgressBar
                completed={uploadProgress[index]}
                className="w-full"
                bgColor="#10b981"
                baseBgColor="#e5e7eb"
                height="6px"
                isLabelVisible={false}
              />
            </div>
          )}

          {/* Cancel button during upload */}
          {uploading && uploadProgress[index] < 100 && (
            <button
              onClick={() => cancelUpload(index)}
              className="mt-2 w-full px-3 py-1 bg-orange-500 hover:bg-orange-600 text-white text-xs rounded-md transition-colors duration-200"
            >
              Cancel Upload
            </button>
          )}
        </div>
      ))}
    </div>
  );
};

export default AFileDisplay;
