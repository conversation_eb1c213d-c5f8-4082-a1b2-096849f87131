"use client";
import React, { useCallback, useEffect, useState } from "react";
import Dropzone, { FileRejection } from "react-dropzone";
import axios, { CancelTokenSource } from "axios";
//@ts-ignore
import FilePreview from "./FilePreview";
import UploadError from "./UploadError";
import getIconForFileType from "@/app/util/GetIconForFileType";
import { useImageResizer } from "@/app/util/useImageResizer";
export const revalidate = 0;

interface FileUploadProps {
  setLinksArray: React.Dispatch<React.SetStateAction<string[]>>;
  setAllUploaded: React.Dispatch<React.SetStateAction<boolean>>;
  allUploaded: boolean;
  onStatusChange?: (hasFiles: boolean, isUploading: boolean) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({
  setLinksArray,
  setAllUploaded,
  allUploaded,
  onStatusChange,
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number[]>([]);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const { processImage, isResizing } = useImageResizer();

  useEffect(() => {
    if (files.length === 0) {
      setAllUploaded(true);
    } else {
      setAllUploaded(false);
    }
  }, [files, setAllUploaded]);
  
  // Notify parent component about file and upload status changes
  useEffect(() => {
    if (onStatusChange) {
      onStatusChange(files.length > 0, uploading);
    }
  }, [files.length, uploading, onStatusChange]);

  const handleDrop = useCallback(
    async (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
      const imageFiles = acceptedFiles.filter((file) =>
        file.type.startsWith("image/")
      );

      const resizedFiles: File[] = [];
      const newPreviews: string[] = [];
      const newProgress: number[] = [];

      for (const file of imageFiles) {
        try {
          const result = await processImage(file, { maxDimension: 500 });

          // Check if result has a file property (it's a ResizeResultFile)
          if ("file" in result) {
            resizedFiles.push(result.file);
            const preview = getIconForFileType([result.file]);
            newPreviews.push(...preview);
            newProgress.push(0);
          } else {
            // This shouldn't happen when input is a File, but handle it just in case
            console.warn(
              "Unexpected result type when processing file:",
              file.name
            );
            setUploadErrors((prev) => [
              ...prev,
              `Error processing ${file.name}: unexpected result type`,
            ]);
          }
        } catch (error) {
          console.error("Error processing image:", error);
          setUploadErrors((prev) => [...prev, `Error processing ${file.name}`]);
        }
      }

      setFiles((prevFiles) => [...prevFiles, ...resizedFiles]);
      setImagePreviews((prevPreviews) => [...prevPreviews, ...newPreviews]);
      setUploadProgress((prevProgress) => [...prevProgress, ...newProgress]);

      // Handle rejected files
      if (rejectedFiles.length > 0) {
        const rejectionErrors = rejectedFiles.map(
          (rejection) =>
            `${rejection.file.name}: ${rejection.errors[0].message}`
        );
        setUploadErrors((prev) => [...prev, ...rejectionErrors]);
      }
    },
    [processImage]
  );

  const removeFile = (index: number) => {
    const updatedFiles = [...files];
    updatedFiles.splice(index, 1);
    const updatedPreviews = [...imagePreviews];
    updatedPreviews.splice(index, 1);
    const updatedProgress = [...uploadProgress];
    updatedProgress.splice(index, 1);
    setFiles(updatedFiles);
    setImagePreviews(updatedPreviews);
    setUploadProgress(updatedProgress);
    cancelUpload(index);
  };

  const uploadFile = async (file: File, index: number): Promise<void> => {
    const formData = new FormData();
    formData.append("file", file);
    const cancelSource = axios.CancelToken.source();
    try {
      const response = await axios.post("/api/create/image-link", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / (progressEvent.total || 1)
          );
          setUploadProgress((prevProgress) => {
            const updatedProgress = [...prevProgress];
            updatedProgress[index] = percentCompleted;
            return updatedProgress;
          });
        },
        cancelToken: cancelSource.token,
      });

      if (response.data.link) {
        setLinksArray((prevLinks) => [...prevLinks, response.data.link]);
      }
    } catch (error) {
      if (axios.isCancel(error)) {
        return;
      }
      setUploadErrors((prevErrors) => [
        ...prevErrors,
        `Error uploading ${file.name}: ${error}`,
      ]);
      throw error;
    }
  };

  const cancelTokenSources: CancelTokenSource[] = [];
  const cancelUpload = (index: number) => {
    cancelTokenSources[index]?.cancel("Upload canceled by user");
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      setAllUploaded(true);
      return;
    }
    setUploading(true);
    setAllUploaded(false); // Reset the upload status

    const uploadPromises = files.map((file, index) => {
      const cancelSource = axios.CancelToken.source();
      cancelTokenSources[index] = cancelSource;
      return uploadFile(file, index);
    });

    try {
      await Promise.all(uploadPromises);
      setAllUploaded(true); // Set to true when all uploads are complete
    } catch (error) {
      console.error("Error uploading files:", error);
      setAllUploaded(false);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="w-full space-y-4">
      <Dropzone onDrop={handleDrop} multiple={true}>
        {({ getRootProps, getInputProps, isDragActive }) => (
          <div 
            {...getRootProps()}
            className={`w-full border-2 border-dashed rounded-xl p-8 transition-all duration-300 cursor-pointer ${
              isDragActive 
                ? 'border-green-400 bg-green-50 scale-105' 
                : 'border-gray-300 bg-gray-50 hover:border-green-300 hover:bg-green-25'
            }`}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center justify-center text-center space-y-3">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${
                isDragActive ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-500'
              }`}>
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <div>
                <p className={`text-lg font-medium ${isDragActive ? 'text-green-700' : 'text-gray-700'}`}>
                  {isDragActive ? 'Drop your images here' : 'Add Photos'}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Drag and drop images or click to browse
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Supports JPG, PNG, GIF up to 10MB each
                </p>
              </div>
            </div>
          </div>
        )}
      </Dropzone>

      {isResizing && (
        <div className="flex items-center justify-center p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
          <p className="text-blue-700 font-medium">Resizing images...</p>
        </div>
      )}

      {files.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">
              Selected Images ({files.length})
            </h4>
            {!allUploaded && !uploading && (
              <button
                type="button"
                onClick={() => {
                  setFiles([]);
                  setImagePreviews([]);
                  setUploadProgress([]);
                }}
                className="text-sm text-red-600 hover:text-red-800 font-medium"
              >
                Remove All
              </button>
            )}
          </div>
          
          <FilePreview
            uploading={uploading}
            imagePreviews={imagePreviews}
            files={files}
            uploadProgress={uploadProgress}
            removeFile={removeFile}
            cancelUpload={cancelUpload}
          />
          
          <div className="flex justify-center">
            <button
              type="button"
              onClick={uploadFiles}
              disabled={allUploaded || uploading}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 ${
                allUploaded 
                  ? "bg-green-600 text-white cursor-default" 
                  : uploading 
                  ? "bg-gray-400 text-white cursor-not-allowed" 
                  : "bg-myTheme-primary hover:bg-myTheme-secondary text-white shadow-lg hover:shadow-xl"
              }`}
            >
              {allUploaded ? (
                <span className="flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Successfully Uploaded!
                </span>
              ) : uploading ? (
                <span className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Uploading...
                </span>
              ) : (
                <span className="flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  Upload {files.length} Image{files.length > 1 ? 's' : ''}
                </span>
              )}
            </button>
          </div>
        </div>
      )}
      
      {uploadErrors.length > 0 && <UploadError uploadErrors={uploadErrors} />}
    </div>
  );
};

export default FileUpload;
