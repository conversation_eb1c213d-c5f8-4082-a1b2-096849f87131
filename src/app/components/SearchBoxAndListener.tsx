import SearchComponent from "@/app/components/SearchComponent";
import ProductListLoader from "@/app/components/ProductListLoader"; // Import the loader component
import { iProduct } from '@/app/util/Interfaces';

interface SearchProps {
  onSearch?: (query: string) => void;
}

const Search: React.FC<SearchProps> = ({ onSearch }) => {
  const handleSearch = (query: string) => {
    if (onSearch) {
      onSearch(query);
    }
  };

  return (
    <div className="flex flex-col pt-2 md:pt-8 bg-transparent h-full w-full items-center justify-start relative z-[200]">
      <SearchComponent onSearch={handleSearch} />
      <ProductListLoader />
    </div>
  );
};

export default Search;
