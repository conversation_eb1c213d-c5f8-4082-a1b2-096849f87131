import { iProduct } from "../util/Interfaces";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ImageModal from "./ImageModal";

interface ProductShowcaseProps {
    product: iProduct;
}

// Function to normalize and validate image URLs from various sources
const normalizeImageUrl = (url: string): string => {
    try {
        const urlObj = new URL(url);
        
        // Handle Unsplash URLs
        if (url.includes('unsplash.com')) {
            if (!urlObj.searchParams.has('fm')) {
                urlObj.searchParams.set('fm', 'jpg');
            }
            if (!urlObj.searchParams.has('q')) {
                urlObj.searchParams.set('q', '80');
            }
            if (!urlObj.searchParams.has('w')) {
                urlObj.searchParams.set('w', '400');
            }
            if (!urlObj.searchParams.has('h')) {
                urlObj.searchParams.set('h', '400');
            }
            return urlObj.toString();
        }
        
        // Handle Pexels URLs
        if (url.includes('pexels.com')) {
            // Pexels URLs work well as-is, but we can add size optimization
            if (url.includes('/photos/') && !url.includes('?')) {
                return `${url}?auto=compress&cs=tinysrgb&w=400&h=400&dpr=1`;
            }
            return url;
        }
        
        // Handle Pixabay URLs
        if (url.includes('pixabay.com')) {
            // Pixabay URLs usually work well as-is
            return url;
        }
        
        // Handle Cloudinary URLs (already optimized usually)
        if (url.includes('cloudinary.com')) {
            return url;
        }
        
        // Handle AWS S3 URLs
        if (url.includes('amazonaws.com') || url.includes('s3.')) {
            return url;
        }
        
        // Handle Imgur URLs
        if (url.includes('imgur.com')) {
            // Ensure we're using the direct image URL
            if (!url.includes('i.imgur.com')) {
                return url.replace('imgur.com', 'i.imgur.com');
            }
            return url;
        }
        
        // For other URLs, return as-is
        return url;
        
    } catch (error) {
        console.warn('Invalid image URL:', url);
        return url; // Return original URL if parsing fails
    }
};

// Function to check if an image URL is likely to work
const isReliableImageSource = (url: string): boolean => {
    const reliableDomains = [
        'cloudinary.com',
        'amazonaws.com',
        's3.',
        'imgur.com',
        'unsplash.com',
        'pexels.com',
        'pixabay.com',
        'storage.googleapis.com',
        'blob.core.windows.net'
    ];
    
    const problematicDomains = [
        'instagram.com',
        'facebook.com',
        'twitter.com',
        'x.com',
        'linkedin.com',
        'drive.google.com',
        'dropbox.com',
        'onedrive.com'
    ];
    
    const lowerUrl = url.toLowerCase();
    
    // Check for problematic domains
    if (problematicDomains.some(domain => lowerUrl.includes(domain))) {
        return false;
    }
    
    // Check for reliable domains
    if (reliableDomains.some(domain => lowerUrl.includes(domain))) {
        return true;
    }
    
    // Check if URL has a proper image extension
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.svg'];
    if (imageExtensions.some(ext => lowerUrl.includes(ext))) {
        return true;
    }
    
    // Default to potentially unreliable
    return false;
};
const convertToEmbedUrl = (url: string): string => {
    // YouTube patterns
    const youtubeWatchRegex = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/;
    const youtubeShortRegex = /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]+)/;
    const youtubeEmbedRegex = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/;
    
    // TikTok patterns
    const tiktokRegex = /(?:https?:\/\/)?(?:www\.)?tiktok\.com\/@[^\/]+\/video\/(\d+)/;
    const tiktokShortRegex = /(?:https?:\/\/)?(?:vm\.)?tiktok\.com\/([a-zA-Z0-9]+)/;
    const tiktokEmbedRegex = /(?:https?:\/\/)?(?:www\.)?tiktok\.com\/embed\/v2\/(\d+)/;
    
    // Vimeo patterns
    const vimeoRegex = /(?:https?:\/\/)?(?:www\.)?vimeo\.com\/(\d+)/;
    const vimeoEmbedRegex = /(?:https?:\/\/)?player\.vimeo\.com\/video\/(\d+)/;
    
    // Check YouTube URLs
    if (youtubeEmbedRegex.test(url)) {
        return url;
    }
    
    const youtubeWatchMatch = url.match(youtubeWatchRegex);
    if (youtubeWatchMatch) {
        return `https://www.youtube.com/embed/${youtubeWatchMatch[1]}`;
    }
    
    const youtubeShortMatch = url.match(youtubeShortRegex);
    if (youtubeShortMatch) {
        return `https://www.youtube.com/embed/${youtubeShortMatch[1]}`;
    }
    
    // Check TikTok URLs
    if (tiktokEmbedRegex.test(url)) {
        return url;
    }
    
    const tiktokMatch = url.match(tiktokRegex);
    if (tiktokMatch) {
        return `https://www.tiktok.com/embed/v2/${tiktokMatch[1]}`;
    }
    
    // Handle TikTok short URLs (these are trickier and might need the full URL)
    const tiktokShortMatch = url.match(tiktokShortRegex);
    if (tiktokShortMatch) {
        // For TikTok short URLs, we'll return the original URL with a note
        // These typically redirect to the full URL, but embedding might be limited
        console.warn('TikTok short URL detected. Full video ID needed for embedding:', url);
        return url; // Return original URL - user might need to use the full TikTok URL
    }
    
    // Check Vimeo URLs
    if (vimeoEmbedRegex.test(url)) {
        return url;
    }
    
    const vimeoMatch = url.match(vimeoRegex);
    if (vimeoMatch) {
        return `https://player.vimeo.com/video/${vimeoMatch[1]}`;
    }
    
    // For other video platforms or already properly formatted URLs, return as-is
    return url;
};

const ProductShowcase = ({ product }: ProductShowcaseProps) => {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [currentVideoIndex, setCurrentVideoIndex] = useState(0);

    const hasContent = product.images.length > 0 ||
        product.videos.length > 0 ||
        product.links.length > 0 ||
        product.website.length > 0;

    if (!hasContent) return null;

    return (
        <>
            <div className="w-full max-w-4xl mx-auto mt-4 sm:mt-8 p-3 sm:p-4 bg-white rounded-lg shadow-md">
                <h2 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6">Product Showcase</h2>

                {/* Images Section */}
                {product.images.length > 0 && (
                    <div className="mb-8 sm:mb-12">
                        <div className="flex items-center justify-between mb-4 sm:mb-6">
                            <h3 className="text-lg sm:text-xl font-semibold text-gray-800">Product Gallery</h3>
                            <div className="text-sm text-gray-500">
                                {currentImageIndex + 1} of {product.images.length}
                            </div>
                        </div>
                        
                        {/* Main Featured Image */}
                        <div className="relative mb-6">
                            <div className="relative aspect-[4/3] sm:aspect-[16/10] rounded-xl overflow-hidden shadow-lg bg-gray-100 group">
                                {(() => {
                                    const currentImage = product.images[currentImageIndex];
                                    const normalizedUrl = normalizeImageUrl(currentImage);
                                    const isReliable = isReliableImageSource(currentImage);
                                    
                                    return (
                                        <>
                                            <img
                                                src={normalizedUrl}
                                                alt={`${product.name} image ${currentImageIndex + 1}`}
                                                className="w-full h-full object-cover cursor-pointer transition-transform duration-300 group-hover:scale-105"
                                                onClick={() => setSelectedImage(currentImage)}
                                                onError={(e) => {
                                                    console.error('Failed to load image:', normalizedUrl);
                                                    const target = e.target as HTMLImageElement;
                                                    target.style.display = 'none';
                                                    const container = target.parentElement;
                                                    if (container) {
                                                        container.innerHTML = `
                                                            <div class="w-full h-full flex flex-col items-center justify-center bg-gray-200 text-gray-500">
                                                                <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                                </svg>
                                                                <span class="text-sm">Failed to load image</span>
                                                            </div>
                                                        `;
                                                    }
                                                }}
                                            />
                                            
                                            {/* Click to expand overlay */}
                                            <div 
                                                className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center cursor-pointer"
                                                onClick={() => setSelectedImage(currentImage)}
                                            >
                                                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white bg-opacity-90 rounded-full p-3 pointer-events-none">
                                                    <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                                    </svg>
                                                </div>
                                            </div>
                                            
                                            {!isReliable && (
                                                <div className="absolute top-3 right-3">
                                                    <div className="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium" title="Potentially unreliable source">
                                                        ⚠ Unreliable
                                                    </div>
                                                </div>
                                            )}
                                        </>
                                    );
                                })()}
                            </div>
                            
                            {/* Navigation Buttons */}
                            {product.images.length > 1 && (
                                <>
                                    <button
                                        onClick={() => setCurrentImageIndex(prev => prev === 0 ? product.images.length - 1 : prev - 1)}
                                        className="absolute left-3 top-1/2 -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110"
                                        aria-label="Previous image"
                                    >
                                        <svg className="w-5 h-5 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                                        </svg>
                                    </button>
                                    <button
                                        onClick={() => setCurrentImageIndex(prev => prev === product.images.length - 1 ? 0 : prev + 1)}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110"
                                        aria-label="Next image"
                                    >
                                        <svg className="w-5 h-5 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </button>
                                </>
                            )}
                        </div>
                        
                        {/* Thumbnail Strip */}
                        {product.images.length > 1 && (
                            <div className="flex gap-2 sm:gap-3 overflow-x-auto pb-2">
                                {product.images.map((image, index) => {
                                    const normalizedUrl = normalizeImageUrl(image);
                                    const isActive = index === currentImageIndex;
                                    
                                    return (
                                        <div
                                            key={index}
                                            className={`relative flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
                                                isActive 
                                                    ? 'ring-2 ring-blue-500 shadow-md scale-105' 
                                                    : 'hover:ring-2 hover:ring-gray-300 hover:shadow-sm'
                                            }`}
                                            onClick={() => setCurrentImageIndex(index)}
                                        >
                                            <img
                                                src={normalizedUrl}
                                                alt={`${product.name} thumbnail ${index + 1}`}
                                                className="w-full h-full object-cover"
                                                loading="lazy"
                                            />
                                            {isActive && (
                                                <div className="absolute inset-0 bg-blue-500 bg-opacity-20"></div>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                )}

                {/* Videos Section */}
                {product.videos.length > 0 && (
                    <div className="mb-8 sm:mb-12">
                        <div className="flex items-center justify-between mb-4 sm:mb-6">
                            <h3 className="text-lg sm:text-xl font-semibold text-gray-800">Product Videos</h3>
                            {product.videos.length > 1 && (
                                <div className="text-sm text-gray-500">
                                    {currentVideoIndex + 1} of {product.videos.length}
                                </div>
                            )}
                        </div>
                        
                        {/* Featured Video */}
                        <div className="relative mb-6">
                            {(() => {
                                const currentVideo = product.videos[currentVideoIndex];
                                const embedUrl = convertToEmbedUrl(currentVideo);
                                const isTikTok = currentVideo.includes('tiktok.com');
                                
                                if (isTikTok) {
                                    return (
                                        <div className="relative aspect-video rounded-xl overflow-hidden shadow-lg bg-gradient-to-br from-pink-500 via-purple-600 to-indigo-700 flex items-center justify-center">
                                            <div className="text-center text-white p-8">
                                                <div className="mb-6">
                                                    <svg className="w-16 h-16 mx-auto mb-4 drop-shadow-lg" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43V7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.43z"/>
                                                    </svg>
                                                    <p className="text-xl font-bold mb-2">TikTok Video</p>
                                                    <p className="text-sm opacity-90 mb-6">Experience this product on TikTok</p>
                                                </div>
                                                <Link
                                                    href={currentVideo}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="inline-flex items-center px-6 py-3 bg-white text-gray-900 rounded-xl hover:bg-gray-100 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl hover:scale-105"
                                                >
                                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                    </svg>
                                                    Watch on TikTok
                                                </Link>
                                            </div>
                                        </div>
                                    );
                                }
                                
                                return (
                                    <div className="relative aspect-video rounded-xl overflow-hidden shadow-lg bg-gray-100">
                                        <iframe
                                            src={embedUrl}
                                            className="absolute inset-0 w-full h-full"
                                            allowFullScreen
                                            title={`${product.name} video ${currentVideoIndex + 1}`}
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            onError={() => {
                                                console.error('Failed to load video:', embedUrl);
                                            }}
                                        />
                                    </div>
                                );
                            })()}
                            
                            {/* Video Navigation Buttons */}
                            {product.videos.length > 1 && (
                                <>
                                    <button
                                        onClick={() => setCurrentVideoIndex(prev => prev === 0 ? product.videos.length - 1 : prev - 1)}
                                        className="absolute left-3 top-1/2 -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110"
                                        aria-label="Previous video"
                                    >
                                        <svg className="w-5 h-5 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                                        </svg>
                                    </button>
                                    <button
                                        onClick={() => setCurrentVideoIndex(prev => prev === product.videos.length - 1 ? 0 : prev + 1)}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 bg-white bg-opacity-100 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110"
                                        aria-label="Next video"
                                    >
                                        <svg className="w-5 h-5 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </button>
                                </>
                            )}
                        </div>
                        
                        {/* Video Indicators */}
                        {product.videos.length > 1 && (
                            <div className="flex gap-3 justify-center mb-4">
                                {product.videos.map((video, index) => {
                                    const isActive = index === currentVideoIndex;
                                    const isTikTok = video.includes('tiktok.com');
                                    const isYouTube = video.includes('youtube.com') || video.includes('youtu.be');
                                    const isVimeo = video.includes('vimeo.com');
                                    
                                    return (
                                        <button
                                            key={index}
                                            onClick={() => setCurrentVideoIndex(index)}
                                            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                                                isActive 
                                                    ? 'bg-blue-500 text-white shadow-md' 
                                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                            }`}
                                        >
                                            {isTikTok && (
                                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43V7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.43z"/>
                                                </svg>
                                            )}
                                            {isYouTube && (
                                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                                </svg>
                                            )}
                                            {isVimeo && (
                                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.529 2.4 6.726 1.4 7.777 1.4c1.94.105 3.129 1.294 3.564 3.566.468 2.459.793 3.987.793 4.583 0 1.294-.468 3.566-.468 3.566s.793-1.294 2.338-3.566c1.545-2.272.793-3.566-.793-3.566-.468 0-.793.105-1.294.316 1.294-4.247 3.564-6.37 6.026-6.37 1.939 0 2.871 1.294 2.871 3.881z"/>
                                                </svg>
                                            )}
                                            <span className="text-sm font-medium">{index + 1}</span>
                                        </button>
                                    );
                                })}
                            </div>
                        )}
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4" style={{display: 'none'}}>
                            {product.videos.map((video, index) => {
                                const embedUrl = convertToEmbedUrl(video);
                                const isTikTok = video.includes('tiktok.com');
                                const isYouTube = video.includes('youtube.com') || video.includes('youtu.be');
                                const isVimeo = video.includes('vimeo.com');
                                
                                // For TikTok, show a custom card instead of trying to embed
                                if (isTikTok) {
                                    return (
                                        <div key={index} className="relative aspect-video rounded-lg overflow-hidden shadow-sm bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center">
                                            <div className="text-center text-white p-6">
                                                <div className="mb-4">
                                                    <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43V7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.43z"/>
                                                    </svg>
                                                    <p className="text-lg font-semibold">TikTok Video</p>
                                                    <p className="text-sm opacity-90">Click to watch on TikTok</p>
                                                </div>
                                                <Link
                                                    href={video}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="inline-flex items-center px-4 py-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors font-medium"
                                                >
                                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                    </svg>
                                                    Watch Video
                                                </Link>
                                            </div>
                                        </div>
                                    );
                                }
                                
                                // For other platforms, try to embed normally
                                return (
                                    <div key={index} className="relative aspect-video rounded-lg overflow-hidden shadow-sm bg-gray-100">
                                        <iframe
                                            src={embedUrl}
                                            className="absolute inset-0 w-full h-full"
                                            allowFullScreen
                                            title={`${product.name} video ${index + 1}`}
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            onError={() => {
                                                console.error('Failed to load video:', embedUrl);
                                            }}
                                        />
                                    </div>
                                );
                            })}
                        </div>
                        
                        {/* Info note about video platforms */}
                        <div className="mt-4 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                            <div className="flex items-start gap-2">
                                <svg className="w-4 h-4 mt-0.5 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p>YouTube and Vimeo videos embed directly. TikTok videos open on TikTok due to platform restrictions.</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Links Section */}
                {(product.links.length > 0 || product.website.length > 0) && (
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 sm:p-8">
                        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 sm:mb-6 flex items-center gap-2">
                            <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                            Related Links
                        </h3>
                        <div className="grid gap-3 sm:gap-4">
                            {product.website.map((site, index) => (
                                <Link
                                    key={`website-${index}`}
                                    href={site.startsWith('http') ? site : `https://${site}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="group flex items-center gap-3 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02] border border-gray-200"
                                >
                                    <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                                        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                        </svg>
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">Official Website</p>
                                        <p className="text-sm text-gray-500 truncate">{site}</p>
                                    </div>
                                    <svg className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                </Link>
                            ))}
                            {product.links.map((link, index) => (
                                <Link
                                    key={`link-${index}`}
                                    href={link.startsWith('http') ? link : `https://${link}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="group flex items-center gap-3 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02] border border-gray-200"
                                >
                                    <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                                        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                        </svg>
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900 group-hover:text-green-600 transition-colors">Related Link</p>
                                        <p className="text-sm text-gray-500 truncate">{link}</p>
                                    </div>
                                    <svg className="w-4 h-4 text-gray-400 group-hover:text-green-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                </Link>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* Image Modal */}
            {selectedImage && (
                <ImageModal
                    src={selectedImage}
                    alt={product.name}
                    onClose={() => setSelectedImage(null)}
                />
            )}
        </>
    );
};

export default ProductShowcase; 