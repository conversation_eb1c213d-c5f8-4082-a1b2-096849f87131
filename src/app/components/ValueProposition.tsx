"use client";

import React from "react";
import { Star, Shield, Users, TrendingUp } from "lucide-react";
import Link from "next/link";

const ValueProposition: React.FC = () => {
  return (
    <section className="w-full py-12 bg-gradient-to-br from-white via-blue-50/30 to-white">
      <div className="max-w-4xl mx-auto px-4 text-center">
        {/* Main Value Prop */}
        <div className="mb-8">
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-extrabold text-gray-800 mb-3">
            Your Voice, Your Choice
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
            Join thousands of Guyanese sharing honest reviews to help others make better choices
          </p>
        </div>

        {/* Key Benefits - Condensed */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-myTheme-primary/10 rounded-full flex items-center justify-center mb-2">
              <Star className="w-6 h-6 text-myTheme-primary" />
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700">Authentic Reviews</span>
          </div>

          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-myTheme-primary/10 rounded-full flex items-center justify-center mb-2">
              <Shield className="w-6 h-6 text-myTheme-primary" />
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700">Trusted Platform</span>
          </div>

          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-myTheme-primary/10 rounded-full flex items-center justify-center mb-2">
              <Users className="w-6 h-6 text-myTheme-primary" />
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700">Local Community</span>
          </div>

          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-myTheme-primary/10 rounded-full flex items-center justify-center mb-2">
              <TrendingUp className="w-6 h-6 text-myTheme-primary" />
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700">Business Growth</span>
          </div>
        </div>

        {/* Call to Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            href="/write-review"
            className="bg-myTheme-primary text-white px-6 py-3 rounded-full font-medium hover:bg-myTheme-secondary transition-colors inline-flex items-center gap-2"
          >
            <Star className="w-4 h-4" />
            <span className="font-medium text-sm sm:text-base">Write Your First Review</span>
          </Link>

          <Link
            href="/business"
            className="border border-myTheme-primary text-myTheme-primary px-6 py-3 rounded-full font-medium hover:bg-myTheme-primary/5 transition-colors inline-flex items-center gap-2"
          >
            <TrendingUp className="w-4 h-4" />
            <span className="font-medium text-sm sm:text-base">Grow Your Business</span>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ValueProposition;
