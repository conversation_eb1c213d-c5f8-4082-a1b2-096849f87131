import React, { useState, useEffect, useCallback } from "react";
import { genTags } from "../util/serverFunctions";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { iProduct } from "../util/Interfaces";

interface Props {
  description: string;
  handleArrayInput: (field: keyof iProduct, value: string) => void;
  handleRemoveArrayItem: (field: keyof iProduct, index: number) => void;
  field: keyof iProduct;
}

interface TagsResponse {
  success: boolean;
  data: {
    tags: string[];
  } | string[] | string | Record<string, unknown>;
  error?: string;
}

const SmartTags = ({ handleArrayInput, description, field }: Props) => {
  const [allAiTags, setAllAiTags] = useState<string[]>([]);
  const [displayedTags, setDisplayedTags] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isDescriptionValid, setIsDescriptionValid] = useState(false);

  useEffect(() => {
    const wordCount = description.trim().split(/\s+/).length;
    setIsDescriptionValid(wordCount >= 10);
  }, [description]);

  const updateDisplayedTags = useCallback(() => {
    setDisplayedTags(allAiTags.slice(0, 5));
  }, [allAiTags]);

  useEffect(() => {
    updateDisplayedTags();
  }, [updateDisplayedTags]);

  const onClickHandle = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await genTags(description) as TagsResponse;

      if (!response.success) {
        throw new Error(response.error || "Failed to generate tags");
      }

      if (!response.data) {
        throw new Error("No tags were generated");
      }

      // Extract tags from the nested structure
      let tagsArray: unknown[] = [];

      if (typeof response.data === 'object' && response.data !== null) {
        if ('tags' in response.data && Array.isArray(response.data.tags)) {
          tagsArray = response.data.tags;
        } else if (Array.isArray(response.data)) {
          tagsArray = response.data;
        } else {
          tagsArray = Object.values(response.data);
        }
      } else if (typeof response.data === 'string') {
        tagsArray = [response.data];
      }

      const validTags = tagsArray
        .filter((tag: unknown): tag is string => typeof tag === 'string' && tag.trim().length > 0)
        .map((tag: string) => tag.trim());

      if (validTags.length === 0) {
        throw new Error("No valid tags were generated");
      }

      setAllAiTags(validTags);
    } catch (error) {
      console.error("Error generating tags:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to generate tags. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleTagClick = (
    e: React.MouseEvent<HTMLButtonElement>,
    tag: string
  ) => {
    e.preventDefault();
    handleArrayInput(field, tag);
    setAllAiTags((prevTags) => {
      const newTags = prevTags.filter((t) => t !== tag);
      setDisplayedTags(newTags.slice(0, 5));
      return newTags;
    });
  };

  return (
    <div className="w-full">
      {loading ? (
        <div className="flex items-center justify-center p-3 sm:p-4">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <p className="text-xs sm:text-sm text-gray-600">Generating suggestions...</p>
          </div>
        </div>
      ) : error ? (
        <div className="flex flex-col sm:flex-row items-center justify-center p-3 sm:p-4 gap-2">
          <p className="text-xs sm:text-sm text-red-500 text-center">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={onClickHandle}
            className="bg-myTheme-secondary text-white hover:bg-myTheme-secondary/90 text-xs sm:text-sm"
          >
            Retry
          </Button>
        </div>
      ) : displayedTags.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-3 sm:p-4 gap-2 text-center">
          <p className="text-xs sm:text-sm text-gray-600">
            Having trouble thinking of categories? Let our AI help you!
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={onClickHandle}
            disabled={!isDescriptionValid}
            className="bg-myTheme-primary text-white hover:bg-myTheme-primary/90 disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm"
          >
            ✨ Generate AI Suggestions
          </Button>
          {!isDescriptionValid && (
            <p className="text-xs text-gray-500 text-center">
              Please enter at least 10 words in the description first.
            </p>
          )}
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex flex-wrap gap-1.5 sm:gap-2">
            {displayedTags.map((tag, index) => (
              <button
                key={index}
                type="button"
                className="group bg-white rounded-full px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm border border-blue-200 cursor-pointer hover:bg-blue-500 hover:text-white hover:border-blue-500 transition-all duration-200 ease-in-out flex items-center gap-1 shadow-sm hover:shadow-md"
                onClick={(e) => handleTagClick(e, tag)}
                aria-label={`Click to add ${tag} to your categories`}
              >
                <span className="text-blue-500 group-hover:text-white font-bold text-sm sm:text-base">+</span>
                <span>{tag}</span>
              </button>
            ))}
          </div>
          {allAiTags.length > displayedTags.length && (
            <p className="text-xs text-gray-500 text-center">
              {allAiTags.length - displayedTags.length} more suggestions available after adding these
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default SmartTags;
