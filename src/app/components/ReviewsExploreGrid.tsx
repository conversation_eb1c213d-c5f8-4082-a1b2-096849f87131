"use client";
import { iReview } from "../util/Interfaces";
import ReviewBox from "./ReviewBox";
import { useQuery } from "@tanstack/react-query";
import { getLatestReviews } from "../util/serverFunctions";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect, useMemo } from "react";
import {
  Clock,
  Flame,
  Star,
  ThumbsUp,
  TrendingUp,
  MapPin,
  Filter,
  Search,
  Calendar,
} from "lucide-react";
import { TopReviewsSkeleton } from "./skeletons";
import { motion, AnimatePresence } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import dayjs from "dayjs";

type FilterType = "latest" | "popular" | "trending" | "verified" | "local";
type TimeFilter = "all" | "today" | "week" | "month";
type CategoryFilter =
  | "all"
  | "restaurants"
  | "services"
  | "retail"
  | "automotive";

const ReviewsExploreGrid = () => {
  const [filter, setFilter] = useState<FilterType>("latest");
  const [timeFilter, setTimeFilter] = useState<TimeFilter>("all");
  const [categoryFilter, setCategoryFilter] = useState<CategoryFilter>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [reviewsPerPage, setReviewsPerPage] = useState(12);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ["exploreReviews", filter, timeFilter, categoryFilter],
    queryFn: async () => {
      const response = await getLatestReviews(filter);
      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch reviews");
      }
      return response.data;
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filter, timeFilter, categoryFilter, searchTerm, reviewsPerPage]);

  // Filter reviews based on search and filters
  const filteredReviews = useMemo(() => {
    if (!data) return [];
    
    return data.filter((review: iReview) => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          review.title.toLowerCase().includes(searchLower) ||
          review.body.toLowerCase().includes(searchLower) ||
          review.product?.name.toLowerCase().includes(searchLower) ||
          review.user?.userName.toLowerCase().includes(searchLower);

        if (!matchesSearch) return false;
      }

      // Category filter
      if (categoryFilter !== "all") {
        const productTags = review.product?.tags || [];
        const categoryMap = {
          restaurants: ["restaurant", "fast food", "cafe", "bar", "food"],
          services: ["service", "repair", "delivery", "taxi", "healthcare"],
          retail: ["store", "shop", "electronics", "clothing", "supermarket"],
          automotive: ["car rental", "auto repair", "gas station", "mechanic"],
        };

        const categoryTags = categoryMap[categoryFilter] || [];
        const hasMatchingTag = productTags.some((tag) =>
          categoryTags.some((catTag) =>
            tag.toLowerCase().includes(catTag.toLowerCase()),
          ),
        );

        if (!hasMatchingTag) return false;
      }

      // Time filter
      if (timeFilter !== "all") {
        const reviewDate = dayjs(review.createdDate);
        const now = dayjs();

        switch (timeFilter) {
          case "today":
            if (!reviewDate.isSame(now, "day")) return false;
            break;
          case "week":
            if (!reviewDate.isAfter(now.subtract(7, "day"))) return false;
            break;
          case "month":
            if (!reviewDate.isAfter(now.subtract(30, "day"))) return false;
            break;
        }
      }

      return true;
    });
  }, [data, timeFilter, categoryFilter, searchTerm]);

  const activeFiltersCount =
    (filter !== "latest" ? 1 : 0) +
    (timeFilter !== "all" ? 1 : 0) +
    (categoryFilter !== "all" ? 1 : 0) +
    (searchTerm ? 1 : 0);

  if (isError) {
    return (
      <div className="text-center p-8 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-lg font-medium text-red-800">
          Unable to load reviews
        </p>
        <p className="text-red-600 mb-4">
          {error instanceof Error ? error.message : "Unknown error"}
        </p>
        <Button onClick={() => refetch()} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (isLoading) return <TopReviewsSkeleton />;

  const FilterButton = ({
    type,
    icon,
    label,
    count,
    isActive,
  }: {
    type: FilterType;
    icon: React.ReactNode;
    label: string;
    count?: number;
    isActive: boolean;
  }) => (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={() => setFilter(type)}
      className={`flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
        isActive
          ? "bg-myTheme-primary text-white shadow-md"
          : "bg-white text-myTheme-accent hover:bg-myTheme-primary/10 hover:text-myTheme-primary border border-gray-200"
      }`}
    >
      {icon}
      <span>{label}</span>
      {count !== undefined && (
        <Badge
          variant="secondary"
          className={`ml-1 text-xs ${
            isActive ? "bg-white/20 text-white" : "bg-gray-100 text-gray-600"
          }`}
        >
          {count}
        </Badge>
      )}
    </motion.button>
  );

  return (
    <div className="w-full space-y-8">
      {/* Filter Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-6"
      >
        {/* Main Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-3">
          <FilterButton
            type="latest"
            icon={<Clock size={16} />}
            label="Latest"
            isActive={filter === "latest"}
          />
          <FilterButton
            type="popular"
            icon={<ThumbsUp size={16} />}
            label="Popular"
            isActive={filter === "popular"}
          />
          <FilterButton
            type="trending"
            icon={<TrendingUp size={16} />}
            label="Trending"
            isActive={filter === "trending"}
          />
          <FilterButton
            type="verified"
            icon={<Star size={16} />}
            label="Verified"
            isActive={filter === "verified"}
          />
          <FilterButton
            type="local"
            icon={<MapPin size={16} />}
            label="Local"
            isActive={filter === "local"}
          />
        </div>

        {/* Advanced Filters Toggle */}
        <div className="flex justify-center">
          <Button
            variant="ghost"
            onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            className="text-gray-600 hover:text-myTheme-primary"
          >
            <Filter size={16} className="mr-2" />
            {isFilterExpanded ? "Hide Filters" : "More Filters"}
          </Button>
        </div>

        {/* Expanded Filters */}
        <AnimatePresence>
          {isFilterExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4 bg-gray-50 p-6 rounded-xl"
            >
              {/* Search */}
              <div className="relative">
                <Search
                  size={20}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <Input
                  placeholder="Search reviews, products, or users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 py-3 rounded-xl border-gray-200 focus:border-myTheme-primary focus:ring-myTheme-primary/20"
                />
              </div>

              {/* Time and Category Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Time Period
                  </label>
                  <Select
                    value={timeFilter}
                    onValueChange={(value) => setTimeFilter(value as TimeFilter)}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Category
                  </label>
                  <Select
                    value={categoryFilter}
                    onValueChange={(value) =>
                      setCategoryFilter(value as CategoryFilter)
                    }
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="restaurants">Restaurants</SelectItem>
                      <SelectItem value="services">Services</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="automotive">Automotive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Active Filters */}
              {(searchTerm || timeFilter !== "all" || categoryFilter !== "all") && (
                <div className="flex flex-wrap gap-2">
                  <span className="text-sm text-gray-600">Active filters:</span>
                  {searchTerm && (
                    <Badge
                      variant="secondary"
                      className="bg-myTheme-primary text-white"
                    >
                      Search: {searchTerm}
                    </Badge>
                  )}
                  {timeFilter !== "all" && (
                    <Badge
                      variant="secondary"
                      className="bg-myTheme-primary text-white"
                    >
                      Time: {timeFilter}
                    </Badge>
                  )}
                  {categoryFilter !== "all" && (
                    <Badge
                      variant="secondary"
                      className="bg-myTheme-primary text-white"
                    >
                      Category: {categoryFilter}
                    </Badge>
                  )}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Results Summary */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="text-center text-gray-600"
      >
        Showing{" "}
        {filteredReviews.length > 0
          ? (currentPage - 1) * reviewsPerPage + 1
          : 0}
        –{Math.min(currentPage * reviewsPerPage, filteredReviews.length)} of{" "}
        {filteredReviews.length} reviews
      </motion.div>

      {/* Reviews Grid */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        {filteredReviews.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-16 bg-gray-50 rounded-2xl"
          >
            <div className="text-6xl mb-4">🔍</div>
            <p className="text-xl font-medium text-gray-800 mb-2">
              No reviews found
            </p>
            <p className="text-gray-600 mb-4">
              {searchTerm || activeFiltersCount > 0
                ? "Try adjusting your search or filters"
                : "Be the first to share your experience!"}
            </p>
            {(searchTerm || activeFiltersCount > 0) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setTimeFilter("all");
                  setCategoryFilter("all");
                  setFilter("latest");
                  setCurrentPage(1);
                }}
              >
                Clear All Filters
              </Button>
            )}
          </motion.div>
        ) : (
          <>
            <motion.div
              layout
              className="w-full columns-1 md:columns-2 xl:columns-3 gap-4 md:gap-6 lg:gap-8 space-y-4 md:space-y-6 lg:space-y-8"
            >
              <AnimatePresence mode="popLayout">
                {filteredReviews
                  .slice(
                    (currentPage - 1) * reviewsPerPage,
                    currentPage * reviewsPerPage,
                  )
                  .map((review: iReview, index: number) => (
                    <motion.div
                      key={review.id}
                      layout
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      transition={{ duration: 0.2, delay: index * 0.05 }}
                      className="break-inside-avoid mb-4 md:mb-6 lg:mb-8"
                    >
                      <ReviewBox review={review} />
                    </motion.div>
                  ))}
              </AnimatePresence>
            </motion.div>

            {/* Pagination Controls */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-8"
            >
              {/* Items per page selector */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>Show:</span>
                <Select
                  value={reviewsPerPage.toString()}
                  onValueChange={(value) => setReviewsPerPage(Number(value))}
                >
                  <SelectTrigger className="w-20 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="12">12</SelectItem>
                    <SelectItem value="24">24</SelectItem>
                    <SelectItem value="48">48</SelectItem>
                  </SelectContent>
                </Select>
                <span>per page</span>
              </div>

              {/* Pagination buttons */}
              {Math.ceil(filteredReviews.length / reviewsPerPage) > 1 && (
                <div className="flex justify-center items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-4 py-2"
                  >
                    Previous
                  </Button>

                  <div className="flex gap-1">
                    {Array.from({
                      length: Math.ceil(filteredReviews.length / reviewsPerPage),
                    })
                      .map((_, index) => index + 1)
                      .filter((page) => {
                        const totalPages = Math.ceil(
                          filteredReviews.length / reviewsPerPage,
                        );
                        if (totalPages <= 7) return true;
                        if (page <= 3) return true;
                        if (page >= totalPages - 2) return true;
                        if (Math.abs(page - currentPage) <= 1) return true;
                        return false;
                      })
                      .map((page, index, array) => {
                        const showEllipsis =
                          index > 0 && page - array[index - 1] > 1;
                        return (
                          <div key={page} className="flex items-center gap-1">
                            {showEllipsis && (
                              <span className="px-2 py-1 text-gray-500">...</span>
                            )}
                            <Button
                              variant={currentPage === page ? "default" : "outline"}
                              onClick={() => setCurrentPage(page)}
                              className={`px-3 py-2 min-w-[40px] ${
                                currentPage === page
                                  ? "bg-myTheme-primary text-white"
                                  : "hover:bg-gray-100"
                              }`}
                            >
                              {page}
                            </Button>
                          </div>
                        );
                      })}
                  </div>

                  <Button
                    variant="outline"
                    onClick={() =>
                      setCurrentPage((prev) =>
                        Math.min(
                          Math.ceil(filteredReviews.length / reviewsPerPage),
                          prev + 1,
                        ),
                      )
                    }
                    disabled={
                      currentPage ===
                      Math.ceil(filteredReviews.length / reviewsPerPage)
                    }
                    className="px-4 py-2"
                  >
                    Next
                  </Button>
                </div>
              )}
            </motion.div>

            {/* Real-time Stats */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-center gap-6 text-sm text-gray-500 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full border border-gray-200"
            >
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Live updates</span>
              </div>
              <div>Last updated: just now</div>
              <div>{filteredReviews.length} total reviews</div>
            </motion.div>
          </>
        )}
      </motion.div>
    </div>
  );
};

export default ReviewsExploreGrid;