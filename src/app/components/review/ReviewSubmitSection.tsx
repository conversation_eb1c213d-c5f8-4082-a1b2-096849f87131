import React from 'react';
import { Button } from '@/components/ui/button';

interface ReviewSubmitSectionProps {
    disabled: boolean;
    hasUnuploadedImages?: boolean;
    isFormValid?: boolean;
}

const ReviewSubmitSection: React.FC<ReviewSubmitSectionProps> = ({ 
    disabled, 
    hasUnuploadedImages = false,
    isFormValid = true
}) => {
    // Determine if the button should be disabled
    const isButtonDisabled = disabled || hasUnuploadedImages || !isFormValid;
    
    // Determine the button text based on state
    let buttonText = 'Submit Review';
    if (disabled) {
        buttonText = 'Submitting...';
    } else if (hasUnuploadedImages) {
        buttonText = 'Upload or Remove Images First';
    } else if (!isFormValid) {
        buttonText = 'Complete Required Fields';
    }
    
    return (
        <div className="flex justify-end pt-8 border-t border-gray-100">
            <Button
                type="submit"
                disabled={isButtonDisabled}
                className="bg-myTheme-primary hover:bg-myTheme-secondary text-white font-bold py-3 px-8 rounded-lg shadow-lg transform hover:scale-105 transition-transform duration-300 disabled:bg-gray-400"
            >
                {buttonText}
            </Button>
        </div>
    );
};

export default ReviewSubmitSection; 