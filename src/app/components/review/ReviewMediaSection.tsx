import React, { useState, useEffect } from 'react';
import MultiFileUpload from '../fileUpload/MultiFileUpload';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ReviewMediaSectionProps {
    handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    setLinksArray: React.Dispatch<React.SetStateAction<string[]>>;
    setAllUploaded: React.Dispatch<React.SetStateAction<boolean>>;
    allUploaded: boolean;
    setHasFilesQueued: React.Dispatch<React.SetStateAction<boolean>>;
}

const ReviewMediaSection: React.FC<ReviewMediaSectionProps> = ({
    handleChange,
    setLinksArray,
    setAllUploaded,
    allUploaded,
    setHasFilesQueued
}) => {
    const [hasFiles, setHasFiles] = useState(false);
    const [isUploading, setIsUploading] = useState(false);

    // This function will be passed to MultiFileUpload to track upload status
    const handleUploadStatusChange = (hasFiles: boolean, isUploading: boolean) => {
        setHasFiles(hasFiles);
        setIsUploading(isUploading);
        setHasFilesQueued(hasFiles); // Update parent component about files being queued
    };

    return (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
            <h2 className="text-xl font-semibold text-myTheme-primary mb-4 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                Media (Optional)
            </h2>
            <div className="space-y-6">
                <div>
                    <Label htmlFor="videoUrl" className="text-myTheme-primary font-medium">Video Link</Label>
                    <Input
                        id="videoUrl"
                        name="videoUrl"
                        type="url"
                        placeholder="https://youtube.com/watch?v=..."
                        onChange={handleChange}
                        className="mt-2 border-gray-200 focus:border-green-400 focus:ring-green-400/20"
                    />
                </div>
                <div>
                    <Label className="text-myTheme-primary font-medium">Add Photos</Label>
                    {hasFiles && !allUploaded && (
                        <Alert className="mt-2 mb-2 bg-amber-50 border-amber-200 text-amber-800">
                            <AlertDescription>
                                {isUploading 
                                    ? "Images are currently uploading. Please wait until upload completes."
                                    : "Please click the 'Upload Files' button to upload your images or remove them. The submit button will be disabled until all images are uploaded or removed."}
                            </AlertDescription>
                        </Alert>
                    )}
                    <div className="mt-2">
                        <MultiFileUpload
                            setLinksArray={setLinksArray}
                            setAllUploaded={setAllUploaded}
                            allUploaded={allUploaded}
                            onStatusChange={handleUploadStatusChange}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ReviewMediaSection;