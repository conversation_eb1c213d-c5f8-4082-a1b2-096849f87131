'use client'
import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { cn } from "@/lib/utils";
import { iProduct } from '@/app/util/Interfaces'
import BusinessStatusBadge from "./BusinessStatusBadge";
import { MapPin, Phone, Globe, Clock, Tag, Eye, MoreHorizontal, MessageSquare, Search } from 'lucide-react'
import { formatProductAddress, hasAddressInfo } from '@/app/util/addressUtils'
import { calculateWeightedRating } from '@/app/util/calculateWeightedRating'
import RatingDisplayWithThreshold from './RatingDisplayWithThreshold'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ShareButtonWrapper } from './ShareButtonWrapper'
import { generateShareMetadata } from '../lib/shareUtils'
import { MdEdit, MdVerified } from 'react-icons/md'
import { ProductReportButton } from './ProductReportButton'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// NOTE: This component has been updated to use RatingDisplayWithThreshold for consistent
// rating display rules across the application. The old manual star rating display
// has been deprecated in favor of the centralized rating display component.

interface GrandProductCardProps {
    productId: string
    productData: iProduct | null
    currentUserId?: string | null
    showWriteReview?: boolean
}

export default function GrandProductCard({
    productId,
    productData,
    currentUserId = null,
    showWriteReview = true
}: GrandProductCardProps) {
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    if (!productData) {
        return (
            <div className="bg-white p-8 rounded-lg shadow-lg mb-8 text-center">
                <h2 className="text-2xl font-semibold text-gray-700">Product information is currently unavailable.</h2>
                <p className="text-gray-500">We couldn&apos;t load the details for product ID: {productId}</p>
            </div>
        );
    }

    const {
        name,
        description,
        display_image,
        images,
        rating,
        address,
        telephone,
        website,
        openingHrs,
        closingHrs,
        openingDays,
        tags,
        _count
    } = productData;

    const totalReviews = _count?.reviews || 0;

    // Generate metadata for sharing
    const metadata = generateShareMetadata({
        title: `${name} | ${totalReviews} Reviews | Rating: ${rating?.toFixed(1) || '0.0'}/5`,
        description: description || `Check out reviews for ${name} on Review It. ${totalReviews} reviews with an average rating of ${rating?.toFixed(1) || '0.0'}/5.`,
        imageUrl: display_image,
        url: `/product/${productId}`,
        rating: rating || 0,
        reviewCount: totalReviews,
    });

    return (
        <div className="relative w-full bg-white/80 backdrop-blur-sm p-6 md:p-8 rounded-2xl border border-gray-200/60 shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 mb-10 overflow-hidden">
            {/* Faint background image overlay */}
            {display_image && (
                <div className="absolute inset-0 opacity-[0.10] pointer-events-none">
                    <Image
                        src={display_image}
                        alt=""
                        layout="fill"
                        objectFit="cover"
                        className="blur-sm scale-110"
                    />
                </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
                {/* Image Section */}
                <div className="md:col-span-1 flex justify-center items-start">
                    {display_image ? (
                        <Image
                            src={display_image}
                            alt={name || 'Product image'}
                            width={300}
                            height={300}
                            className="rounded-lg object-cover w-full h-auto max-h-80 shadow-md"
                        />
                    ) : (
                        <div className="w-full h-60 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500">
                            No Image Available
                        </div>
                    )}
                </div>

                {/* Details Section */}
                <div className="md:col-span-2">
                    <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">{name}</h1>

                    {/* Rating Section with Minimum Review Validation */}
                    <div className="mb-4">
                        <RatingDisplayWithThreshold
                            ratingData={calculateWeightedRating(productData.reviews || [], {
                                globalAverageRating: rating
                            })}
                            size="lg"
                            showReviewCount={true}
                            showConfidence={false}
                            minimumReviewsMessage="Not enough reviews for rating yet"
                            className="text-lg"
                        />
                    </div>

                    {/* Business Status Badge */}
                    <BusinessStatusBadge product={productData} className="mb-4" showMessage={true} />

                    {description && <p className="text-gray-600 mb-5 text-md leading-relaxed">{description}</p>}

                    <div className="space-y-3 text-gray-700">
                        {hasAddressInfo(productData) && (
                            <div className="flex items-center group">
                                <MapPin className="h-5 w-5 mr-2 text-myTheme-primary group-hover:text-myTheme-accent transition-colors" />
                                <span className="group-hover:text-gray-900 transition-colors">{formatProductAddress(productData)}</span>
                            </div>
                        )}
                        {telephone && (
                            <div className="flex items-center group">
                                <Phone className="h-5 w-5 mr-2 text-myTheme-primary group-hover:text-myTheme-accent transition-colors" />
                                <span className="group-hover:text-gray-900 transition-colors">{telephone}</span>
                            </div>
                        )}
                        {website && website.length > 0 && (
                            <div className="flex items-center group">
                                <Globe className="h-5 w-5 mr-2 text-myTheme-primary group-hover:text-myTheme-accent transition-colors" />
                                <a href={website[0]} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:text-blue-600 hover:underline transition-colors">
                                    {website[0]}
                                </a>
                            </div>
                        )}
                        {openingHrs && closingHrs && openingDays && openingDays.length > 0 && (
                            <div className="flex items-center group">
                                <Clock className="h-5 w-5 mr-2 text-myTheme-primary group-hover:text-myTheme-accent transition-colors" />
                                <span className="group-hover:text-gray-900 transition-colors">Open: {openingDays.join(', ')} from {openingHrs} - {closingHrs}</span>
                            </div>
                        )}
                    </div>

                    {tags && tags.length > 0 && (
                        <div className="mt-6">
                            <h3 className="text-sm font-semibold text-gray-500 mb-2 flex items-center">
                                <Tag className="h-4 w-4 mr-1.5" /> Tags
                            </h3>
                            <div className="flex flex-wrap gap-2">
                                {tags.map(tag => (
                                    <span key={tag} className="px-3 py-1 text-xs bg-gray-100/80 backdrop-blur-sm text-gray-700 rounded-full shadow-sm hover:bg-gray-200/80 hover:shadow-md transition-all duration-200 cursor-default">
                                        {tag}
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Action Buttons Section */}
            <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="flex flex-wrap gap-3 justify-between items-center">
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">
                            {totalReviews} {totalReviews === 1 ? 'review' : 'reviews'}
                        </span>
                        {productData.hasOwner && productData.business?.isVerified === true && (
                            <span className="flex items-center text-sm text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                                <MdVerified className="mr-1" size={14} />
                                Verified Business
                            </span>
                        )}
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 w-full sm:w-auto mt-3 sm:mt-0">
                        <div className="w-full">
                            <Link href={`/product/${productId}`} className="w-full">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-full flex items-center justify-center gap-1"
                                >
                                    <Eye size={16} />
                                    <span className="hidden sm:inline">View Business</span>
                                    <span className="sm:hidden">View</span>
                                </Button>
                            </Link>
                        </div>

                        {mounted && showWriteReview && (
                            <div className="w-full">
                                <Link href={`/cr/?id=${productId}&rating=3`} className="w-full">
                                    <Button
                                        variant="default"
                                        size="sm"
                                        className="w-full flex items-center justify-center gap-1 bg-black hover:bg-gray-800 text-white"
                                    >
                                        <MdEdit size={16} />
                                        <span>Review</span>
                                    </Button>
                                </Link>
                            </div>
                        )}

                        <div className="w-full">
                            <ShareButtonWrapper metadata={metadata} className="w-full" />
                        </div>

                        <div className="w-full">
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full flex items-center justify-center gap-1"
                                    >
                                        <MoreHorizontal size={16} />
                                        <span>More</span>
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-48">
                                    <DropdownMenuItem>
                                        <Link
                                            href={`/reviews?id=${productId}`}
                                            className="flex items-center gap-2 w-full"
                                        >
                                            <MessageSquare size={16} />
                                            View All Reviews
                                        </Link>
                                    </DropdownMenuItem>
                                    {productData.latitude && productData.longitude && (
                                        <DropdownMenuItem>
                                            <Link
                                                href={`/location?id=${productId}`}
                                                className="flex items-center gap-2 w-full"
                                            >
                                                <MapPin size={16} />
                                                View on Map
                                            </Link>
                                        </DropdownMenuItem>
                                    )}
                                    {productData.tags && productData.tags.length > 0 && (
                                        <DropdownMenuItem>
                                            <Link
                                                href={`/browse?tags=${encodeURIComponent(productData.tags.slice(0, 2).join(","))}`}
                                                className="flex items-center gap-2 w-full"
                                            >
                                                <Search size={16} />
                                                View Similar Products
                                            </Link>
                                        </DropdownMenuItem>
                                    )}
                                    <DropdownMenuItem asChild>
                                        <ProductReportButton
                                            productId={productId}
                                            productName={name || ''}
                                            variant="ghost"
                                            size="sm"
                                            showText={true}
                                            className="w-full justify-start"
                                        />
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </div>
            </div>

            {/* Additional Images Gallery (Optional) */}
            {/* {images && images.length > 1 && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                    <h3 className="text-xl font-semibold text-gray-700 mb-4">More Images</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        {images.slice(1, 6).map((img, index) => (
                            <div key={index} className="aspect-square relative rounded-lg overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                <Image
                                    src={img}
                                    alt={`${name || 'Product'} image ${index + 1}`}
                                    layout="fill"
                                    objectFit="cover"
                                />
                            </div>
                        ))}
                    </div>
                </div>
            ) */}
        </div>
    )
}