"use client";

import React, { PropsWithChildren } from "react";
import { useKeenSlider } from "keen-slider/react";
import "keen-slider/keen-slider.min.css";

interface CarouselWrapperProps {
  slidesPerView?: number;
  spacing?: number;
  loop?: boolean;
  className?: string;
}

const CarouselWrapper: React.FC<PropsWithChildren<CarouselWrapperProps>> = ({
  slidesPerView = 1.1,
  spacing = 16,
  loop = false,
  className = "",
  children,
}) => {
  const [sliderRef] = useKeenSlider<HTMLDivElement>({
    loop,
    mode: "snap",
    slides: {
      perView: slidesPerView,
      spacing,
    },
    breakpoints: {
      "(min-width: 640px)": {
        disabled: true, // Disable slider on ≥ sm screens so grid layout takes over
      },
    },
  });

  return (
    <div ref={sliderRef} className={`keen-slider ${className}`}>
      {React.Children.map(children, (child, idx) => (
        <div className="keen-slider__slide" key={idx}>
          {child}
        </div>
      ))}
    </div>
  );
};

export default CarouselWrapper;