import dynamic from 'next/dynamic';
import { Suspense } from 'react';
import { Metadata } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

// Import critical components normally
import HeroSection from "./components/HeroSection";
import LiveFeedStrip from "./components/LiveFeedStrip";

// Lazy load non-critical components
const QuickTabs = dynamic(() => import("./components/QuickTabs"), {
  loading: () => <div className="h-64 bg-gray-100 animate-pulse rounded-lg"></div>
});

const EnhancedTopReviews = dynamic(() => import("./components/EnhancedTopReviews"), {
  loading: () => <div className="h-96 bg-gray-100 animate-pulse rounded-lg"></div>
});

const ReviewCategories = dynamic(() => import("./components/CompanyCategories"), {
  loading: () => <div className="h-64 bg-gray-100 animate-pulse rounded-lg"></div>
});

const ValueProposition = dynamic(() => import("./components/ValueProposition"), {
  loading: () => <div className="h-48 bg-gray-100 animate-pulse rounded-lg"></div>
});

const TopReviewers = dynamic(() => import("./components/TopReviewers"), {
  loading: () => <div className="h-64 bg-gray-100 animate-pulse rounded-lg"></div>
});

const AdvertCard = dynamic(() => import("./components/advert-components/AdvertCard"), {
  loading: () => <div className="h-48 bg-gray-100 animate-pulse rounded-lg"></div>
});

// Override any noindex directives with explicit indexing instructions
export const metadata: Metadata = {
  title: "Discover and Share Honest Reviews - ReviewIt",
  description: "Find trusted reviews for businesses, products, and services in Guyana. Share your experiences and help others make informed decisions.",
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
    },
  },
};

export default function Home() {
  return (
    <div className="flex h-full w-full flex-col justify-start bg-gradient-to-b from-gray-50 via-white to-gray-50">
      {/* Hero Section */}
      <section className="relative z-[50]">
        <HeroSection />
      </section>

      {/* Live Feed – recent products */}
      <section className="py-6 md:py-8">
        <LiveFeedStrip />
      </section>

      {/* Quick Categories */}
      <section className="py-12 md:py-16 relative z-[1] bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Suspense fallback={<div className="h-64 bg-gray-100 animate-pulse rounded-lg"></div>}>
            <QuickTabs />
          </Suspense>
        </div>
      </section>

      {/* Recent Reviews Section */}
      <section className="py-12 md:py-16 relative z-[4]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Suspense fallback={<div className="h-96 bg-gray-100 animate-pulse rounded-lg"></div>}>
            <EnhancedTopReviews />
          </Suspense>
        </div>
      </section>


      {/* Additional Content Sections */}
      <section className="py-12 md:py-16 relative z-[4]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-16 md:space-y-20">
          {/* Top Reviewers Section with Advert */}
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Top Reviewers - Always visible */}
            <div className="flex-1 lg:w-1/2 bg-slate-50 rounded-xl shadow-inner p-2 md:p-4">
              <Suspense fallback={<div className="h-64 bg-gray-100 animate-pulse rounded-lg"></div>}>
                <TopReviewers />
              </Suspense>
            </div>
            
            {/* Adverts - Only visible on PC (lg and above) */}
            <div className="hidden lg:block lg:w-1/2">
              <div className="sticky top-8 space-y-6">
                <AdvertCard
                  title="Discover Local Businesses"
                  description="Find the best restaurants, shops, and services in your area. Join thousands of satisfied customers!"
                  imageUrl="/your-brand-here.jpg"
                  link="/browse"
                  isSponsored={true}
                />
                <AdvertCard
                  title="Featured Services"
                  description="Explore top-rated services and products recommended by our community members."
                  imageUrl="/your-brand-here.jpg"
                  link="/featured"
                  isSponsored={true}
                />
              </div>
            </div>
          </div>
          
          {/* Review Categories */}
          <div>
            <Suspense fallback={<div className="h-64 bg-gray-100 animate-pulse rounded-lg"></div>}>
              <ReviewCategories />
            </Suspense>
          </div>
          
          {/* Value Proposition */}
          <div>
            <Suspense fallback={<div className="h-48 bg-gray-100 animate-pulse rounded-lg"></div>}>
              <ValueProposition />
            </Suspense>
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      {/* <section className="bg-gradient-to-r from-myTheme-primary to-myTheme-secondary py-16 md:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to share your experience?
          </h2>
          <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
            Join thousands of Guyanese helping others make informed decisions
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/write-review">
              <Button className="bg-white text-myTheme-primary hover:bg-gray-100 font-semibold px-8 py-3 rounded-lg transition-all duration-200 hover:scale-105">
                Write a Review
              </Button>
            </Link>
            <Link href="/browse">
              <Button variant="outline" className="border-2 border-white text-gray-100 hover:bg-white hover:text-myTheme-primary font-semibold px-8 py-3 rounded-lg transition-all duration-200">
                Browse Reviews
              </Button>
            </Link>
          </div>
        </div>
      </section> */}
    </div>
  );
}
