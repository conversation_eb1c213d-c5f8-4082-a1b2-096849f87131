// Image processing worker for off-main-thread resizing
export interface ResizeWorkerMessage {
  id: string;
  type: 'resize';
  file: ArrayBuffer;
  fileName: string;
  fileType: string;
  options: {
    maxWidth?: number;
    maxHeight?: number;
    maxDimension?: number;
    quality?: number;
    maxFileSize?: number;
  };
}

export interface ResizeWorkerResponse {
  id: string;
  type: 'success' | 'error';
  dataUrl?: string;
  resized?: boolean;
  error?: string;
}

// Calculate new dimensions while preserving aspect ratio
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  options: ResizeWorkerMessage['options']
): { width: number; height: number } {
  const { maxWidth, maxHeight, maxDimension } = options;

  let targetWidth = originalWidth;
  let targetHeight = originalHeight;

  if (maxDimension) {
    const maxSize = Math.max(originalWidth, originalHeight);
    if (maxSize > maxDimension) {
      const scale = maxDimension / maxSize;
      targetWidth = Math.round(originalWidth * scale);
      targetHeight = Math.round(originalHeight * scale);
    }
  } else {
    if (maxWidth && targetWidth > maxWidth) {
      const scale = maxWidth / targetWidth;
      targetWidth = maxWidth;
      targetHeight = Math.round(targetHeight * scale);
    }
    if (maxHeight && targetHeight > maxHeight) {
      const scale = maxHeight / targetHeight;
      targetHeight = maxHeight;
      targetWidth = Math.round(targetWidth * scale);
    }
  }

  return { width: targetWidth, height: targetHeight };
}

// Binary search for optimal quality to meet file size requirements
async function findOptimalQuality(
  canvas: OffscreenCanvas,
  fileType: string,
  maxFileSize: number,
  minQuality = 0.3,
  maxQuality = 1.0
): Promise<{ dataUrl: string; quality: number }> {
  let low = minQuality;
  let high = maxQuality;
  let bestResult = { dataUrl: '', quality: maxQuality };

  while (high - low > 0.05) {
    const mid = (low + high) / 2;
    const blob = await canvas.convertToBlob({ type: fileType, quality: mid });
    
    if (blob.size <= maxFileSize) {
      // This quality works, try higher
      const reader = new FileReader();
      const dataUrl = await new Promise<string>((resolve) => {
        reader.onload = () => resolve(reader.result as string);
        reader.readAsDataURL(blob);
      });
      
      bestResult = { dataUrl, quality: mid };
      low = mid;
    } else {
      // Quality too high, try lower
      high = mid;
    }
  }

  return bestResult;
}

// Process image in worker
async function processImage(message: ResizeWorkerMessage): Promise<ResizeWorkerResponse> {
  try {
    const { id, file, fileName, fileType, options } = message;
    
    // Create ImageBitmap from ArrayBuffer
    const blob = new Blob([file], { type: fileType });
    const imageBitmap = await createImageBitmap(blob);
    
    const { width: newWidth, height: newHeight } = calculateDimensions(
      imageBitmap.width,
      imageBitmap.height,
      options
    );
    
    const shouldResize = newWidth !== imageBitmap.width || newHeight !== imageBitmap.height;
    
    // Create OffscreenCanvas
    const canvas = new OffscreenCanvas(newWidth, newHeight);
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    // Configure canvas for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // Draw image
    ctx.drawImage(imageBitmap, 0, 0, newWidth, newHeight);
    
    // Clean up ImageBitmap
    imageBitmap.close();
    
    const maxFileSize = options.maxFileSize;
    const initialQuality = options.quality ?? 0.85;
    
    let dataUrl: string;
    let finalQuality = initialQuality;
    let resized = shouldResize;
    
    if (maxFileSize) {
      // Use binary search to find optimal quality
      const result = await findOptimalQuality(canvas, fileType, maxFileSize, 0.3, initialQuality);
      dataUrl = result.dataUrl;
      finalQuality = result.quality;
      resized = resized || finalQuality < initialQuality;
    } else {
      // No file size limit, use initial quality
      const blob = await canvas.convertToBlob({ type: fileType, quality: initialQuality });
      const reader = new FileReader();
      dataUrl = await new Promise<string>((resolve) => {
        reader.onload = () => resolve(reader.result as string);
        reader.readAsDataURL(blob);
      });
    }
    
    return {
      id,
      type: 'success',
      dataUrl,
      resized,
    };
    
  } catch (error) {
    return {
      id: message.id,
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

// Worker message handler
self.onmessage = async (event: MessageEvent<ResizeWorkerMessage>) => {
  const response = await processImage(event.data);
  self.postMessage(response);
};
