// notificationCategory.ts
// Centralised mapping from backend-provided `notification_type` → category string used by
// mark-as-read endpoint and other front-end logic.
// Keep this in sync with the backend spec. Add new entries as new notification types emerge.

export const NOTIFICATION_TYPE_MAPPING = {
  // User notifications
  reply_review: 'user',
  reply_comment: 'user',

  // Owner notifications
  owner_review: 'owner',

  // Like notifications
  like_comment: 'like',
  like_review: 'like',

  // System notifications
  system: 'system',
} as const;

export type NotificationCategory = 'user' | 'owner' | 'like' | 'system';

export function getNotificationCategory(ntype: string): NotificationCategory {
  return (NOTIFICATION_TYPE_MAPPING as Record<string, NotificationCategory>)[ntype] || 'user';
}
