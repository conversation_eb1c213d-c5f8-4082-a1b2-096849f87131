/**
 * Utility functions for rating calculations and display
 */

/**
 * Rounds a rating to the nearest half-star (0.5 increment)
 * 
 * Examples:
 * - 4.1 → 4.0
 * - 4.3 → 4.5  
 * - 4.7 → 4.5
 * - 4.8 → 5.0
 * 
 * @param rating - The decimal rating to round (0-5)
 * @returns The rating rounded to nearest 0.5, clamped between 0 and 5
 */
export function roundToHalfStar(rating: number): number {
  // Handle invalid inputs
  if (typeof rating !== 'number' || isNaN(rating)) {
    return 0;
  }
  
  // Clamp rating between 0 and 5
  const clampedRating = Math.min(Math.max(rating, 0), 5);
  
  // Round to nearest 0.5
  return Math.round(clampedRating * 2) / 2;
}

/**
 * Determines if a rating should display a half star
 * 
 * @param rating - The decimal rating to check
 * @returns True if the rating should show a half star
 */
export function hasHalfStar(rating: number): boolean {
  const rounded = roundToHalfStar(rating);
  return rounded % 1 === 0.5;
}

/**
 * Gets the number of full stars to display
 * 
 * @param rating - The decimal rating
 * @returns Number of full stars (0-5)
 */
export function getFullStars(rating: number): number {
  const rounded = roundToHalfStar(rating);
  return Math.floor(rounded);
}

/**
 * Gets the number of empty stars to display
 * 
 * @param rating - The decimal rating
 * @returns Number of empty stars (0-5)
 */
export function getEmptyStars(rating: number): number {
  const fullStars = getFullStars(rating);
  const halfStar = hasHalfStar(rating) ? 1 : 0;
  return 5 - fullStars - halfStar;
}

/**
 * Formats a rating for display with appropriate decimal places
 * 
 * @param rating - The decimal rating
 * @param showDecimals - Whether to show decimal places for half stars
 * @returns Formatted rating string
 */
export function formatRatingDisplay(rating: number, showDecimals: boolean = true): string {
  const rounded = roundToHalfStar(rating);
  
  if (!showDecimals) {
    return rounded.toString();
  }
  
  // Show one decimal place for half stars, no decimals for whole numbers
  return rounded % 1 === 0 ? rounded.toString() : rounded.toFixed(1);
}