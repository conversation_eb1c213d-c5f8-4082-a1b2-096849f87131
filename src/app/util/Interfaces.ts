import { WeightedRatingResult } from './calculateWeightedRating';
import { BusinessHours } from '../types/businessHours';

export interface ReviewSummaryData {
  percentage5Stars: number;
  percentage4Stars: number;
  percentage3Stars: number;
  percentage2Stars: number;
  percentage1Star: number;
  totalCount: number;
  counts: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export interface iNotification {
  id: string;
  receiver_id: string;
  business_id: string;
  review_title: string;
  created_at?: Date;
  from_name: string;
  from_id: string;
  read: boolean;
  product_name: string;
  product_id: string;
  comment_id: string;
  review_id: string;
}

export interface iProductOwnerNotification {
  // Unified schema additions
  target_type?: 'review' | 'comment';
  target_url?: string;
  id: string;
  owner_id: string;
  business_id: string;
  review_title: string;
  created_at?: Date;
  from_name: string;
  from_id: string;
  read: boolean;
  product_id: string;
  product_name: string;
  review_id: string;
  comment_id: string | null;
  notification_type: string;
}

export interface iUserNotification {
  // Unified schema additions
  target_type?: 'review' | 'comment';
  target_url?: string;
  id: string;
  user_id?: string;
  content: string;
  read: boolean;
  notification_type: string;
  comment_id: string;
  review_id: string;
  from_name: string;
  from_id: string;
  created_at?: Date;
  parent_id: string;
  parent_user_id?: string; // Added to match backend expectation
  product_id?: string;
  // Additional fields for grouped notifications
  additional_users?: Array<{
    name: string;
    id: string;
  }>;
  total_count?: number; // Total number of users who performed the action
  action_type?: 'like' | 'dislike' | 'helpful'; // Type of action for vote notifications
}

export interface LikeNotification {
  // Unified schema additions
  notification_type?: string;
  target_type: 'comment' | 'review';
  target_url?: string;
  id?: string;
  target_user_id?: string; // filled by backend
  target_id: string; // ID of comment or review
  from_id: string;   // user who liked
  from_name: string; // human name
  product_id?: string;
  review_id?: string;
  comment_id?: string;
  read: boolean;
  created_at?: string | Date;
}

export interface SystemNotification {
  id: string;
  title: string;
  message: string;
  icon?: string;
  cta_url?: string;
  created_at?: Date | string;
  read: boolean;
}

export interface FetchedNotificationsData {
  userNotifications: iUserNotification[];
  ownerNotifications: iProductOwnerNotification[];
  likeNotifications: LikeNotification[];
  systemNotifications?: SystemNotification[];
}

export interface ReviewLike {
  reviewId: string;
  userId: string;

  review: iReview;
  user: iUser;
}

export interface iCalculatedRating {
  roundedRating: number;
  roundedRatingOneDecimalPlace: string;
  numberOfReviews: number;
  // New fields for weighted rating
  hasMinimumReviews?: boolean;
  confidence?: 'low' | 'medium' | 'high';
  sortingScore?: number;
  displayRating?: number;
}

export interface iPromotion {
  id?: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  discountPercentage?: number;
  discountAmount?: number;
  promotionCode?: string;
  isActive: boolean;
  productId: string;
  product?: iProduct;
  businessId: string;
  business?: iBusiness;
  createdAt: Date;
  updatedAt?: Date;
  image?: string;
  viewCount?: number;
  clickCount?: number;
  conversionCount?: number;
}

export interface TopReviewer {
  userId: string;
  reviewCount: number;
  user: {
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
  };
}

export interface TopReviewersResponse {
  success: boolean;
  status: number;
  data: TopReviewer[];
}

export interface iProduct {
  id?: string;
  address?: string | null;       // Keep for backward compatibility
  streetAddress?: string | null; // NEW: Street address only ("123 Main Street")
  city?: string | null;          // NEW: City/Town/Area ("Linden", "Georgetown", "Bartica")
  latitude?: number | null;
  longitude?: number | null;
  createdDate: Date;
  description: string;
  display_image: string;
  images: string[];
  videos: string[];
  links: string[];
  name: string;
  tags: string[];
  openingHrs?: string | null;
  closingHrs?: string | null;
  openingDays?: string[];
  businessHours?: BusinessHours | null;
  telephone?: string | null;
  website: string[];
  rating: number;
  hasOwner: boolean | null;
  ownerId: string | null;
  createdById: string;
  isDeleted: boolean;
  isPublic?: boolean;
  email?: string | null;
  businessId?: string | null;
  updatedAt?: Date | null;
  viewCount?: number;
  rating5Stars?: number;
  rating4Stars?: number;
  rating3Stars?: number;
  rating2Stars?: number;
  rating1Star?: number;
  featuredPosition?: number | null;
  business?: iBusiness | null;
  createdBy?: iUser | null;
  reviews?: iReview[];
  promotions?: iPromotion[];
  relatedProducts?: iProduct[];
  _count?: {
    reviews?: number;
    promotions?: number;
  };
  analytics?: iProductAnalytics;
  viewEvents?: iProductViewEvent[];
  userInteractions?: iUserProductInteraction[];
  claims?: iProductClaim[];
  weightedRating?: WeightedRatingResult;
  sector?: string;
  sub_sector?: string;
  industry?: string;

}

export interface iVoteCount {
  id: string;
  reviewId: string;
  review: iReview;
  helpfulVotes: number;
  unhelpfulVotes: number;
}

// Minimal user info for review context
export interface iReviewUser {
  id: string;
  firstName: string;
  lastName: string;
  avatar: string | null;
  userName: string;
  clerkUserId?: string; // Add clerkUserId for permission checks
}

export interface iReview {
  id?: string;
  body: string;
  createdDate?: Date;
  rating: number;
  title: string;
  productId: string;
  userId: string;
  isVerified?: boolean | null;
  verifiedBy?: string | null;
  createdBy?: string | null;
  isPublic: boolean;
  images: string[];
  videos: string[];
  links: string[];
  isDeleted?: boolean | null;
  verifiedAt?: Date | null;
  moderationHistory?: iModerationEvent[];
  product?: iProduct | null;
  user?: iReviewUser | null;
  comments?: iComment[];
  voteCount?: iVoteCount | null;
  likedBy?: iUser[];
  reports?: iReviewReport[];
}

export interface iComment {
  id?: string;
  body: string;
  createdDate: Date;
  review?: iReview;
  user?: iUser;
  reviewId: string;
  userId: string;
  isDeleted: boolean | false;
  parentId?: string | null;
  replies?: iComment[];
  parent?: iComment;
  parentUserId?: string;
  upvotes: number;
  downvotes: number;
  votes?: iCommentVote[];
}

export interface iCommentVote {
  id: string;
  commentId: string;
  userId: string;
  clerkUserId: string;
  voteType: "UP" | "DOWN";
  createdAt: Date;
  user?: iUser;
}

export interface iUser {
  id: string;
  bio?: string | null;
  userName: string;
  avatar: string | null;
  createdDate: Date;
  email: string;
  firstName: string;
  lastName: string;
  clerkUserId: string;
  isDeleted: boolean | null;
  role?: "USER" | "ADMIN";
  status?: "ACTIVE" | "SUSPENDED" | "BANNED";
  lastLoginAt?: Date;
  loginCount?: number;
  suspendedUntil?: Date;
  suspendedReason?: string;
  reviews?: iReview[];
  product?: iProduct[];
  createdBy?: iUser | null;
  createdById?: string;
  comments?: iComment[];
  likedReviews?: iReview[];
  businesses?: iBusiness[];
  _count?: {
    reviews?: number;
    comments?: number;
    product?: number;
  };
  adminActions?: iAdminAction[];
  moderationEvents?: iModerationEvent[];
  commentVotes?: iCommentVote[];
  productClaims?: iProductClaim[];
  reviewedClaims?: iProductClaim[];
  // Notification settings
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  // Privacy settings
  profileVisibility?: "public" | "private" | "friends";
  showEmail?: boolean;
  usernameNeedsChange?: boolean;
}

export interface iBusiness {
  id: string;
  owner: {
    id: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
  };
  ownerId: string;
  businessDescription?: string | null;
  subscriptionStatus: string;
  subscriptionExpiry: Date | null;
  products?: iProduct[];
  createdDate: Date | null;
  isVerified: boolean | null;
  ownerName?: string | null;
}

export interface iService {
  id?: string;
  name: string;
  description: string;
  images?: string[];
  createdDate?: Date;
  address?: string;
}

export interface iImage {
  id?: string;
  url: string;
}

export interface SentDataReviewAndProduct {
  userId: string; // identifier for the user who wrote the review
  rating: number; // a number between 1 and 5 indicating the rating for the product
  title: string; // the title of the review
  body: string; // the main text of the review


  comments: string[]; // an array of comments on the review
  createdDate?: Date;
  images?: string[]; // an array of images on the review
  confirmed?: boolean; // a boolean indicating whether the review has been confirmed
  deleted?: boolean; // a boolean indicating whether the review has been deleted
  deletedDate?: Date; // the date the review was deleted
  deletedBy?: string; // the user who deleted the review
  deletedReason?: string; // the reason the review was deleted
  productId?: string;
  links?: string[];
  videos?: string[];
  publicMetadata?: { userInDb: boolean; id: string };
  product: {
    display_image: string;
    productSelected: boolean;
    productId?: string;
    name: string;
    description: string;
    images?: string[];
    createdDate?: Date;
    address?: string;
    tags?: string[];
    openingHrs?: string;
    closingHrs?: string;
    telephone?: string;
    website?: string[];
    hasOwner?: boolean;
    owner?: string;
    ownerId?: string;
    isService?: boolean;
    isProduct?: boolean;
    videos?: string[];
    links?: string[];
  };
}

export interface ReviewUserAndproduct {
  id: string;
  body: string;
  comments: Comment[];
  createdDate: string;
  rating: number;
  title: string;
  productId: string;
  userId: string;
  isVerified: boolean | null;
  verifiedBy: string | null;
  isPublic: boolean;
  images: string[];
  videos: string[];
  links: string[];
  createdBy: string;
  isDeleted: boolean;
  user: {
    id: string;
    userName: string;
    avatar: string;
    createdDate: string;
    email: string;
    firstName: string;
    lastName: string;
    clerkUserId: string;
    isDeleted: boolean;
  };
  product: {
    id: string;
    address: string | null;
    createdDate: string;
    description: string;
    images: string[];
    videos: string[];
    links: string[];
    name: string;
    tags: string[];
    openingHrs: string | null;
    closingHrs: string | null;
    telephone: string | null;
    website: string[];
    rating: number;
    hasOwner: boolean | null;
    ownerId: string | null;
    createdById: string;
    isDeleted: boolean;
  };
}

// Interface representing user data
export interface UserDATA {
  avatar?: string | null;
  azp: string;
  email: string;
  exp: number;
  firstName: string;
  lastName: string;
  fullName: string;
  iat: number;
  iss: string;
  jti: string;
  nbf: number;
  sub: string;
  userId: string;
  userName: string;
  metadata?: {
    id: string;
    userInDb: boolean;
  };
}

export interface UserCreatedEvent {
  object: string;
  type: string;

  data: {
    birthday: string;
    created_at: number;
    email_addresses: Array<{
      email_address: string;
      id: string;
      linked_to: Array<string>;
      object: string;
      verification: {
        status: string;
        strategy: string;
      };
    }>;
    external_accounts: Array<string>;
    external_id: string;
    first_name: string;
    gender: string;
    id: string;
    image_url: string;
    last_name: string;
    last_sign_in_at: number;
    object: string;
    password_enabled: boolean;
    phone_numbers: Array<string>;
    primary_email_address_id: string;
    primary_phone_number_id: string | null;
    primary_web3_wallet_id: string | null;
    private_metadata: {};
    profile_image_url: string;
    public_metadata: {};
    two_factor_enabled: boolean;
    unsafe_metadata: {};
    updated_at: number;
    username: string | null;
    web3_wallets: Array<string>;
  };
}

export interface iTag {
  tags: string[];
}

export interface iAdminAction {
  id?: string;
  adminId: string;
  actionType: string;
  targetId: string;
  targetType: string;
  description: string;
  createdAt?: Date;
  admin?: iUser;
}

export interface iModerationEvent {
  id?: string;
  reviewId: string;
  adminId: string;
  action: "APPROVED" | "REJECTED" | "FLAGGED";
  reason?: string;
  createdAt?: Date;
  review?: iReview;
  admin?: iUser;
}

export interface OGImage {
  url: string;
  width?: number;
  height?: number;
  alt?: string;
  type?: string;
  secure_url?: string;
}

// Analytics Interfaces
export interface iProductViewEvent {
  id?: string;
  productId: string;
  userId?: string; // Optional, for authenticated users
  sessionId: string; // For anonymous tracking
  timestamp: Date;
  duration?: number; // Time spent viewing in seconds
  source?: string; // Where the user came from
  deviceType?: string; // Mobile/Desktop/Tablet
  isNewUser: boolean;
  isThrottled: boolean; // Whether this view was counted in stats
}

export interface iProductAnalytics {
  id?: string;
  productId: string;
  totalViews: number;
  uniqueVisitors: number;
  averageViewDuration: number;
  peakHours: Record<number, number>; // Hour -> View count
  weekdayStats: Record<string, number>; // Day -> View count
  lastUpdated: Date;
}

export interface iUserProductInteraction {
  id?: string;
  userId: string;
  productId: string;
  lastViewed: Date;
  viewCount: number;
  hasReviewed: boolean;
  hasLiked: boolean;
  averageTimeSpent: number;
}

// Subscription Interfaces
export interface iSubscriptionTier {
  id: string;
  name: string;
  description: string;
  price: number;
  billingCycle: "monthly" | "yearly" | "quarterly";
  features: string[];
  maxProducts: number;
  maxPromotions: number;
  maxReviews: number;
  analyticsAccess: boolean;
  apiAccess: boolean;
  priority: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface iSubscriptionUsage {
  id?: string;
  businessId: string;
  productCount: number;
  promotionCount: number;
  reviewCount: number;
  apiCalls: number;
  monthlyViews: number;
  lastUpdated: Date;
  business?: iBusiness;
}

export interface iSubscriptionBilling {
  id?: string;
  businessId: string;
  tierId: string;
  customerId?: string;
  paymentMethod?: string;
  lastFour?: string;
  nextBillingDate: Date;
  amount: number;
  status: "active" | "past_due" | "canceled" | "inactive";
  createdAt: Date;
  updatedAt?: Date;
  tier?: iSubscriptionTier;
  business?: iBusiness;
}

export interface iSubscriptionTransaction {
  id?: string;
  businessId: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  status: "succeeded" | "failed" | "pending";
  paymentMethod: string;
  receiptUrl?: string;
  description: string;
  createdAt: Date;
  billing?: iSubscriptionBilling;
  business?: iBusiness;
}

// Analytics Dashboard Interfaces
export interface iBusinessAnalytics {
  id?: string;
  businessId: string;
  totalViews: number;
  uniqueVisitors: number;
  totalReviews: number;
  averageRating: number;
  viewsPerDay: Record<string, number>; // Date string -> count
  trafficSources: Record<string, number>; // Source -> count
  deviceTypes: Record<string, number>; // Device type -> count
  conversionRate: number;
  topProducts: Array<{
    id: string;
    name: string;
    views: number;
    conversion: number;
  }>;
  lastUpdated: Date;
  business?: iBusiness;
}

export interface iProductPerformance {
  id?: string;
  productId: string;
  businessId: string;
  viewsTrend: Array<{
    date: string;
    views: number;
  }>;
  reviewsTrend: Array<{
    date: string;
    count: number;
    rating: number;
  }>;
  conversionTrend: Array<{
    date: string;
    rate: number;
  }>;
  engagementMetrics: {
    averageTimeOnPage: number;
    bounceRate: number;
    clickThroughRate: number;
  };
  competitorComparison?: Array<{
    name: string;
    views: number;
    rating: number;
  }>;
  lastUpdated: Date;
  product?: iProduct;
}

export interface iTrafficSource {
  id?: string;
  businessId: string;
  source: string;
  medium: string;
  campaign?: string;
  visitors: number;
  sessions: number;
  bounceRate: number;
  conversionRate: number;
  lastUpdated: Date;
  business?: iBusiness;
}

export interface iAnalyticsPeriod {
  startDate: Date;
  endDate: Date;
  comparison?: "previous_period" | "previous_year" | "none";
}

export interface iTrafficTrend {
  date: string;
  Total: number;
  Google: number;
  Direct: number;
  Social: number;
  Other: number;
}

export interface iProductClaim {
  id: string;
  productId: string;
  userId: string;
  status: string;
  contactInfo: string;
  additionalInfo: string;
  images: string[];
  createdAt: Date;
  updatedAt: Date;
  reviewedAt: Date | null;
  reviewedBy: string | null;
  rejectionReason: string | null;
  product: {
    id: string;
    name: string;
    description: string;
    display_image: string;
    address?: string | null;
  } | null;
  user?: iUser;
  reviewer?: iUser;
}

export interface iBugReport {
  id: string;
  title: string;
  description: string;
  browser?: string;
  device?: string;
  mobile_os?: string;
  status: "OPEN" | "IN_PROGRESS" | "RESOLVED" | "CLOSED" | "WONT_FIX";
  resolved_at?: Date;
  resolved_by?: string;
  resolution_notes?: string;
  created_at: Date;
  updated_at: Date;
  reporter?: iUser;
  reporterId?: string;
  resolver?: iUser;
  adminActions?: iAdminAction[];
}

export interface iReviewReport {
  id: string;
  reviewId: string;
  userId: string;
  reason: string;
  status: "PENDING" | "REVIEWED" | "RESOLVED";
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  notes?: string;
  review?: iReview;
  user?: iUser;
  resolver?: iUser;
}

export interface ApiResponse<T> {
  success: boolean;
  status: number;
  data: T;
  error?: string;
}

// ===== PRODUCT REPORTING INTERFACES =====

export interface iProductReport {
  id: string;
  productId: string;
  userId: string;
  reason: string;
  status: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  resolvedAt?: Date | string | null;
  resolvedBy?: string | null;
  notes?: string | null;
  reportType: 'product';

  // Related entities
  product: {
    id: string;
    name: string;
    description: string;
    display_image?: string;
    createdBy: {
      id: string;
      userName: string;
      firstName: string;
      lastName: string;
    };
  };

  user: {
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    email?: string;
  };

  resolver?: {
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
  } | null;
}

// Update existing iReviewReport to include reportType for consistency
export interface iReviewReportExtended extends Omit<iReviewReport, 'review'> {
  reportType: 'review';
  review: {
    id: string;
    title: string;
    rating: number;
    body: string;
    product: {
      id: string;
      name: string;
    };
    user: {
      id: string;
      userName: string;
      firstName: string;
      lastName: string;
    };
  };
}

export type iReport = iProductReport | iReviewReportExtended;

export interface iReportsResponse {
  success: boolean;
  data: {
    reports: iReport[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
    reportType: 'all' | 'review' | 'product';
  };
}

export const REPORT_STATUSES = {
  PENDING: 'PENDING',
  REVIEWED: 'REVIEWED',
  RESOLVED: 'RESOLVED',
  DISMISSED: 'DISMISSED',
} as const;

export type ReportStatus = keyof typeof REPORT_STATUSES;

export const PRODUCT_REPORT_REASONS = [
  { value: 'inappropriate_content', label: 'Inappropriate Content' },
  { value: 'spam', label: 'Spam or Fake Listing' },
  { value: 'copyright', label: 'Copyright Violation' },
  { value: 'misleading_info', label: 'Misleading Information' },
  { value: 'duplicate', label: 'Duplicate Listing' },
  { value: 'closed_business', label: 'Business is Closed/Doesn\'t Exist' },
  { value: 'other', label: 'Other (please specify)' },
] as const;

export type ProductReportReason = typeof PRODUCT_REPORT_REASONS[number]['value'];

// ===== ENHANCED ANALYTICS INTERFACES =====

// Enhanced ProductAnalytics interface (consolidating with existing iProductAnalytics)
export interface iProductAnalyticsEnhanced extends iProductAnalytics {
  peakHours: Record<string, number>; // Updated to match types/analytics.ts format
  weekdayStats: Record<string, number>; // Updated to match types/analytics.ts format
}

// Enhanced ProductViewEvent interface (consolidating with existing iProductViewEvent)
export interface iProductViewEventEnhanced extends iProductViewEvent {
  // All fields already match, keeping existing structure
}

// Enhanced BusinessAnalytics interface (consolidating different versions)
export interface iBusinessAnalyticsEnhanced {
  id?: string;
  businessId: string;
  totalViews: number;
  uniqueVisitors: number;
  totalReviews: number;
  averageRating: number;
  // Updated format to match actual usage in code
  viewsPerDay: Array<{ date: string; views: number }>;
  trafficSources: Array<{ source: string; count: number }>;
  deviceTypes: Array<{ type: string; count: number }>;
  conversionRate: number;
  topProducts: Array<{
    id: string;
    name: string;
    views: number;
    conversion: number;
  }>;
  lastUpdated: Date;
  business?: iBusiness;
}

// Enhanced TrafficSource interface (consolidating different versions)
export interface iTrafficSourceEnhanced {
  id?: string;
  businessId: string;
  source: string;
  medium: string;
  campaign?: string;
  visitors: number;
  sessions: number;
  bounceRate: number;
  conversionRate: number;
  lastUpdated: Date;
  business?: iBusiness;
}

// Additional analytics interfaces from types/analytics.ts
export interface ViewsPerDay {
  date: string;
  views: number;
}

export interface DeviceTypeStats {
  type: string;
  count: number;
}

export interface TopProductStats {
  id: string;
  name: string;
  views: number;
  conversion: number;
}

export interface TrafficSourceMetrics {
  source: string;
  medium: string;
  visitors: number;
  sessions: number;
  bounceRate: number;
  conversionRate: number;
  lastUpdated: Date;
}

// Type aliases for backward compatibility
export type ProductAnalyticsData = iProductAnalyticsEnhanced;

// ===== WIDGET SYSTEM INTERFACES =====

export type WidgetType = 
  | 'REVIEW_CAROUSEL'
  | 'REVIEW_GRID'
  | 'RATING_SUMMARY'
  | 'MINI_REVIEW'
  | 'BUSINESS_CARD'
  | 'TRUST_BADGE'
  | 'REVIEW_POPUP';

export type WidgetSecurityLevel = 'SIMPLE' | 'SECURE';

export interface iWidget {
  id: string;
  businessId: string;
  productId?: string | null;
  name: string;
  type: WidgetType;
  config: Record<string, any>;
  isActive: boolean;
  
  // Security Configuration
  securityLevel: WidgetSecurityLevel;
  apiKey?: string | null;
  tokenExpiry: number;
  maxRequestsPerHour: number;
  
  theme: string;
  primaryColor?: string | null;
  borderRadius: string;
  showLogo: boolean;
  showPoweredBy: boolean;
  maxReviews: number;
  showRating: boolean;
  showReviewText: boolean;
  showReviewDate: boolean;
  showReviewerName: boolean;
  allowedDomains: string[];
  viewCount: number;
  clickCount: number;
  conversionCount: number;
  blockedCount: number;
  createdAt: Date;
  updatedAt: Date;
  lastUsed?: Date | null;
  business?: iBusiness;
  product?: iProduct;
  analytics?: iWidgetAnalytics;
  tokens?: iWidgetToken[];
  domainVerifications?: iDomainVerification[];
}

export interface iWidgetAnalytics {
  id: string;
  widgetId: string;
  dailyViews: Record<string, number>;
  dailyClicks: Record<string, number>;
  dailyConversions: Record<string, number>;
  blockedEvents: Record<string, number>;
  topReferrers: Record<string, number>;
  averageLoadTime: number;
  errorCount: number;
  lastUpdated: Date;
  widget?: iWidget;
}

export interface WidgetConfig {
  id: string;
  type: WidgetType;
  businessId: string;
  productId?: string;
  styling: WidgetStyling;
  content: WidgetContent;
}

export interface WidgetStyling {
  theme: 'light' | 'dark' | 'custom';
  primaryColor?: string;
  borderRadius: string;
  width?: string;
  height?: string;
}

export interface WidgetContent {
  maxReviews: number;
  showRating: boolean;
  showReviewText: boolean;
  showReviewDate: boolean;
  showReviewerName: boolean;
  showLogo: boolean;
  showPoweredBy: boolean;
}

export interface WidgetTrackingData {
  event: 'view' | 'click' | 'interaction' | 'conversion';
  referrer?: string;
  userAgent?: string;
  elementType?: string;
  timestamp?: Date;
}

export interface WidgetEmbedOptions {
  widgetId: string;
  containerId: string;
  baseUrl?: string;
}

export interface WidgetAnalyticsResponse {
  success: boolean;
  data: {
    widget: iWidget;
    analytics: iWidgetAnalytics;
    period: {
      startDate: Date;
      endDate: Date;
    };
    metrics: {
      totalViews: number;
      totalClicks: number;
      clickThroughRate: number;
      topReferrers: Array<{
        domain: string;
        views: number;
        percentage: number;
      }>;
      dailyStats: Array<{
        date: string;
        views: number;
        clicks: number;
      }>;
    };
  };
}

export interface CreateWidgetRequest {
  businessId: string;
  productId?: string;
  name: string;
  type: WidgetType;
  securityLevel?: WidgetSecurityLevel;
  config?: Record<string, any>;
  styling?: Partial<WidgetStyling>;
  content?: Partial<WidgetContent>;
  allowedDomains?: string[];
}

export interface UpdateWidgetRequest {
  name?: string;
  config?: Record<string, any>;
  styling?: Partial<WidgetStyling>;
  content?: Partial<WidgetContent>;
  isActive?: boolean;
  allowedDomains?: string[];
  securityLevel?: WidgetSecurityLevel;
  tokenExpiry?: number;
  maxRequestsPerHour?: number;
}

// ===== WIDGET SECURITY INTERFACES =====

export type VerificationMethod = 'HTML_FILE' | 'DNS_TXT' | 'META_TAG';

export interface iWidgetToken {
  id: string;
  widgetId: string;
  domain: string;
  token: string;
  expiresAt: Date;
  isActive: boolean;
  createdAt: Date;
  lastUsed?: Date | null;
  requestCount: number;
  widget?: iWidget;
}

export interface iDomainVerification {
  id: string;
  widgetId: string;
  domain: string;
  verificationCode: string;
  method: VerificationMethod;
  isVerified: boolean;
  verifiedAt?: Date | null;
  createdAt: Date;
  expiresAt: Date;
  widget?: iWidget;
}

export interface DomainVerificationRequest {
  domain: string;
  method: VerificationMethod;
}

export interface DomainVerificationResponse {
  success: boolean;
  data: {
    verificationCode: string;
    method: VerificationMethod;
    domain: string;
    expiresAt: Date;
    instructions: {
      htmlFile?: {
        fileName: string;
        content: string;
        uploadPath: string;
      };
      dnsRecord?: {
        type: string;
        name: string;
        value: string;
      };
      metaTag?: {
        tag: string;
        placement: string;
      };
    };
  };
}

export interface TokenGenerationRequest {
  domain: string;
}

export interface TokenGenerationResponse {
  success: boolean;
  data: {
    token: string;
    domain: string;
    expiresAt: Date;
    embedCode: string;
  };
}

export interface WidgetSecuritySettings {
  securityLevel: WidgetSecurityLevel;
  allowedDomains: string[];
  tokenExpiry: number;
  maxRequestsPerHour: number;
}

export interface SecureWidgetRequest {
  token: string;
  domain: string;
  referrer?: string;
  userAgent?: string;
}

export interface WidgetTokenPayload {
  widgetId: string;
  domain: string;
  iat: number;
  exp: number;
  permissions: string[];
}

export interface Sector{
  id: string;
  sector?: string;
  sub_sectors?: string[];
}

export interface SuggestionBox {
  id?: string;
  ip_address: string;
  description: string;
  user_email?: string;
  user_name?: string;
}