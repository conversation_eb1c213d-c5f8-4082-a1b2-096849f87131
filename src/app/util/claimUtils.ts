import { formatPhoneNumber } from '@/app/util/formatters';

/**
 * Format contact information string into a more human-readable format
 * @param contactInfo - Raw contact information string (could be JSON or plain text)
 * @returns Formatted contact information
 */
export const formatContactInfo = (contactInfo: string): string => {
  if (!contactInfo) return 'No contact information provided';
  
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(contactInfo);
    
    // If it's an object, format it nicely
    if (typeof parsed === 'object' && parsed !== null) {
      const formattedParts: string[] = [];
      
      // Format name if present
      if (parsed.name) {
        formattedParts.push(`Name: ${parsed.name}`);
      }
      
      // Format email if present
      if (parsed.email) {
        formattedParts.push(`Email: ${parsed.email}`);
      }
      
      // Format phone if present
      if (parsed.phone) {
        const formattedPhone = formatPhoneNumber(parsed.phone);
        formattedParts.push(`Phone: ${formattedPhone}`);
      }
      
      // Format address if present
      if (parsed.address) {
        formattedParts.push(`Address: ${parsed.address}`);
      }
      
      // Format any other fields
      Object.keys(parsed).forEach(key => {
        if (!['name', 'email', 'phone', 'address'].includes(key)) {
          formattedParts.push(`${key}: ${parsed[key]}`);
        }
      });
      
      return formattedParts.join('\n');
    }
    
    // If it's not an object, return as is
    return contactInfo;
  } catch (error) {
    // If parsing fails, it's likely a plain string
    // Try to extract phone number and format it
    const phoneRegex = /\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
    const phones = contactInfo.match(phoneRegex);
    
    if (phones && phones.length > 0) {
      let formatted = contactInfo;
      phones.forEach(phone => {
        const formattedPhone = formatPhoneNumber(phone);
        formatted = formatted.replace(phone, formattedPhone);
      });
      return formatted;
    }
    
    // If no phone number found, return as is
    return contactInfo;
  }
};
