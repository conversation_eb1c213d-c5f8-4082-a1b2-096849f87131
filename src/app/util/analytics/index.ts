// Main analytics module - unified interface
import { prisma } from '../prismaClient';
import { redisService } from '../../lib/redis';

// Re-export all types
export * from './types';

// Re-export cache module (now redirects to lib/cache)
export * from './cache';
export * from './formatters';
export * from './product';
export * from './business';
export * from './admin';



// Create the main DatabaseAnalytics class for backward compatibility
export class DatabaseAnalytics {
  private prisma: import('@prisma/client').PrismaClient;
  private redisService: typeof redisService;

  constructor(prismaClient: import('@prisma/client').PrismaClient, redis = redisService) {
    this.prisma = prismaClient;
    this.redisService = redis;
  }

  // Helper methods from the original class
  private async getFromCache<T>(key: string): Promise<T | null> {
    try {
      return await this.redisService.getFromCache<T>(key);
    } catch (error) {
      console.warn(`Cache get failed for key ${key}:`, error);
      return null;
    }
  }

  private async setInCache<T>(key: string, value: T, ttl: number = 3600): Promise<void> {
    try {
      await this.redisService.setInCache(key, value, ttl);
    } catch (error) {
      console.warn(`Cache set failed for key ${key}:`, error);
    }
  }

  private calculatePeakHours(viewsData: any[]): Record<number, number> {
    const hourCounts: Record<number, number> = {};
    
    viewsData.forEach(view => {
      const hour = new Date(view.timestamp).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });
    
    return hourCounts;
  }

  private calculateWeekdayStats(viewsData: any[]): { [key: string]: number } {
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const weekdayCounts = new Array(7).fill(0);
    
    viewsData.forEach(view => {
      const dayOfWeek = new Date(view.timestamp).getDay();
      weekdayCounts[dayOfWeek]++;
    });
    
    const result: { [key: string]: number } = {};
    weekdays.forEach((day, index) => {
      result[day] = weekdayCounts[index];
    });
    
    return result;
  }

  private handleError(operation: string, error: any): never {
    console.error(`DatabaseAnalytics ${operation} error:`, error);
    throw new Error(`Failed to ${operation}: ${error.message}`);
  }

  // Add the getProductAnalytics method for backward compatibility
  async getProductAnalytics(productId: string): Promise<any> {
    // Import and use the product analytics function
    const { databaseAnalytics } = await import('./product');
    return databaseAnalytics.getProductAnalytics(productId);
  }
}

// Create singleton instance
const databaseAnalytics = new DatabaseAnalytics(prisma);

export { databaseAnalytics };

// For backward compatibility, re-export the main functions
export { getBusinessAnalyticsFromDB, getTrafficSourcesFromDB } from './business';
export { getTopReviewersFromCache, getAllProductsFromCache, getProductReviewsFromCache } from './admin';
export { getProductSearchFromCache, getProductDetailsFromCache } from './product';