"use server";
import { v2 as cloudinary } from "cloudinary";

// Create a separate Cloudinary instance for product images to avoid conflicts with main config
let productCloudinaryConfig: any;

if (process.env.PRODUCT_CLOUDINARY_URL) {
  console.log('Product Cloudinary: Using PRODUCT_CLOUDINARY_URL');
  // Parse the PRODUCT_CLOUDINARY_URL manually to avoid global config conflicts
  const url = new URL(process.env.PRODUCT_CLOUDINARY_URL);
  productCloudinaryConfig = {
    cloud_name: url.hostname,
    api_key: url.username,
    api_secret: url.password,
  };
} else {
  console.log('Product Cloudinary: Using individual env vars');
  productCloudinaryConfig = {
    cloud_name: process.env.PRODUCT_CLOUDINARY_CLOUD_NAME,
    api_key: process.env.PRODUCT_CLOUDINARY_API_KEY,
    api_secret: process.env.PRODUCT_CLOUDINARY_API_SECRET,
  };
}

console.log('Product Cloudinary configured with cloud_name:', productCloudinaryConfig.cloud_name);

interface CloudinaryUploadResult {
  public_id: string;
  version: number;
  signature: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  created_at: string;
  bytes: number;
  type: string;
  url: string;
  secure_url: string;
}

export async function uploadProductImageToCloudinary(data: any): Promise<CloudinaryUploadResult> {
  // Validate input - should be data URL or file path
  if (data.startsWith('blob:')) {
    throw new Error('Blob URLs cannot be uploaded. Please convert to data URL first.');
  }
  
  if (!data.startsWith('data:') && !data.startsWith('http') && !data.startsWith('/')) {
    throw new Error('Invalid image data format. Expected data URL, HTTP URL, or file path.');
  }

  // Validate that product Cloudinary credentials are configured
  if (!process.env.PRODUCT_CLOUDINARY_URL && (!process.env.PRODUCT_CLOUDINARY_CLOUD_NAME || !process.env.PRODUCT_CLOUDINARY_API_KEY || !process.env.PRODUCT_CLOUDINARY_API_SECRET)) {
    throw new Error('Product Cloudinary credentials not configured. Please set either PRODUCT_CLOUDINARY_URL or all of PRODUCT_CLOUDINARY_CLOUD_NAME, PRODUCT_CLOUDINARY_API_KEY, and PRODUCT_CLOUDINARY_API_SECRET environment variables.');
  }

  return await cloudinary.uploader.upload(data, {
    ...productCloudinaryConfig,
    resource_type: "image",
    folder: "product_gallery",
  });
}

export async function deleteProductImageFromCloudinary(publicId: string) {
  return await cloudinary.uploader.destroy(publicId, productCloudinaryConfig);
}
