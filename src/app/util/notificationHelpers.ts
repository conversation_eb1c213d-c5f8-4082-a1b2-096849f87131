import {
  iProductOwnerNotification,
  iUserNotification,
  iUser,
  iProduct,
  LikeNotification,
  SystemNotification,
} from "./Interfaces";

type NotificationType = iUserNotification | iProductOwnerNotification | LikeNotification | SystemNotification;

/**
 * Checks if a user notification is from a product owner
 *
 * @param notification The user notification to check
 * @returns boolean indicating if the notification is from a product owner
 */
export function isOwnerNotification(notification: iUserNotification): boolean {
  // Check if the notification type indicates it's from an owner
  if (
    notification.notification_type === "owner_comment" ||
    notification.notification_type === "owner_reply"
  ) {
    return true;
  }

  // Check if the notification has a from_id that matches a product owner
  // This is a more general approach that works even if notification_type isn't set correctly
  if (notification.from_id && notification.product_id) {
    // In a real implementation, we would check if from_id is the owner of product_id
    // For now, we'll use a simpler approach based on notification content
    const content = notification.content?.toLowerCase() || "";
    return (
      content.includes("owner") ||
      content.includes("business owner") ||
      content.includes("responded to your")
    );
  }

  return false;
}

/**
 * Determines if a notification should have premium styling
 *
 * @param notification The notification to check
 * @returns boolean indicating if the notification should have premium styling
 */
export function shouldHavePremiumStyling(
  notification: NotificationType
): boolean {
  // For testing purposes, let's make all notifications have premium styling
  // return true;

  if ("notification_type" in notification) {
    // This is a user notification
    return isOwnerNotification(notification as iUserNotification);
  }

  // For owner notifications, check if it's a notification about a comment
  if ("comment_id" in notification) {
    // Check if it's a like notification
    if ("target_type" in notification) {
      return false; // Like notifications don't need premium styling
    }
    
    // For owner notifications with comment_id
    if ("owner_id" in notification) {
      return true;
    }
  }

  // Product owner notifications don't need premium styling by default
  return false;
}
