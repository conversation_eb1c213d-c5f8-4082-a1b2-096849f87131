import { BusinessHours, DaySchedule, DAYS_OF_WEEK, createDefaultBusinessHours } from '../types/businessHours';
import { Prisma } from '@prisma/client';
import { iProduct } from '../util/Interfaces';

/**
 * Converts a Prisma JsonValue to a strongly typed BusinessHours object
 * @param jsonValue The JSON value from Prisma
 * @returns A properly typed BusinessHours object or null if the input is invalid
 */
export function parseBusinessHours(jsonValue: Prisma.JsonValue | null | undefined): BusinessHours | null {
  if (!jsonValue) return null;
  
  try {
    // If it's already an object with the right structure
    if (typeof jsonValue === 'object' && jsonValue !== null && 'schedules' in jsonValue) {
      const businessHours = jsonValue as unknown as BusinessHours;
      
      // Validate the structure
      if (Array.isArray(businessHours.schedules) && 
          typeof businessHours.openOnHolidays === 'boolean') {
        return businessHours;
      }
    }
    
    // If it's a string (serialized JSON), parse it
    if (typeof jsonValue === 'string') {
      const parsed = JSON.parse(jsonValue);
      if (parsed && typeof parsed === 'object' && 'schedules' in parsed) {
        return parsed as BusinessHours;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing business hours:', error);
    return null;
  }
}

/**
 * Type guard to check if a value is a valid BusinessHours object
 * @param value The value to check
 * @returns True if the value is a valid BusinessHours object
 */
export function isBusinessHours(value: any): value is BusinessHours {
  return (
    value !== null &&
    typeof value === 'object' &&
    'schedules' in value &&
    Array.isArray(value.schedules) &&
    typeof value.openOnHolidays === 'boolean'
  );
}

/**
 * Safely converts a Prisma product to an iProduct with properly typed businessHours
 * @param product The product from Prisma with JsonValue businessHours
 * @returns A properly typed iProduct
 */
/**
 * Converts legacy business hours format to new flexible BusinessHours format
 */
function convertLegacyBusinessHours(
  openingDays: string[] | null | undefined,
  openingHrs: string | null | undefined,
  closingHrs: string | null | undefined
): BusinessHours | null {
  // If no legacy data exists, return null
  if (!openingDays || !openingHrs || !closingHrs || openingDays.length === 0) {
    return null;
  }

  // Create default business hours structure
  const businessHours = createDefaultBusinessHours();
  
  // Update schedules based on legacy opening days
  businessHours.schedules = businessHours.schedules.map(schedule => {
    if (openingDays.includes(schedule.day)) {
      return {
        ...schedule,
        isClosed: false,
        openTime: openingHrs,
        closeTime: closingHrs
      };
    }
    return schedule; // Keep closed days as is
  });
  
  return businessHours;
}

export function convertPrismaProductToIProduct<T extends Record<string, any>>(product: T): iProduct {
  if (!product) return {} as iProduct;
  
  // Create a new object with all properties from the original product
  const typedProduct: iProduct = { ...product } as unknown as iProduct;
  
  // Parse the businessHours field if it exists and is not null
  if ('businessHours' in product && product.businessHours !== null && product.businessHours !== undefined) {
    typedProduct.businessHours = parseBusinessHours(product.businessHours as Prisma.JsonValue);
  } else {
    // If businessHours is null/undefined, try to convert from legacy fields
    const legacyBusinessHours = convertLegacyBusinessHours(
      (product as any).openingDays,
      (product as any).openingHrs,
      (product as any).closingHrs
    );
    
    if (legacyBusinessHours) {
      typedProduct.businessHours = legacyBusinessHours;
    }
    // If no legacy data either, businessHours will remain undefined
  }
  
  return typedProduct;
}
