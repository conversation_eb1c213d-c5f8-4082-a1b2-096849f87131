/*
 * Server-only tier utilities
 * These functions can be safely imported in API routes and server components.
 */

export type Tier = 'starter' | 'pro' | 'enterprise';

// Ordered from lowest privilege to highest.
const TIER_ORDER: Tier[] = ['starter', 'pro', 'enterprise'];

/**
 * Returns a numeric rank to compare tiers.
 */
function tierRank(tier: Tier): number {
  return TIER_ORDER.indexOf(tier);
}

/**
 * True if current tier meets or exceeds the required tier.
 */
export function hasRequiredTier(current: Tier, required: Tier): boolean {
  return tierRank(current) >= tierRank(required);
}

/**
 * Throws an error if the current tier is insufficient. Can be used in API
 * routes and server actions. Error can be caught at the edge to surface an
 * upgrade prompt.
 */
export const TIER_LIMITS_ENABLED = process.env.ENABLE_TIER_LIMITS === 'true';
console.log("Tiers enabled=", TIER_LIMITS_ENABLED)

export function assertTier(current: Tier, required: Tier, featureKey?: string): void {
  if (!hasRequiredTier(current, required)) {
    console.log("does not have required tier")
    const err: Error & { status?: number; featureKey?: string } = new Error(
      `Upgrade required: ${required} tier needed${featureKey ? ` for ${featureKey}` : ''}.`,
    );
    err.status = 402; // Payment Required
    if (featureKey) err.featureKey = featureKey;
    throw err;
  }
}

/**
 * Assert tier only when limits are enabled (feature flag).
 */
export function maybeAssertTier(current: Tier, required: Tier, featureKey?: string): void {
  console.log("6666666666666", !TIER_LIMITS_ENABLED)
  console.log("Tiers enabled in the function check =", TIER_LIMITS_ENABLED)
  if (!TIER_LIMITS_ENABLED) return;
  assertTier(current, required, featureKey);
}
