import { useState, useCallback, useEffect } from "react";
import { useImageWorker } from './useImageWorker';

interface ResizeResult {
  dataUrl: string;
  resized: boolean;
}

interface ResizeOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  maxFileSize?: number;
}

const DEFAULT_MAX_WIDTH = 1000;
const DEFAULT_MAX_HEIGHT = 1000;
const DEFAULT_QUALITY = 0.85; // Higher quality for product images
const DEFAULT_MAX_FILE_SIZE = 800 * 1024; // 800KB for better quality

export const useProductImageResizer = () => {
  const [isResizing, setIsResizing] = useState(false);
  const { processImage: processWithWorker, isSupported: workerSupported, cleanup } = useImageWorker();

  // Cleanup worker on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const calculateDimensions = useCallback(
    (width: number, height: number, maxWidth: number, maxHeight: number): {
      width: number;
      height: number;
      shouldResize: boolean;
    } => {
      let newWidth = width;
      let newHeight = height;
      let shouldResize = false;

      // Calculate scaling to fit within max dimensions while maintaining aspect ratio
      const widthRatio = maxWidth / width;
      const heightRatio = maxHeight / height;
      const ratio = Math.min(widthRatio, heightRatio);

      if (ratio < 1) {
        newWidth = Math.round(width * ratio);
        newHeight = Math.round(height * ratio);
        shouldResize = true;
      }

      return {
        width: newWidth,
        height: newHeight,
        shouldResize,
      };
    },
    []
  );

  const resizeImage = useCallback(
    async (file: File, options: ResizeOptions = {}): Promise<ResizeResult> => {
      const {
        maxWidth = DEFAULT_MAX_WIDTH,
        maxHeight = DEFAULT_MAX_HEIGHT,
        quality = DEFAULT_QUALITY,
        maxFileSize = DEFAULT_MAX_FILE_SIZE,
      } = options;

      setIsResizing(true);

      try {
        return await new Promise<ResizeResult>((resolve, reject) => {
          const img = new Image();
          img.onload = () => {
            const canvas = document.createElement("canvas");
            const { width, height, shouldResize } = calculateDimensions(
              img.width,
              img.height,
              maxWidth,
              maxHeight
            );

            canvas.width = width;
            canvas.height = height;

            const ctx = canvas.getContext("2d");
            if (!ctx) {
              reject(new Error("Failed to get canvas context"));
              return;
            }

            // Use better image rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = "high";
            ctx.drawImage(img, 0, 0, width, height);

            let currentQuality = quality;
            const tryCompress = () => {
              const dataUrl = canvas.toDataURL(file.type, currentQuality);
              
              // Convert dataURL to approximate file size (base64 is ~33% larger than binary)
              const base64Length = dataUrl.split(',')[1].length;
              const approximateSize = (base64Length * 3) / 4;

              if (approximateSize <= maxFileSize || currentQuality <= 0.3) {
                resolve({
                  dataUrl,
                  resized: shouldResize || currentQuality < quality,
                });
              } else {
                currentQuality -= 0.1;
                tryCompress();
              }
            };

            tryCompress();
          };

          img.onerror = () => reject(new Error(`Failed to load image: ${file.name}`));
          img.src = URL.createObjectURL(file);
        });
      } finally {
        setIsResizing(false);
      }
    },
    [calculateDimensions]
  );

  const processImage = useCallback(
    async (file: File, options: ResizeOptions = {}): Promise<ResizeResult> => {
      if (!file.type.startsWith("image/")) {
        throw new Error("File is not an image");
      }

      const maxKB = options.maxFileSize ?? DEFAULT_MAX_FILE_SIZE;
      if (file.size <= maxKB) {
        // Convert to actual data URL, not blob URL
        const dataUrl = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
        
        return {
          dataUrl,
          resized: false,
        };
      }

      // Try worker first if supported
      if (workerSupported) {
        try {
          const workerResult = await processWithWorker(file, {
            maxWidth: options.maxWidth ?? DEFAULT_MAX_WIDTH,
            maxHeight: options.maxHeight ?? DEFAULT_MAX_HEIGHT,
            quality: options.quality ?? DEFAULT_QUALITY,
            maxFileSize: options.maxFileSize ?? DEFAULT_MAX_FILE_SIZE,
          });
          
          return {
            dataUrl: workerResult.dataUrl,
            resized: workerResult.resized,
          };
        } catch (error) {
          console.warn('Worker processing failed, falling back to main thread:', error);
          // Fall through to main thread processing
        }
      }

      // Fallback to main thread processing
      return await resizeImage(file, options);
    },
    [resizeImage, workerSupported, processWithWorker]
  );

  return {
    processImage,
    isResizing,
  };
};
