import React from 'react';
import {
  MessageSquare,
  Star,
  ThumbsUp,
  Thum<PERSON>Down,
  Heart,
  <PERSON>ly,
  User,
  CheckCircle,
  AlertCircle,
  Bell,
  Eye,
  UserPlus,
  MessageCircle,
  Award,
  TrendingUp,
} from 'lucide-react';
import { iProductOwnerNotification, iUserNotification, LikeNotification, SystemNotification } from './Interfaces';

type NotificationType = iUserNotification | iProductOwnerNotification | LikeNotification | SystemNotification;

export interface NotificationTypeInfo {
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  label: string;
}

/**
 * Get notification type information including icon, color, and label
 */
export function getNotificationTypeInfo(
  notification: NotificationType
): NotificationTypeInfo {
  // Handle like notifications
  if ('target_type' in notification) {
    return {
      icon: <ThumbsUp className="h-4 w-4" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      label: 'Like'
    };
  }
  
  // Handle user notifications
  if ('action_type' in notification && notification.action_type) {
    switch (notification.action_type) {
      case 'like':
        return {
          icon: <ThumbsUp className="h-4 w-4" />,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          label: 'Like'
        };
      case 'dislike':
        return {
          icon: <ThumbsDown className="h-4 w-4" />,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          label: 'Dislike'
        };
      case 'helpful':
        return {
          icon: <Heart className="h-4 w-4" />,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          label: 'Helpful'
        };
    }
  }

  // Handle system notifications first
  if ('type' in notification) {
    return {
      icon: <Bell className="h-4 w-4" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      label: 'System Announcement'
    };
  }

  // Handle notification types
  const notificationType = (notification as iUserNotification | iProductOwnerNotification).notification_type;
  
  switch (notificationType) {
    case 'review':
      return {
        icon: <Star className="h-4 w-4" />,
        color: 'text-amber-600',
        bgColor: 'bg-amber-50',
        label: 'New Review'
      };
    
    case 'comment':
      return {
        icon: <MessageSquare className="h-4 w-4" />,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        label: 'Comment'
      };
    
    case 'comment_reply':
    case 'reply':
      return {
        icon: <Reply className="h-4 w-4" />,
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        label: 'Reply'
      };
    
    case 'owner_comment':
      return {
        icon: <MessageCircle className="h-4 w-4" />,
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        label: 'Owner Response'
      };
    
    case 'owner_reply':
      return {
        icon: <Reply className="h-4 w-4" />,
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        label: 'Owner Reply'
      };
    
    case 'like':
      return {
        icon: <ThumbsUp className="h-4 w-4" />,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        label: 'Like'
      };
    
    case 'helpful':
      return {
        icon: <Heart className="h-4 w-4" />,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        label: 'Helpful Vote'
      };
    
    case 'follow':
      return {
        icon: <UserPlus className="h-4 w-4" />,
        color: 'text-cyan-600',
        bgColor: 'bg-cyan-50',
        label: 'New Follower'
      };
    
    case 'mention':
      return {
        icon: <User className="h-4 w-4" />,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        label: 'Mention'
      };
    
    case 'verification':
      return {
        icon: <CheckCircle className="h-4 w-4" />,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        label: 'Verified'
      };
    
    case 'warning':
    case 'alert':
      return {
        icon: <AlertCircle className="h-4 w-4" />,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        label: 'Alert'
      };
    
    case 'view':
      return {
        icon: <Eye className="h-4 w-4" />,
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        label: 'View'
      };
    
    case 'achievement':
      return {
        icon: <Award className="h-4 w-4" />,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        label: 'Achievement'
      };
    
    case 'trending':
      return {
        icon: <TrendingUp className="h-4 w-4" />,
        color: 'text-emerald-600',
        bgColor: 'bg-emerald-50',
        label: 'Trending'
      };
    
    default:
      // Default fallback
      return {
        icon: <Bell className="h-4 w-4" />,
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        label: 'Notification'
      };
  }
}

/**
 * Get a compact notification type icon for the navbar
 */
export function getNotificationIcon(notification: NotificationType): React.ReactNode {
  const typeInfo = getNotificationTypeInfo(notification);
  return (
    <div className={`p-1 rounded-full ${typeInfo.bgColor} ${typeInfo.color}`}>
      {typeInfo.icon}
    </div>
  );
}

/**
 * Get notification type label for accessibility and tooltips
 */
export function getNotificationTypeLabel(notification: NotificationType): string {
  return getNotificationTypeInfo(notification).label;
}