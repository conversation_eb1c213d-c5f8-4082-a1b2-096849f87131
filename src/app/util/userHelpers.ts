// src/app/util/userHelpers.ts
// Utility for user profile URLs and username validation

import { isUsernameRestricted } from "@/app/config/usernameRestrictions";

// Prefer username; fall back to Clerk/DB id
export function profileUrl(user: { userName?: string; id: string }) {
  return `/userprofile/${user.userName ? `@${user.userName}` : user.id}`;
}

// Clerk-specific validation (still needed for Clerk auto-generated usernames)
const CLERK_PREFIX = /^user_/i;

export function isValidUsername(name: string) {
  if (!name) return false;
  if (CLERK_PREFIX.test(name)) return false;
  
  // Use comprehensive username restrictions system
  return !isUsernameRestricted(name);
}
