import { useCallback, useRef, useState } from 'react';

export interface WorkerResizeOptions {
  maxWidth?: number;
  maxHeight?: number;
  maxDimension?: number;
  quality?: number;
  maxFileSize?: number;
}

export interface WorkerResizeResult {
  dataUrl: string;
  resized: boolean;
}

interface PendingRequest {
  resolve: (result: WorkerResizeResult) => void;
  reject: (error: Error) => void;
}

export const useImageWorker = () => {
  const workerRef = useRef<Worker | null>(null);
  const pendingRequests = useRef<Map<string, PendingRequest>>(new Map());
  const [isSupported, setIsSupported] = useState<boolean | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const activeJobs = useRef(0);

  // Initialize worker on first use
  const initWorker = useCallback(() => {
    if (workerRef.current || isSupported === false) return;

    try {
      // Check if Worker and OffscreenCanvas are supported
      if (typeof Worker === 'undefined' || typeof OffscreenCanvas === 'undefined') {
        setIsSupported(false);
        return;
      }

      // Create worker from the worker file
      const workerUrl = new URL('./workers/imageWorker.ts', import.meta.url);
      workerRef.current = new Worker(workerUrl, { type: 'module' });
      
      workerRef.current.onmessage = (event) => {
        const { id, type, dataUrl, resized, error } = event.data;
        const request = pendingRequests.current.get(id);
        
        if (request) {
          pendingRequests.current.delete(id);
          activeJobs.current = Math.max(0, activeJobs.current - 1);
          
          if (activeJobs.current === 0) {
            setIsProcessing(false);
          }
          
          if (type === 'success' && dataUrl) {
            request.resolve({ dataUrl, resized: resized ?? false });
          } else {
            request.reject(new Error(error || 'Worker processing failed'));
          }
        }
      };

      workerRef.current.onerror = (error) => {
        console.error('Worker error:', error);
        setIsSupported(false);
        
        // Reject all pending requests
        pendingRequests.current.forEach(({ reject }) => {
          reject(new Error('Worker error occurred'));
        });
        pendingRequests.current.clear();
        activeJobs.current = 0;
        setIsProcessing(false);
      };

      setIsSupported(true);
    } catch (error) {
      console.error('Failed to create worker:', error);
      setIsSupported(false);
    }
  }, [isSupported]);

  const processImage = useCallback(
    async (file: File, options: WorkerResizeOptions = {}): Promise<WorkerResizeResult> => {
      // Initialize worker if needed
      if (isSupported === null) {
        initWorker();
      }

      // If worker not supported, throw error to trigger fallback
      if (isSupported === false || !workerRef.current) {
        throw new Error('Worker not supported');
      }

      return new Promise<WorkerResizeResult>((resolve, reject) => {
        const id = `${Date.now()}-${Math.random()}`;
        
        // Store the promise handlers
        pendingRequests.current.set(id, { resolve, reject });
        
        // Update processing state
        activeJobs.current += 1;
        setIsProcessing(true);

        // Convert file to ArrayBuffer and send to worker
        const reader = new FileReader();
        reader.onload = () => {
          if (workerRef.current && reader.result instanceof ArrayBuffer) {
            workerRef.current.postMessage({
              id,
              type: 'resize',
              file: reader.result,
              fileName: file.name,
              fileType: file.type,
              options,
            });
          } else {
            pendingRequests.current.delete(id);
            activeJobs.current = Math.max(0, activeJobs.current - 1);
            if (activeJobs.current === 0) {
              setIsProcessing(false);
            }
            reject(new Error('Failed to read file'));
          }
        };
        
        reader.onerror = () => {
          pendingRequests.current.delete(id);
          activeJobs.current = Math.max(0, activeJobs.current - 1);
          if (activeJobs.current === 0) {
            setIsProcessing(false);
          }
          reject(new Error('Failed to read file'));
        };
        
        reader.readAsArrayBuffer(file);
      });
    },
    [isSupported, initWorker]
  );

  // Cleanup worker on unmount
  const cleanup = useCallback(() => {
    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
    }
    pendingRequests.current.clear();
    activeJobs.current = 0;
    setIsProcessing(false);
  }, []);

  return {
    processImage,
    isSupported,
    isProcessing,
    cleanup,
  };
};
