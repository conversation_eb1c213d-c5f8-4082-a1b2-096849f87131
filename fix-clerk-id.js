const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: "postgresql://postgres:<EMAIL>:5432/reviewit?sslmode=require"
    }
  }
});

async function fixClerkId() {
  try {
    // Check current record
    const currentUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: { email: true, clerkUserId: true, role: true }
    });
    
    console.log('Current user record:', currentUser);
    
    if (currentUser) {
      // Update to the correct Clerk ID
      const updatedUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { clerkUserId: 'user_30OF2IfnwZc3zv5TZ0FUpRuHzMS' },
        select: { email: true, clerkUserId: true, role: true }
      });
      
      console.log('Updated user record:', updatedUser);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixClerkId();
