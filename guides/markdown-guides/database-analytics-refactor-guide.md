# Database Analytics Refactor Guide

## Overview

This guide provides a comprehensive plan to refactor the large `databaseAnalytics.ts` file (1503 lines) into smaller, more maintainable modules. The refactor will improve code organization, maintainability, and testability while preserving all existing functionality.

## Current File Analysis

The current `databaseAnalytics.ts` file contains:
- **Lines 1-80**: Imports, Prometheus metrics setup, and data formatting utilities
- **Lines 81-250**: DatabaseAnalytics class with product analytics methods
- **Lines 251-450**: Business analytics and traffic source functions
- **Lines 451-650**: Cache management utilities and key generators
- **Lines 651-950**: Product search and details caching functions
- **Lines 951-1200**: Top reviewers and admin dashboard functions
- **Lines 1201-1503**: Popular, latest, and trending reviews caching

## Refactor Strategy

Break the file into 8 logical modules based on functionality:

### 1. Core Types and Interfaces (`analytics/types.ts`)
### 2. Data Formatters and Utilities (`analytics/formatters.ts`)
### 3. Cache Management (`analytics/cache.ts`)
### 4. Product Analytics (`analytics/product.ts`)
### 5. Business Analytics (`analytics/business.ts`)
### 6. Review Analytics (`analytics/reviews.ts`)
### 7. Admin Analytics (`analytics/admin.ts`)
### 8. Main Export Index (`analytics/index.ts`)

---

## Implementation Steps

### Step 1: Create Directory Structure

Create the analytics directory:
```bash
mkdir -p src/app/util/analytics
```

### Step 2: Create Core Types Module

**File**: `src/app/util/analytics/types.ts`

**Purpose**: Centralize all analytics-related types and interfaces

**Content**:
```typescript
// Re-export types from main Interfaces file
export {
  iAnalyticsPeriod,
  iBusinessAnalytics,
  iTrafficSource,
  iProductAnalytics,
  TopReviewer,
  TopReviewersResponse
} from '../Interfaces';

// Analytics-specific interfaces
export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
}

export interface AnalyticsConfig {
  cacheTtl: number;
  defaultLimit: number;
  maxSearchResults: number;
}
```

**Dependencies**:
- `../Interfaces` (existing file)

### Step 3: Create Data Formatters Module

**File**: `src/app/util/analytics/formatters.ts`

**Purpose**: Data transformation and formatting utilities

**Content**: Move these functions from original file:
- `formatViewsPerDay()`
- `formatTrafficSources()`
- `formatDeviceTypes()`
- `calculateConversionRate()`
- `calculateUniqueVisitorConversionRate()`
- `formatTopProducts()`
- `calculateSourceConversionRate()`

**Imports needed**:
```typescript
import { iAnalyticsPeriod } from './types';
```

**Dependencies**:
- `./types`

### Step 4: Create Cache Management Module

**File**: `src/app/util/analytics/cache.ts`

**Purpose**: Centralized cache operations and key management

**Content**: Move these from original file:
- `generateCacheKey()` function
- `hashQuery()` function
- `CacheKeys` object
- `cacheStats` object
- `trackCacheHit()` function
- `trackCacheMiss()` function
- `getCacheStats()` function
- `getCacheHitRate()` function
- `invalidateProductCache()` function
- `invalidateAggregatedCachesOnReviewChange()` function
- `invalidateSearchCache()` function
- `invalidateAdminCache()` function

**Imports needed**:
```typescript
import { redisService } from '../../lib/redis';
import { CacheStats } from './types';
```

**Dependencies**:
- `../../lib/redis`
- `./types`

### Step 5: Create Product Analytics Module

**File**: `src/app/util/analytics/product.ts`

**Purpose**: Product-specific analytics operations

**Content**: Move these from original file:
- `DatabaseAnalytics` class (entire class)
- `databaseAnalytics` singleton instance
- `getProductSearchFromCache()` function
- `getProductDetailsFromCache()` function

**Imports needed**:
```typescript
import { PrismaClient } from '@prisma/client';
import { StatusCodes } from 'http-status-codes';
import { register, Counter, Histogram } from 'prom-client';
import { redisService } from '../../lib/redis';
import { iProductAnalytics, iTrafficSource } from './types';
import { CacheKeys, trackCacheHit, trackCacheMiss } from './cache';
```

**Dependencies**:
- `@prisma/client`
- `http-status-codes`
- `prom-client`
- `../../lib/redis`
- `./types`
- `./cache`

### Step 6: Create Business Analytics Module

**File**: `src/app/util/analytics/business.ts`

**Purpose**: Business-level analytics and traffic sources

**Content**: Move these from original file:
- `getBusinessAnalyticsFromDB()` function
- `getTrafficSourcesFromDB()` function

**Imports needed**:
```typescript
import { PrismaClient } from '@prisma/client';
import { redisService } from '../../lib/redis';
import { iAnalyticsPeriod, iBusinessAnalytics, iTrafficSource } from './types';
import {
  formatViewsPerDay,
  formatTrafficSources,
  formatDeviceTypes,
  calculateUniqueVisitorConversionRate,
  formatTopProducts
} from './formatters';
```

**Dependencies**:
- `@prisma/client`
- `../../lib/redis`
- `./types`
- `./formatters`

### Step 7: Create Reviews Analytics Module

**File**: `src/app/util/analytics/reviews.ts`

**Purpose**: Review-related analytics and caching

**Content**: Move these from original file:
- `getTopReviewersFromCache()` function
- `getPopularReviewsFromCache()` function
- `getLatestReviewsFromCache()` function
- `getTrendingReviewsFromCache()` function

**Imports needed**:
```typescript
import { PrismaClient } from '@prisma/client';
import { subHours } from 'date-fns';
import { redisService } from '../../lib/redis';
import { TopReviewer } from './types';
import { CacheKeys, trackCacheHit, trackCacheMiss } from './cache';
```

**Dependencies**:
- `@prisma/client`
- `date-fns`
- `../../lib/redis`
- `./types`
- `./cache`

### Step 8: Create Admin Analytics Module

**File**: `src/app/util/analytics/admin.ts`

**Purpose**: Admin dashboard analytics and reporting

**Content**: Move these from original file:
- `getAdminRecentReviewsFromCache()` function
- `getAdminReviewStatsFromCache()` function
- `getAdminDashboardMetricsFromCache()` function
- `getAdminReportsFromCache()` function
- `getAdminReportStatsFromCache()` function

**Imports needed**:
```typescript
import { PrismaClient } from '@prisma/client';
import { redisService } from '../../lib/redis';
import { CacheKeys, trackCacheHit, trackCacheMiss } from './cache';
```

**Dependencies**:
- `@prisma/client`
- `../../lib/redis`
- `./cache`

### Step 9: Create Main Export Index

**File**: `src/app/util/analytics/index.ts`

**Purpose**: Central export point for all analytics functionality

**Content**:
```typescript
// Re-export all types
export * from './types';

// Re-export formatters
export * from './formatters';

// Re-export cache utilities
export * from './cache';

// Re-export product analytics
export * from './product';

// Re-export business analytics
export * from './business';

// Re-export reviews analytics
export * from './reviews';

// Re-export admin analytics
export * from './admin';

// Initialize Prisma client (shared across modules)
import { PrismaClient } from '@prisma/client';
export const prisma = new PrismaClient();
```

**Dependencies**:
- All analytics modules
- `@prisma/client`

### Step 10: Update Original File

**File**: `src/app/util/databaseAnalytics.ts`

**Purpose**: Maintain backward compatibility by re-exporting everything

**Content**:
```typescript
// Backward compatibility - re-export everything from analytics modules
export * from './analytics';

// Maintain existing exports for any direct imports
export { databaseAnalytics } from './analytics/product';
export { getBusinessAnalyticsFromDB, getTrafficSourcesFromDB } from './analytics/business';
export { invalidateAggregatedCachesOnReviewChange } from './analytics/cache';
```

---

## Import Updates Required

### Files that import from `databaseAnalytics.ts`

Search for imports and update them:

```bash
# Find all files importing from databaseAnalytics
grep -r "from.*databaseAnalytics" src/
grep -r "import.*databaseAnalytics" src/
```

### Common Import Patterns to Update

**Before**:
```typescript
import { getBusinessAnalyticsFromDB } from '../util/databaseAnalytics';
```

**After** (if using specific modules):
```typescript
import { getBusinessAnalyticsFromDB } from '../util/analytics/business';
```

**Or** (if maintaining compatibility):
```typescript
import { getBusinessAnalyticsFromDB } from '../util/databaseAnalytics';
// No change needed if using compatibility layer
```

---

## Testing Strategy

### 1. Unit Tests
Create test files for each module:
- `analytics/formatters.test.ts`
- `analytics/cache.test.ts`
- `analytics/product.test.ts`
- `analytics/business.test.ts`
- `analytics/reviews.test.ts`
- `analytics/admin.test.ts`

### 2. Integration Tests
- Test that all existing API endpoints still work
- Verify cache operations function correctly
- Ensure Prometheus metrics are still collected

### 3. Performance Tests
- Compare response times before and after refactor
- Monitor cache hit rates
- Check memory usage

---

## Migration Checklist

### Pre-Migration
- [ ] Create backup of current `databaseAnalytics.ts`
- [ ] Run existing tests to establish baseline
- [ ] Document current API usage patterns

### During Migration
- [ ] Create analytics directory structure
- [ ] Create `types.ts` module
- [ ] Create `formatters.ts` module
- [ ] Create `cache.ts` module
- [ ] Create `product.ts` module
- [ ] Create `business.ts` module
- [ ] Create `reviews.ts` module
- [ ] Create `admin.ts` module
- [ ] Create `index.ts` export file
- [ ] Update original file for compatibility

### Post-Migration
- [ ] Run all tests to ensure functionality
- [ ] Update import statements (if desired)
- [ ] Update documentation
- [ ] Monitor production for any issues
- [ ] Remove original file after confidence period

---

## Benefits of This Refactor

### 1. **Maintainability**
- Smaller, focused files are easier to understand and modify
- Clear separation of concerns
- Reduced cognitive load when working on specific features

### 2. **Testability**
- Individual modules can be tested in isolation
- Easier to mock dependencies
- More focused test suites

### 3. **Reusability**
- Modules can be imported independently
- Formatters can be reused across different analytics functions
- Cache utilities can be used by other parts of the application

### 4. **Performance**
- Tree-shaking can eliminate unused code
- Faster TypeScript compilation
- Better IDE performance

### 5. **Team Development**
- Multiple developers can work on different modules simultaneously
- Reduced merge conflicts
- Clearer code ownership

---

## Risk Mitigation

### 1. **Backward Compatibility**
- Maintain original file as compatibility layer
- Gradual migration of import statements
- No breaking changes to existing APIs

### 2. **Testing**
- Comprehensive test coverage before refactor
- Automated testing during migration
- Staged rollout in production

### 3. **Monitoring**
- Monitor cache performance metrics
- Track API response times
- Watch for any error rate increases

---

## File Reference Summary

### Key Files to Reference During Implementation

1. **`/Users/<USER>/developer/javascript/Review It/review-it-nextjs-v7/src/app/util/Interfaces.tsx`**
   - Contains all TypeScript interfaces used in analytics
   - Reference for `iBusinessAnalytics`, `iTrafficSource`, `iProductAnalytics`, etc.

2. **`/Users/<USER>/developer/javascript/Review It/review-it-nextjs-v7/src/app/lib/redis.ts`**
   - Redis singleton service used throughout analytics
   - Reference for cache operations

3. **`/Users/<USER>/developer/javascript/Review It/review-it-nextjs-v7/src/app/util/databaseAnalytics.ts`**
   - Original file being refactored
   - Source of all functions and logic to be moved

### Import Dependencies to Track

- `@prisma/client` - Database operations
- `http-status-codes` - HTTP status constants
- `date-fns` - Date manipulation utilities
- `prom-client` - Prometheus metrics
- `crypto` - Hash generation for cache keys

---

## Conclusion

This refactor will transform a monolithic 1503-line file into 8 focused modules, each with a clear responsibility. The modular structure will improve code maintainability, testability, and team productivity while preserving all existing functionality through a backward-compatible approach.

The refactor can be implemented incrementally, allowing for thorough testing at each step and minimal risk to production systems.