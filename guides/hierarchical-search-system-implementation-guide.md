# Hierarchical Search System Implementation Guide

## Overview
This guide outlines the implementation of a hierarchical search system for organizing businesses into sectors, sub-sectors, and industries using a collapsible tree structure.

## Requirements Analysis

### Core Features
1. **Three-Level Hierarchy**: Sectors → Sub-sectors → Industries
2. **Collapsible Tree Structure**: Expandable/collapsible nodes for navigation
3. **Business Listings**: Display businesses with ratings at the leaf level
4. **Interactive Popups**: Click businesses to show review details
5. **Mock Data Structure**: Avoid hardcoding production data
6. **Modern Design**: Clean, user-friendly interface
7. **Single Component**: Consolidated implementation for simplicity

### Data Structure Design
```typescript
interface BusinessNode {
  id: string;
  name: string;
  type: 'sector' | 'subsector' | 'industry' | 'business';
  rating?: number;
  reviewCount?: number;
  children?: BusinessNode[];
  isExpanded?: boolean;
}
```

### Example Hierarchy Structure
```
Agriculture
├── Crop Production
│   ├── Grain Farming
│   │   └── Mock Rice Farm (4.5 stars, 127 reviews)
│   └── Vegetable Farming
│       └── Mock Organic Farm (4.3 stars, 89 reviews)
└── Animal Production
    └── Dairy Farming
        └── Mock Dairy Co (4.1 stars, 156 reviews)

Retail Trade
├── Electronics and Appliance Stores
│   └── Computer Store (4.2 stars, 203 reviews)
└── Pet and Pet Supplies Stores
    └── Mock Pet Store (4.2 stars, 145 reviews)

Food Services
└── Mobile Food Services
    └── Street Food Vendor (4.6 stars, 78 reviews)

Professional Services
└── Veterinary Services
    └── Vet Clinic (4.4 stars, 234 reviews)
```

## Implementation Plan

### Phase 1: Component Structure
- [] Create single HierarchicalSearchSystem component
- [] Define TypeScript interfaces
- [] Set up mock data structure
- [] Implement basic tree rendering

### Phase 2: Interactive Features
- [] Add expand/collapse functionality
- [] Implement business popup modal
- [] Add search/filter capabilities
- [] Handle node state management

### Phase 3: Styling & UX
- [] Apply modern, clean design
- [] Add hover effects and animations
- [] Implement responsive design
- [] Add loading states

### Phase 4: Integration Preparation
- [] Create data integration points
- [] Add configuration options
- [] Document API requirements
- [] Prepare for real data migration

## Technical Implementation

### Dependencies Required
- React (existing)
- Lucide React icons (existing)
- Radix UI Collapsible (existing)
- Tailwind CSS (existing)

### File Structure
```
src/app/components/
└── HierarchicalSearchSystem.tsx (new)
```

### Key Features Implemented

#### 1. Tree Structure Navigation
- Collapsible nodes with smooth animations
- Visual hierarchy with indentation
- Expand/collapse all functionality
- Breadcrumb navigation

#### 2. Business Display
- Star ratings with visual indicators
- Review count display
- Business type badges
- Hover effects for interactivity

#### 3. Modal Popup System
- Business details modal
- Mock review data display
- Rating breakdown
- Contact information placeholder

#### 4. Search & Filter
- Real-time search across all levels
- Filter by business type
- Sort by rating or review count
- Clear search functionality

#### 5. Responsive Design
- Mobile-first approach
- Collapsible sidebar on mobile
- Touch-friendly interactions
- Optimized spacing

## Mock Data Examples

### Sample Businesses by Category
```typescript
const mockBusinesses = [
  // Agriculture
  { name: "Mock Rice Farm", rating: 4.5, reviews: 127, category: "Grain Farming" },
  { name: "Mock Organic Farm", rating: 4.3, reviews: 89, category: "Vegetable Farming" },
  { name: "Mock Dairy Co", rating: 4.1, reviews: 156, category: "Dairy Farming" },
  
  // Retail
  { name: "Computer Store", rating: 4.2, reviews: 203, category: "Electronics" },
  { name: "Mock Pet Store", rating: 4.2, reviews: 145, category: "Pet Supplies" },
  
  // Food Services
  { name: "Street Food Vendor", rating: 4.6, reviews: 78, category: "Mobile Food" },
  
  // Professional Services
  { name: "Vet Clinic", rating: 4.4, reviews: 234, category: "Veterinary" }
];
```

## Integration Points for Real Data

### API Endpoints Needed
1. `GET /api/sectors` - Fetch all sectors
2. `GET /api/sectors/{id}/subsectors` - Fetch subsectors
3. `GET /api/industries/{id}/businesses` - Fetch businesses
4. `GET /api/businesses/{id}/reviews` - Fetch business reviews

### Database Schema Considerations
```sql
-- Sectors table
CREATE TABLE sectors (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Subsectors table
CREATE TABLE subsectors (
  id UUID PRIMARY KEY,
  sector_id UUID REFERENCES sectors(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Industries table
CREATE TABLE industries (
  id UUID PRIMARY KEY,
  subsector_id UUID REFERENCES subsectors(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Update products table to include industry_id
ALTER TABLE products ADD COLUMN industry_id UUID REFERENCES industries(id);
```

## Configuration Options

### Customizable Settings
```typescript
interface HierarchicalSearchConfig {
  maxDepth: number; // Default: 3 (sector → subsector → industry)
  showBusinessCount: boolean; // Show count in parentheses
  enableSearch: boolean; // Enable search functionality
  enableSort: boolean; // Enable sorting options
  defaultExpanded: boolean; // Start with nodes expanded
  showRatings: boolean; // Display star ratings
  modalEnabled: boolean; // Enable business detail modals
}
```

## Testing Checklist

### Functionality Tests
- [ ] Tree expansion/collapse works correctly
- [ ] Search filters results appropriately
- [ ] Modal opens with correct business data
- [ ] Responsive design works on all screen sizes
- [ ] Keyboard navigation is accessible
- [ ] Loading states display properly

### Performance Tests
- [ ] Large datasets render efficiently
- [ ] Search is responsive with many items
- [ ] Smooth animations don't cause lag
- [ ] Memory usage is optimized

### Accessibility Tests
- [ ] Screen reader compatibility
- [ ] Keyboard navigation support
- [ ] Color contrast compliance
- [ ] Focus indicators are visible

## Future Enhancements

### Phase 2 Features
1. **Advanced Filtering**
   - Filter by rating range
   - Filter by location
   - Filter by business hours
   - Custom filter combinations

2. **Enhanced Business Cards**
   - Business photos
   - Operating hours
   - Contact information
   - Quick actions (call, directions)

3. **Analytics Integration**
   - Track popular categories
   - Monitor search patterns
   - Business discovery metrics

4. **Personalization**
   - Recently viewed businesses
   - Favorite categories
   - Personalized recommendations

## Maintenance Notes

### Regular Updates Needed
1. **Mock Data Refresh**: Update sample businesses quarterly
2. **Category Review**: Review hierarchy structure annually
3. **Performance Monitoring**: Monitor component performance monthly
4. **User Feedback**: Collect and implement user suggestions

### Migration Path to Real Data
1. Replace mock data with API calls
2. Implement caching for performance
3. Add error handling for API failures
4. Update search to use backend indexing
5. Implement pagination for large datasets

## Success Metrics

### Key Performance Indicators
- User engagement with tree navigation
- Search usage and success rates
- Business discovery through hierarchy
- Modal interaction rates
- Mobile vs desktop usage patterns

### Technical Metrics
- Component render time
- Search response time
- Memory usage optimization
- Bundle size impact

## Conclusion

This hierarchical search system provides a solid foundation for organizing and discovering businesses through a structured, user-friendly interface. The implementation balances functionality with performance while maintaining flexibility for future enhancements and real data integration.

The single-component approach simplifies maintenance while the mock data structure ensures the system can be tested and refined before production data integration.