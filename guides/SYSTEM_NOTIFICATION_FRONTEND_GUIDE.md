# 🎉 How to Create System Notifications from Frontend

Based on the codebase analysis, system notifications are **already implemented and working**! Here's how to use them:

## 1. **API Endpoint for Creating System Notifications**

```http
POST /notifications/system
Content-Type: application/json
```

## 2. **Request Body Structure**

```javascript
// For targeted notifications (specific users)
{
  "id": "unique_notification_id",           // Required: Unique ID for the notification
  "target_user_ids": ["user_123", "user_456"], // Array of specific user IDs
  "title": "Important Update",              // Required: Notification title
  "message": "Your account has been updated", // Required: Notification message
  "cta_url": "https://app.com/settings",    // Optional: Call-to-action URL
  "icon": "info"                           // Optional: Icon hint (info/success/warning/error)
}

// For broadcast notifications (all users)
{
  "id": "broadcast_notification_id",
  "target_user_ids": [],                   // Empty array = broadcast to ALL users
  "title": "System Maintenance",
  "message": "Scheduled maintenance tonight at 2 AM",
  "cta_url": "https://status.app.com",
  "icon": "warning"
}
```

## 3. **Frontend JavaScript Examples**

```javascript
// Create a targeted system notification
const createTargetedNotification = async (userIds, title, message) => {
  const response = await fetch('/notifications/system', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      id: `sys_${Date.now()}`, // Generate unique ID
      target_user_ids: userIds, // Array of specific user IDs
      title: title,
      message: message,
      icon: "info"
    })
  });
  return response.json();
};

// Create a broadcast notification (to all users)
const createBroadcastNotification = async (title, message) => {
  const response = await fetch('/notifications/system', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      id: `broadcast_${Date.now()}`,
      target_user_ids: [], // Empty = broadcast to all
      title: title,
      message: message,
      icon: "warning"
    })
  });
  return response.json();
};

// Usage examples
await createTargetedNotification(
  ["user_123", "user_456"], 
  "Account Update", 
  "Your billing information has been updated"
);

await createBroadcastNotification(
  "System Maintenance", 
  "Scheduled maintenance tonight from 2-4 AM EST"
);
```

## 4. **Real-time Delivery via SSE**

System notifications are automatically delivered in real-time via Server-Sent Events:

```javascript
// Connect to notification stream
const eventSource = new EventSource(`/notifications/stream?user_id=${userId}`);

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  // Handle system notifications
  if (data.type === 'system' && data.event === 'new_notification') {
    const notification = data.notification;
    
    // Display system notification with special styling
    showSystemNotification({
      title: notification.title,
      message: notification.message,
      icon: notification.icon,
      ctaUrl: notification.cta_url
    });
  }
};
```

## 5. **Mark System Notifications as Read**

```javascript
const markSystemNotificationAsRead = async (notificationId) => {
  const response = await fetch(`/notifications/${notificationId}/read?type=system`, {
    method: 'PUT'
  });
  return response.ok;
};
```

## 6. **Common Use Cases**

```javascript
// Bug fix announcements
await createBroadcastNotification(
  "Bug Fix Released", 
  "We've fixed the login issue. Please refresh your browser."
);

// Feature announcements
await createBroadcastNotification(
  "New Feature Available", 
  "Check out our new dashboard redesign!"
);

// Billing notifications
await createTargetedNotification(
  ["user_with_expired_card"], 
  "Payment Required", 
  "Your payment method needs to be updated"
);

// Maintenance alerts
await createBroadcastNotification(
  "Scheduled Maintenance", 
  "System will be down for maintenance tonight 2-4 AM EST"
);
```

## 7. **Response Format**

Successful creation returns:
```json
{
  "id": "sys_123",
  "target_user_ids": ["user_123"],
  "title": "Important Update",
  "message": "Your account has been updated",
  "cta_url": "https://app.com/settings",
  "icon": "info",
  "read": false,
  "created_at": "2024-01-15T10:30:00Z",
  "notification_type": "system"
}
```

## 8. **Testing with cURL**

```bash
# Test targeted notification
curl -X POST http://localhost:3001/notifications/system \
  -H "Content-Type: application/json" \
  -d '{
    "id": "sys_test_123",
    "target_user_ids": ["user_123"],
    "title": "Test Notification",
    "message": "This is a test system notification",
    "icon": "info"
  }'

# Test broadcast notification
curl -X POST http://localhost:3001/notifications/system \
  -H "Content-Type: application/json" \
  -d '{
    "id": "broadcast_test_123",
    "target_user_ids": [],
    "title": "Broadcast Test",
    "message": "This goes to all users",
    "icon": "warning"
  }'

# Mark notification as read
curl -X PUT "http://localhost:3001/notifications/sys_test_123/read?type=system"
```

## 9. **Error Handling**

```javascript
const createSystemNotification = async (data) => {
  try {
    const response = await fetch('/notifications/system', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create notification: ${error}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating system notification:', error);
    throw error;
  }
};
```

## 10. **Required Fields Validation**

- ✅ **id**: Required - Unique identifier for the notification
- ✅ **title**: Required - Notification title
- ✅ **message**: Required - Notification message
- ✅ **target_user_ids**: Required - Array of user IDs (empty array = broadcast)
- ⚪ **cta_url**: Optional - Call-to-action URL
- ⚪ **icon**: Optional - Icon hint (info/success/warning/error)

## ✅ **Current Status**

According to the documentation, system notifications are **95% complete and production-ready**:

- ✅ **POST /notifications/system** - Fully working
- ✅ **Real-time SSE delivery** - Working perfectly  
- ✅ **PUT /notifications/{id}/read** - Working perfectly
- 🔧 **GET /notifications** - Minor database query issue (doesn't affect creation)

**The frontend team can start using system notifications immediately!** The core functionality for creating and broadcasting system notifications is working perfectly.

## 📋 **Integration Checklist**

- [ ] Set up system notification creation function
- [ ] Implement SSE connection for real-time delivery
- [ ] Add UI components for displaying system notifications
- [ ] Implement mark-as-read functionality
- [ ] Add error handling and validation
- [ ] Test with both targeted and broadcast notifications
- [ ] Style system notifications differently from user notifications
- [ ] Add admin panel for creating system notifications

## 🚀 **Next Steps**

1. **Test the endpoints** using the provided cURL commands
2. **Integrate into your frontend** using the JavaScript examples
3. **Set up real-time SSE connection** for instant delivery
4. **Create admin interface** for sending system notifications
5. **Style notifications** to distinguish system vs user notifications