# ReviewIt React Native Mobile App

## Overview

This directory contains the React Native mobile app version of the ReviewIt platform. The mobile app is a simplified "user-only" version that focuses on core review functionality for end users.

## Target Audience

- **Primary Users**: End users who write and read reviews
- **Excluded**: Business owners, admin users, analytics users

## Core Features

### Included Features
- ✅ User authentication and profiles
- ✅ Product browsing and search
- ✅ Writing and reading reviews
- ✅ Basic rating system
- ✅ User profile management
- ✅ Review interactions (helpful votes, etc.)

### Excluded Features
- ❌ Business admin features
- ❌ Owner dashboards
- ❌ Advanced analytics
- ❌ Promotion management
- ❌ Business profile management
- ❌ Admin panel functionality

## Technical Architecture

### Recommended Structure
```
native-app/
├── src/
│   ├── components/          # Mobile-specific UI components
│   ├── screens/            # Screen components
│   ├── navigation/         # React Navigation setup
│   ├── services/           # API integration layer
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Mobile-specific utilities
│   └── types/              # Mobile-specific types
├── shared-packages/        # Shared code from main app
│   ├── api-client/         # Shared API logic
│   ├── types/              # Shared TypeScript interfaces
│   ├── auth/               # Authentication configuration
│   └── business-logic/     # Shared utilities
└── docs/                   # Mobile app documentation
```

## Code Sharing Strategy

### High Reusability (90-100%)
- **Database Schema**: Prisma models are platform-agnostic
- **TypeScript Interfaces**: All interfaces from `src/app/util/Interfaces.tsx`
- **API Client Logic**: HTTP requests and data transformation
- **Business Logic**: Validation, formatting, calculations
- **Constants**: URLs, configuration values

### Medium Reusability (50-70%)
- **Authentication**: Clerk has React Native support, requires different setup
- **State Management**: React Query works on both platforms
- **Notification Logic**: Core logic shareable, platform implementations differ

### Low Reusability (10-30%)
- **UI Components**: Tailwind/shadcn/ui are web-specific
- **Navigation**: Next.js router vs React Navigation
- **Platform Features**: PWA features, middleware, etc.

## Technology Stack

### Core Technologies
- **Framework**: React Native with Expo
- **Language**: TypeScript
- **State Management**: React Query + Jotai (shared with web)
- **Authentication**: Clerk React Native SDK
- **Navigation**: React Navigation
- **Database**: Shared PostgreSQL with Prisma

### Shared Dependencies
- **API Client**: Axios-based client (shared)
- **Data Models**: Prisma schema (shared)
- **Business Logic**: Validation and utility functions (shared)
- **Type Definitions**: TypeScript interfaces (shared)

## Development Phases

### Phase 1: Project Setup
- [ ] Initialize React Native project with Expo
- [ ] Set up TypeScript configuration
- [ ] Configure shared package imports
- [ ] Set up development environment

### Phase 2: Shared Code Integration
- [ ] Extract shared types from main app
- [ ] Create shared API client package
- [ ] Set up shared authentication configuration
- [ ] Extract shared business logic utilities

### Phase 3: Core Mobile Features
- [ ] Implement authentication flow
- [ ] Create product browsing screens
- [ ] Build review reading interface
- [ ] Implement review writing functionality
- [ ] Add user profile management

### Phase 4: Polish & Optimization
- [ ] Implement offline capabilities
- [ ] Add push notifications
- [ ] Optimize performance
- [ ] Add error handling and loading states

## API Integration

### Existing APIs to Integrate
- **Authentication**: `/api/auth/*` endpoints
- **Products**: `/api/products/*` endpoints
- **Reviews**: `/api/reviews/*` endpoints
- **Users**: `/api/users/*` endpoints
- **Notifications**: External notification server

### Shared Configuration
```typescript
// Shared from main app
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
const NOTIFICATION_SERVER_URL = process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_URL;
```

## Authentication Strategy

### Current Web Implementation
- Uses Clerk with Next.js middleware
- Session management via cookies
- Protected routes with server-side validation

### Mobile Adaptation Required
- Migrate to `@clerk/clerk-expo` or `@clerk/clerk-react-native`
- Token-based authentication for API calls
- Secure storage for authentication tokens
- Biometric authentication support

## Key Considerations

### Benefits
- **Consistency**: Shared business logic ensures identical behavior
- **Maintainability**: Single source of truth for data models
- **Development Speed**: Reduced duplication of effort
- **Type Safety**: Shared TypeScript interfaces prevent API mismatches

### Challenges
- **Authentication Migration**: Different Clerk setup for React Native
- **UI Redesign**: Complete mobile-first UI implementation required
- **Platform Differences**: Handling iOS/Android specific requirements
- **Performance**: Mobile-specific optimizations needed

## Getting Started

1. **Prerequisites**
   - Node.js 18+
   - Expo CLI
   - iOS Simulator / Android Emulator
   - Access to main ReviewIt database

2. **Development Setup**
   ```bash
   cd native-app
   npm install
   npx expo start
   ```

3. **Environment Configuration**
   - Copy environment variables from main app
   - Configure mobile-specific settings
   - Set up development certificates

## Documentation

- [API Integration Guide](./docs/api-integration.md)
- [Authentication Setup](./docs/authentication.md)
- [Component Library](./docs/components.md)
- [Development Workflow](./docs/development.md)

## Contributing

Follow the same coding standards as the main ReviewIt application:
- TypeScript strict mode
- Component files under 300-400 lines
- Use `npx tsc --noEmit` for type checking
- Follow existing naming conventions